# ModernAction Deployment Guide

## Overview
This guide provides complete instructions for deploying ModernAction to staging and production environments using AWS CDK, GitHub Actions, and manual deployment methods.

## Table of Contents
- [Prerequisites](#prerequisites)
- [Environment Configuration](#environment-configuration)
- [Staging Deployment](#staging-deployment)
- [Production Deployment](#production-deployment)
- [GitHub Actions CI/CD](#github-actions-cicd)
- [Manual Deployment](#manual-deployment)
- [Verification & Testing](#verification--testing)
- [Troubleshooting](#troubleshooting)
- [Rollback Procedures](#rollback-procedures)

## Prerequisites

### Required Tools
```bash
# AWS CLI v2
aws --version

# AWS CDK
npm install -g aws-cdk
cdk --version

# Node.js 20+
node --version

# Python 3.11+
python --version

# Docker
docker --version

# Git
git --version
```

### Required Access
- ✅ **AWS Account Access**: IAM permissions for ECS, RDS, VPC, Route53, Secrets Manager
- ✅ **GitHub Repository Access**: Push access to trigger CI/CD
- ✅ **Auth0 Dashboard Access**: For custom domain configuration
- ✅ **Google Cloud Console**: For OAuth credential management

### Environment Variables
```bash
# Required for all deployments
export AWS_REGION=us-east-1
export AWS_ACCOUNT_ID=************

# Optional for manual deployments
export GITHUB_TOKEN=[your-github-token]
```

## Environment Configuration

### 🏗️ **Infrastructure Architecture**

```yaml
Staging Environment:
  Domain: staging.modernaction.io
  Auth0 Tenant: dev-vvwd64m28nwqm871.us.auth0.com
  Database: Shared staging RDS instance
  ECS Cluster: modernaction-staging
  
Production Environment:
  Domain: modernaction.io
  Auth0 Tenant: dev-cp0hhr8rujq87exh.us.auth0.com
  Database: Dedicated production RDS instance
  ECS Cluster: modernaction-prod
```

### 🔐 **Secrets Configuration**

#### **Staging Secrets**
```bash
# Check staging secrets
aws secretsmanager get-secret-value --secret-id "Auth0SecretE68A07FE-ffbFX6p8FqXw" --query "SecretString"

# Contains:
{
  "AUTH0_SECRET": "prod-ultra-secure-auth0-secret-32chars-minimum-length-requirement",
  "AUTH0_CLIENT_SECRET": "spa-applications-do-not-use-client-secrets"
}
```

#### **Production Secrets**
```bash
# Check production secrets
aws secretsmanager get-secret-value --secret-id "Auth0Secret-Production" --query "SecretString"

# Contains:
{
  "AUTH0_SECRET": "ultra-secure-production-auth0-secret-with-64-chars-minimum-for-production",
  "AUTH0_CLIENT_SECRET": "production-spa-applications-use-pkce-not-client-secrets"
}
```

## Staging Deployment

### 🚀 **Method 1: GitHub Actions (Recommended)**

#### **Automatic Deployment**
```bash
# 1. Make changes to your code
git add .
git commit -m "Your changes"

# 2. Push to main branch (triggers automatic deployment)
git push origin main

# 3. Monitor deployment
gh run list --limit 1
gh run watch
```

#### **Manual Trigger**
```bash
# Trigger deployment without code changes
gh workflow run "Deploy to AWS" --ref main
```

### 🔧 **Method 2: Manual CDK Deployment**

#### **Quick Staging Deploy**
```bash
# 1. Navigate to infrastructure directory
cd infrastructure

# 2. Install dependencies
pip install -r requirements.txt

# 3. Deploy staging
ENVIRONMENT=staging cdk deploy --require-approval never

# 4. Verify deployment
curl -I https://staging.modernaction.io/api/v1/health
```

#### **Step-by-Step Manual Staging Deploy**
```bash
# 1. Build and push Docker images
cd apps/api
docker build -t modernaction-api-staging:latest .
docker tag modernaction-api-staging:latest ************.dkr.ecr.us-east-1.amazonaws.com/modernaction-api-staging:latest
docker push ************.dkr.ecr.us-east-1.amazonaws.com/modernaction-api-staging:latest

cd ../web
docker build -t modernaction-web-staging:latest \
  --build-arg NEXT_PUBLIC_API_URL=https://staging.modernaction.io/api/v1 \
  --build-arg NEXT_PUBLIC_AUTH0_DOMAIN=dev-vvwd64m28nwqm871.us.auth0.com \
  --build-arg NEXT_PUBLIC_AUTH0_CLIENT_ID=Jtv4r9uS9BSeahx5jp93v0Ur1K7b5ihC .
docker tag modernaction-web-staging:latest ************.dkr.ecr.us-east-1.amazonaws.com/modernaction-web-staging:latest
docker push ************.dkr.ecr.us-east-1.amazonaws.com/modernaction-web-staging:latest

# 2. Deploy infrastructure
cd ../../infrastructure
ENVIRONMENT=staging cdk deploy --require-approval never

# 3. Force service updates (if needed)
aws ecs update-service --cluster modernaction-staging --service modernaction-api-staging --force-new-deployment
aws ecs update-service --cluster modernaction-staging --service modernaction-web-staging --force-new-deployment

# 4. Wait for deployment stability
aws ecs wait services-stable --cluster modernaction-staging --services modernaction-api-staging modernaction-web-staging
```

### 📊 **Staging Verification**
```bash
# 1. Health checks
curl -s https://staging.modernaction.io/api/v1/health | jq .
curl -I https://staging.modernaction.io

# 2. Database connectivity
curl -s https://staging.modernaction.io/api/v1/campaigns | head -20

# 3. Auth0 integration test
# Visit: https://staging.modernaction.io
# Click "Sign In" - should redirect to staging Auth0 tenant

# 4. Service status
aws ecs describe-services --cluster modernaction-staging \
  --services modernaction-api-staging modernaction-web-staging \
  --query "services[].{Name:serviceName,Status:status,Running:runningCount,Desired:desiredCount}"
```

## Production Deployment

### ⚠️ **Pre-Production Checklist**

#### **Required Verifications**
- [ ] **Staging Environment**: Fully tested and verified
- [ ] **Production Secrets**: Created and validated in AWS Secrets Manager
- [ ] **DNS Configuration**: `modernaction.io` pointing to production ALB
- [ ] **SSL Certificates**: Valid for `modernaction.io`
- [ ] **Database Backup**: Recent backup of staging data (if migrating)
- [ ] **Auth0 Production Tenant**: Configured and tested
- [ ] **Google OAuth**: Production credentials configured
- [ ] **Monitoring**: Set up for production environment

#### **Production Safety Checks**
```bash
# 1. Verify production secrets exist
aws secretsmanager describe-secret --secret-id "Auth0Secret-Production"

# 2. Check production Auth0 tenant
auth0 tenants use dev-cp0hhr8rujq87exh.us.auth0.com
auth0 apps show JZYS6kWu4jKPHmajvukycBDgbhCrPNIx

# 3. Verify production database is separate
aws rds describe-db-instances --query "DBInstances[?contains(DBInstanceIdentifier, 'prod')]"
```

### 🏭 **Production Deployment Process**

#### **Method 1: CDK Deployment (Recommended)**
```bash
# 1. Navigate to infrastructure directory
cd infrastructure

# 2. Review production configuration
ENVIRONMENT=prod cdk diff

# 3. Deploy to production (requires approval)
ENVIRONMENT=prod cdk deploy

# 4. Verify deployment
curl -I https://modernaction.io/api/v1/health
```

#### **Method 2: GitHub Actions Production Deploy**
```bash
# 1. Create production deployment workflow (if needed)
# Edit .github/workflows/deploy-production.yml

# 2. Tag for production release
git tag -a v1.0.0 -m "Production release v1.0.0"
git push origin v1.0.0

# 3. Trigger production deployment
gh workflow run "Deploy to Production" --ref v1.0.0
```

#### **Production Deployment Steps**
```bash
# 1. Build production images
cd apps/api
docker build -t modernaction-api-prod:latest .
docker tag modernaction-api-prod:latest ************.dkr.ecr.us-east-1.amazonaws.com/modernaction-api-prod:latest
docker push ************.dkr.ecr.us-east-1.amazonaws.com/modernaction-api-prod:latest

cd ../web
docker build -t modernaction-web-prod:latest \
  --build-arg NEXT_PUBLIC_API_URL=https://modernaction.io/api/v1 \
  --build-arg NEXT_PUBLIC_AUTH0_DOMAIN=dev-cp0hhr8rujq87exh.us.auth0.com \
  --build-arg NEXT_PUBLIC_AUTH0_CLIENT_ID=JZYS6kWu4jKPHmajvukycBDgbhCrPNIx .
docker tag modernaction-web-prod:latest ************.dkr.ecr.us-east-1.amazonaws.com/modernaction-web-prod:latest
docker push ************.dkr.ecr.us-east-1.amazonaws.com/modernaction-web-prod:latest

# 2. Deploy infrastructure
cd ../../infrastructure
ENVIRONMENT=prod cdk deploy --require-approval never

# 3. Verify services
aws ecs describe-services --cluster modernaction-prod \
  --services modernaction-api-prod modernaction-web-prod
```

### 🔒 **Production Security Verification**
```bash
# 1. SSL certificate validation
curl -I https://modernaction.io | grep -i "strict-transport-security"

# 2. Auth0 tenant isolation check
# Verify production uses dev-cp0hhr8rujq87exh.us.auth0.com
curl -s https://modernaction.io | grep -o "auth0\.com/[^\"]*"

# 3. Database isolation verification
# Ensure production database is separate from staging

# 4. Secrets validation
aws secretsmanager get-secret-value --secret-id "Auth0Secret-Production" --query "SecretString" | jq .
```

## GitHub Actions CI/CD

### 📋 **Workflow Configuration**

#### **Current Workflow** (`.github/workflows/deploy.yml`)
```yaml
name: Deploy to AWS
on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

env:
  AWS_REGION: us-east-1
  ECR_REGISTRY: ************.dkr.ecr.us-east-1.amazonaws.com
  ECS_CLUSTER: modernaction-staging
  ENVIRONMENT: staging
```

#### **Workflow Steps**
1. **Test Phase**: Run backend tests (API tests passing: 228/229)
2. **Build Phase**: Build and push Docker images to ECR
3. **Deploy Phase**: Deploy infrastructure with CDK
4. **Service Update**: Update ECS services with new images
5. **Health Check**: Verify deployment success
6. **Notification**: Report deployment status

### 🔄 **Monitoring CI/CD**

#### **Check Workflow Status**
```bash
# List recent runs
gh run list --limit 5

# Watch current deployment
gh run watch

# View detailed logs
gh run view --log
```

#### **Common CI/CD Commands**
```bash
# Re-run failed workflow
gh run rerun [RUN_ID]

# Cancel running workflow
gh run cancel [RUN_ID]

# Download artifacts
gh run download [RUN_ID]
```

## Manual Deployment

### 🛠️ **Emergency Manual Deployment**

#### **When to Use Manual Deployment**
- GitHub Actions is down
- Emergency hotfix required
- Testing CDK changes locally
- Custom deployment requirements

#### **Full Manual Deployment Process**
```bash
# 1. Set up environment
export ENVIRONMENT=staging  # or prod
export AWS_REGION=us-east-1
export ECR_REGISTRY=************.dkr.ecr.us-east-1.amazonaws.com

# 2. Login to AWS ECR
aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin $ECR_REGISTRY

# 3. Build and push API image
cd apps/api
docker build -t modernaction-api-$ENVIRONMENT:latest .
docker tag modernaction-api-$ENVIRONMENT:latest $ECR_REGISTRY/modernaction-api-$ENVIRONMENT:latest
docker push $ECR_REGISTRY/modernaction-api-$ENVIRONMENT:latest

# 4. Build and push Web image
cd ../web
if [ "$ENVIRONMENT" = "staging" ]; then
  AUTH0_DOMAIN="dev-vvwd64m28nwqm871.us.auth0.com"
  CLIENT_ID="Jtv4r9uS9BSeahx5jp93v0Ur1K7b5ihC"
  API_URL="https://staging.modernaction.io/api/v1"
else
  AUTH0_DOMAIN="dev-cp0hhr8rujq87exh.us.auth0.com"
  CLIENT_ID="JZYS6kWu4jKPHmajvukycBDgbhCrPNIx"
  API_URL="https://modernaction.io/api/v1"
fi

docker build -t modernaction-web-$ENVIRONMENT:latest \
  --build-arg NEXT_PUBLIC_API_URL=$API_URL \
  --build-arg NEXT_PUBLIC_AUTH0_DOMAIN=$AUTH0_DOMAIN \
  --build-arg NEXT_PUBLIC_AUTH0_CLIENT_ID=$CLIENT_ID .
docker tag modernaction-web-$ENVIRONMENT:latest $ECR_REGISTRY/modernaction-web-$ENVIRONMENT:latest
docker push $ECR_REGISTRY/modernaction-web-$ENVIRONMENT:latest

# 5. Deploy infrastructure
cd ../../infrastructure
pip install -r requirements.txt
ENVIRONMENT=$ENVIRONMENT cdk deploy --require-approval never

# 6. Update services
aws ecs update-service --cluster modernaction-$ENVIRONMENT --service modernaction-api-$ENVIRONMENT --force-new-deployment
aws ecs update-service --cluster modernaction-$ENVIRONMENT --service modernaction-web-$ENVIRONMENT --force-new-deployment

# 7. Wait for stability
aws ecs wait services-stable --cluster modernaction-$ENVIRONMENT --services modernaction-api-$ENVIRONMENT modernaction-web-$ENVIRONMENT
```

## Verification & Testing

### ✅ **Post-Deployment Verification**

#### **Automated Health Checks**
```bash
#!/bin/bash
# health-check.sh
ENVIRONMENT=${1:-staging}
BASE_URL="https://${ENVIRONMENT}.modernaction.io"
if [ "$ENVIRONMENT" = "prod" ]; then
  BASE_URL="https://modernaction.io"
fi

echo "🔍 Testing $ENVIRONMENT environment at $BASE_URL"

# API Health Check
echo "1. API Health Check..."
API_STATUS=$(curl -s -o /dev/null -w "%{http_code}" $BASE_URL/api/v1/health)
if [ "$API_STATUS" = "200" ]; then
  echo "   ✅ API Health: PASS"
else
  echo "   ❌ API Health: FAIL (HTTP $API_STATUS)"
fi

# Web Application Check
echo "2. Web Application Check..."
WEB_STATUS=$(curl -s -o /dev/null -w "%{http_code}" $BASE_URL/campaigns)
if [ "$WEB_STATUS" = "200" ]; then
  echo "   ✅ Web App: PASS"
else
  echo "   ❌ Web App: FAIL (HTTP $WEB_STATUS)"
fi

# Database Connectivity
echo "3. Database Connectivity..."
DB_STATUS=$(curl -s -o /dev/null -w "%{http_code}" $BASE_URL/api/v1/campaigns)
if [ "$DB_STATUS" = "200" ]; then
  echo "   ✅ Database: PASS"
else
  echo "   ❌ Database: FAIL (HTTP $DB_STATUS)"
fi

# Auth0 Configuration Check
echo "4. Auth0 Configuration..."
if [ "$ENVIRONMENT" = "staging" ]; then
  EXPECTED_TENANT="dev-vvwd64m28nwqm871"
else
  EXPECTED_TENANT="dev-cp0hhr8rujq87exh"
fi

AUTH_CHECK=$(curl -s $BASE_URL | grep -o "$EXPECTED_TENANT" | head -1)
if [ "$AUTH_CHECK" = "$EXPECTED_TENANT" ]; then
  echo "   ✅ Auth0 Tenant: PASS ($EXPECTED_TENANT)"
else
  echo "   ❌ Auth0 Tenant: FAIL (Expected: $EXPECTED_TENANT)"
fi

echo "✅ Health check complete"
```

#### **Manual Testing Checklist**
- [ ] **Homepage loads**: Visit main URL
- [ ] **API endpoints respond**: Test `/api/v1/health`, `/api/v1/campaigns`
- [ ] **Authentication works**: Test Google OAuth login
- [ ] **Database connectivity**: List campaigns/bills works
- [ ] **Static assets load**: Check CSS, JS, images
- [ ] **SSL certificate valid**: No certificate warnings
- [ ] **Auth0 tenant correct**: Right tenant in auth flow

#### **Performance Testing**
```bash
# Load testing with curl
echo "Load testing API..."
for i in {1..10}; do
  curl -s -o /dev/null -w "%{time_total}\n" https://staging.modernaction.io/api/v1/health
done | awk '{sum+=$1; count++} END {print "Average response time:", sum/count, "seconds"}'

# Check ECS service metrics
aws ecs describe-services --cluster modernaction-staging \
  --services modernaction-api-staging modernaction-web-staging \
  --query "services[].{Name:serviceName,CPU:cpuArchitecture,Memory:memoryReservation,Running:runningCount}"
```

## Troubleshooting

### 🐛 **Common Issues**

#### **1. Docker Image Not Found**
```bash
# Symptoms: CannotPullContainerError
# Solution: Check image exists and push latest
aws ecr describe-images --repository-name modernaction-web-staging --query "imageDetails[*].imageTags"

# Fix: Build and push new image
docker build -t modernaction-web-staging:latest apps/web/
docker tag modernaction-web-staging:latest ************.dkr.ecr.us-east-1.amazonaws.com/modernaction-web-staging:latest
docker push ************.dkr.ecr.us-east-1.amazonaws.com/modernaction-web-staging:latest
```

#### **2. Service Won't Start**
```bash
# Check task failures
aws ecs list-tasks --cluster modernaction-staging --service-name modernaction-web-staging --desired-status STOPPED
aws ecs describe-tasks --cluster modernaction-staging --tasks [TASK_ARN]

# Common fixes:
# - Update task definition with correct image
# - Check secrets are available
# - Verify environment variables
```

#### **3. Auth0 Integration Issues**
```bash
# Check Auth0 configuration
curl -s https://staging.modernaction.io | grep -i auth0

# Verify secrets
aws secretsmanager get-secret-value --secret-id "Auth0SecretE68A07FE-ffbFX6p8FqXw"

# Test Auth0 tenant access
auth0 tenants use dev-vvwd64m28nwqm871.us.auth0.com
auth0 apps show Jtv4r9uS9BSeahx5jp93v0Ur1K7b5ihC
```

#### **4. Database Connection Issues**
```bash
# Check RDS status
aws rds describe-db-instances --query "DBInstances[?contains(DBInstanceIdentifier, 'staging')]"

# Test database connectivity from ECS
aws ecs execute-command --cluster modernaction-staging --task [TASK_ARN] --interactive --command "/bin/bash"
# Inside container: python -c "from app.db.database import get_db; print('DB connected')"
```

### 🔧 **Debug Commands**

#### **ECS Service Debugging**
```bash
# Service events
aws ecs describe-services --cluster modernaction-staging --services modernaction-web-staging --query "services[0].events[:5]"

# Task logs
aws logs get-log-events --log-group-name "/aws/ecs/modernaction-web-staging" --log-stream-name [STREAM_NAME] --limit 50

# Service metrics
aws cloudwatch get-metric-statistics --namespace AWS/ECS \
  --metric-name CPUUtilization \
  --dimensions Name=ServiceName,Value=modernaction-web-staging Name=ClusterName,Value=modernaction-staging \
  --start-time $(date -u -d '1 hour ago' +%Y-%m-%dT%H:%M:%S) \
  --end-time $(date -u +%Y-%m-%dT%H:%M:%S) \
  --period 300 --statistics Average
```

## Rollback Procedures

### 🔄 **Emergency Rollback**

#### **1. Service-Level Rollback**
```bash
# Rollback to previous task definition
aws ecs list-task-definitions --family-prefix "ModernActionstagingWebTaskDefinition" --sort DESC --max-items 2
aws ecs update-service --cluster modernaction-staging --service modernaction-web-staging --task-definition [PREVIOUS_TASK_DEF_ARN]
```

#### **2. Image-Level Rollback**
```bash
# List recent images
aws ecr describe-images --repository-name modernaction-web-staging --query "imageDetails[*].{Tags:imageTags,Pushed:imagePushedAt}" --output table

# Retag previous image as latest
aws ecr batch-get-image --repository-name modernaction-web-staging --image-ids imageTag=[PREVIOUS_TAG] --query "images[0].imageManifest" --output text | aws ecr put-image --repository-name modernaction-web-staging --image-tag latest --image-manifest file:///dev/stdin
```

#### **3. Full Infrastructure Rollback**
```bash
# Rollback entire stack to previous version
cd infrastructure
git log --oneline -5  # Find previous commit
git checkout [PREVIOUS_COMMIT]
ENVIRONMENT=staging cdk deploy --require-approval never
git checkout main  # Return to main after rollback
```

### 📋 **Rollback Verification**
```bash
# Verify rollback success
./health-check.sh staging

# Check application version/commit
curl -s https://staging.modernaction.io/api/v1/health | jq .version

# Verify ECS service stability
aws ecs wait services-stable --cluster modernaction-staging --services modernaction-api-staging modernaction-web-staging
```

## Deployment Best Practices

### 🎯 **Pre-Deployment**
- [ ] **Test locally** with Docker Compose
- [ ] **Run all tests** (`npm run test:e2e`, `poetry run pytest`)
- [ ] **Review changes** in staging before production
- [ ] **Backup production data** before major deployments
- [ ] **Notify team** of scheduled deployments

### 🛡️ **Security Considerations**
- [ ] **Secrets rotation** - quarterly Auth0 and database credentials
- [ ] **Access review** - regular IAM and Auth0 permission audits
- [ ] **Vulnerability scanning** - container and dependency scans
- [ ] **SSL certificate monitoring** - automatic renewal verification
- [ ] **Auth0 tenant isolation** - verify no cross-environment access

### 📊 **Monitoring & Observability**
- [ ] **CloudWatch dashboards** for ECS metrics
- [ ] **Auth0 logs monitoring** for authentication issues
- [ ] **Application logs** centralized in CloudWatch
- [ ] **Performance monitoring** with response time alerts
- [ ] **Error tracking** with automated notifications

---

## Quick Reference

### 🚀 **One-Command Deployments**

```bash
# Staging deployment
cd infrastructure && ENVIRONMENT=staging cdk deploy --require-approval never

# Production deployment  
cd infrastructure && ENVIRONMENT=prod cdk deploy

# Health check
curl -I https://staging.modernaction.io/api/v1/health

# Service status
aws ecs describe-services --cluster modernaction-staging --services modernaction-api-staging modernaction-web-staging --query "services[].{Name:serviceName,Status:status,Running:runningCount}"
```

### 📞 **Emergency Contacts**
- **AWS Support**: https://aws.amazon.com/support/
- **Auth0 Support**: https://support.auth0.com/
- **GitHub Support**: https://support.github.com/

---

*Last Updated: 2025-08-21*  
*Version: 2.0*  
*Environment: Production Ready*