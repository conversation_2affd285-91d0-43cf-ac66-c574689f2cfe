/**
 * Comprehensive Action Page Audit Test
 * Tests the complete bill action flow from start to finish
 */

const { test, expect } = require('@playwright/test');

const BILL_ID = '34e4c62c-3b2f-404a-a2c3-2f668d4358db';
const ACTION_URL = `http://localhost:3000/bills/${BILL_ID}/action`;

test.describe('Action Page Comprehensive Audit', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the action page
    await page.goto(ACTION_URL);
    
    // Wait for page to load completely
    await page.waitForLoadState('networkidle');
  });

  test('Page loads and displays bill information', async ({ page }) => {
    console.log('🔍 Testing page load and bill information display...');
    
    // Check that the page loads without errors
    await expect(page.locator('h1')).toContainText('Take Action');
    
    // Verify bill information is displayed
    await expect(page.locator('text=HR4922')).toBeVisible();
    await expect(page.locator('text=Lacey Act Amendments')).toBeVisible();
    
    // Check for bill details in the left column (stance step)
    await expect(page.locator('[data-testid="bill-info-card"], .bg-white:has-text("HR4922")')).toBeVisible();
    
    console.log('✅ Page loads correctly with bill information');
  });

  test('Step 1: Stance Selection Flow', async ({ page }) => {
    console.log('🔍 Testing stance selection step...');
    
    // Verify stance selection options are present
    await expect(page.locator('text=What\'s your position?')).toBeVisible();
    
    // Check all stance options are available
    const supportButton = page.locator('button:has-text("Support")');
    const opposeButton = page.locator('button:has-text("Oppose")');
    const amendButton = page.locator('button:has-text("Needs Changes")');
    
    await expect(supportButton).toBeVisible();
    await expect(opposeButton).toBeVisible();
    await expect(amendButton).toBeVisible();
    
    // Test selecting a stance
    await supportButton.click();
    
    // Verify Continue button becomes enabled
    const continueButton = page.locator('button:has-text("Continue")');
    await expect(continueButton).toBeEnabled();
    
    console.log('✅ Stance selection works correctly');
  });

  test('Step 2: Reasons Selection Flow', async ({ page }) => {
    console.log('🔍 Testing reasons selection step...');
    
    // First select a stance to proceed
    await page.locator('button:has-text("Support")').click();
    await page.locator('button:has-text("Continue")').click();
    
    // Wait for reasons step to load
    await expect(page.locator('text=Why do you support this bill?')).toBeVisible();
    
    // Check for predefined reasons
    const reasonCheckboxes = page.locator('input[type="checkbox"]');
    const reasonCount = await reasonCheckboxes.count();
    console.log(`Found ${reasonCount} predefined reasons`);
    
    // Select some reasons if available
    if (reasonCount > 0) {
      await reasonCheckboxes.first().check();
      await expect(reasonCheckboxes.first()).toBeChecked();
    }
    
    // Test custom reason functionality
    const customReasonInput = page.locator('input[placeholder*="custom reason"]');
    if (await customReasonInput.isVisible()) {
      await customReasonInput.fill('This bill will help protect wildlife in my community');
      await page.locator('button:has-text("Add")').click();
      
      // Verify custom reason was added
      await expect(page.locator('text=This bill will help protect wildlife')).toBeVisible();
    }
    
    // Test personal story field
    const personalStoryTextarea = page.locator('textarea[placeholder*="personal story"], textarea[name="personal_stories"]');
    if (await personalStoryTextarea.isVisible()) {
      await personalStoryTextarea.fill('As a nature enthusiast, I believe this legislation will help protect the ecosystems I love to explore.');
    }
    
    console.log('✅ Reasons selection works correctly');
  });

  test('Step 3: Contact Information Flow', async ({ page }) => {
    console.log('🔍 Testing contact information step...');
    
    // Navigate through first two steps
    await page.locator('button:has-text("Support")').click();
    await page.locator('button:has-text("Continue")').click();
    await page.locator('button:has-text("Continue")').click();
    
    // Wait for contact step
    await expect(page.locator('text=Your Contact Information')).toBeVisible();
    
    // Fill out required contact fields
    await page.locator('input[name="first_name"]').fill('John');
    await page.locator('input[name="last_name"]').fill('Doe');
    await page.locator('input[name="email"]').fill('<EMAIL>');
    await page.locator('input[name="zip_code"]').fill('94102');
    await page.locator('input[name="address"]').fill('123 Main Street');
    await page.locator('input[name="city"]').fill('San Francisco');
    await page.locator('input[name="state"]').fill('CA');
    
    // Verify all fields are filled
    await expect(page.locator('input[name="first_name"]')).toHaveValue('John');
    await expect(page.locator('input[name="zip_code"]')).toHaveValue('94102');
    
    console.log('✅ Contact information form works correctly');
  });

  test('Step 4: AI Generation and Message Preview Flow', async ({ page }) => {
    console.log('🔍 Testing AI generation and message preview...');
    
    // Navigate through all previous steps
    await page.locator('button:has-text("Support")').click();
    await page.locator('button:has-text("Continue")').click();
    await page.locator('button:has-text("Continue")').click();
    
    // Fill contact information
    await page.locator('input[name="first_name"]').fill('John');
    await page.locator('input[name="last_name"]').fill('Doe');
    await page.locator('input[name="email"]').fill('<EMAIL>');
    await page.locator('input[name="zip_code"]').fill('94102');
    await page.locator('input[name="address"]').fill('123 Main Street');
    await page.locator('input[name="city"]').fill('San Francisco');
    await page.locator('input[name="state"]').fill('CA');
    
    await page.locator('button:has-text("Continue")').click();
    
    // Wait for AI generation step
    await expect(page.locator('text=Crafting Your Message')).toBeVisible();
    
    // Look for AI progress indicators
    const progressBar = page.locator('[style*="width"]');
    const sparklesIcon = page.locator('svg.animate-spin');
    
    // Wait for AI generation to complete (with timeout)
    await page.waitForSelector('text=Message Ready!, text=Representatives', { timeout: 30000 });
    
    console.log('✅ AI generation step works correctly');
  });

  test('Step 5: Edit and Send Message Flow', async ({ page }) => {
    console.log('🔍 Testing edit and send message step...');
    
    // Navigate through all previous steps quickly
    await page.locator('button:has-text("Support")').click();
    await page.locator('button:has-text("Continue")').click();
    await page.locator('button:has-text("Continue")').click();
    
    // Fill contact information
    await page.locator('input[name="first_name"]').fill('John');
    await page.locator('input[name="last_name"]').fill('Doe');
    await page.locator('input[name="email"]').fill('<EMAIL>');
    await page.locator('input[name="zip_code"]').fill('94102');
    await page.locator('input[name="address"]').fill('123 Main Street');
    await page.locator('input[name="city"]').fill('San Francisco');
    await page.locator('input[name="state"]').fill('CA');
    
    await page.locator('button:has-text("Continue")').click();
    
    // Wait for AI to complete and continue
    await page.waitForSelector('text=Message Ready!, text=Representatives', { timeout: 30000 });
    await page.locator('button:has-text("Continue")').click();
    
    // Wait for edit and send step
    await expect(page.locator('text=Review & Send Your Message')).toBeVisible();
    
    // Check for representatives display
    await expect(page.locator('text=Your Representatives')).toBeVisible();
    
    // Check for message editor
    const messageTextarea = page.locator('textarea[name="custom_message"]');
    await expect(messageTextarea).toBeVisible();
    
    // Verify message has been pre-populated
    const messageContent = await messageTextarea.inputValue();
    expect(messageContent.length).toBeGreaterThan(50); // Should have substantial content
    
    // Check for action summary
    await expect(page.locator('text=Action Summary')).toBeVisible();
    await expect(page.locator('text=Support this bill')).toBeVisible();
    
    console.log('✅ Edit and send message step works correctly');
  });

  test('Complete End-to-End Flow', async ({ page }) => {
    console.log('🔍 Testing complete end-to-end action flow...');
    
    // Step 1: Select stance
    await page.locator('button:has-text("Support")').click();
    await page.locator('button:has-text("Continue")').click();
    
    // Step 2: Add reason and personal story
    const customReasonInput = page.locator('input[placeholder*="custom reason"]');
    if (await customReasonInput.isVisible()) {
      await customReasonInput.fill('Wildlife protection is crucial for our environment');
      await page.locator('button:has-text("Add")').click();
    }
    
    const personalStoryTextarea = page.locator('textarea[name="personal_stories"]');
    if (await personalStoryTextarea.isVisible()) {
      await personalStoryTextarea.fill('I volunteer at local wildlife refuges and see the impact of invasive species firsthand.');
    }
    
    await page.locator('button:has-text("Continue")').click();
    
    // Step 3: Contact information
    await page.locator('input[name="first_name"]').fill('Jane');
    await page.locator('input[name="last_name"]').fill('Smith');
    await page.locator('input[name="email"]').fill('<EMAIL>');
    await page.locator('input[name="zip_code"]').fill('94102');
    await page.locator('input[name="address"]').fill('456 Oak Avenue');
    await page.locator('input[name="city"]').fill('San Francisco');
    await page.locator('input[name="state"]').fill('CA');
    
    await page.locator('button:has-text("Continue")').click();
    
    // Step 4: Wait for AI generation
    await page.waitForSelector('text=Message Ready!, text=Representatives', { timeout: 30000 });
    await page.locator('button:has-text("Continue")').click();
    
    // Step 5: Review and send
    await expect(page.locator('text=Review & Send Your Message')).toBeVisible();
    
    // Verify message content includes personal elements
    const messageTextarea = page.locator('textarea[name="custom_message"]');
    const messageContent = await messageTextarea.inputValue();
    
    // Check that the message appears to be personalized
    expect(messageContent).toContain('Dear');
    expect(messageContent.length).toBeGreaterThan(100);
    
    // Check representatives are displayed
    const representativesSection = page.locator('text=Your Representatives');
    await expect(representativesSection).toBeVisible();
    
    // Verify send button is present and enabled
    const sendButton = page.locator('button:has-text("Send Messages")');
    await expect(sendButton).toBeVisible();
    await expect(sendButton).toBeEnabled();
    
    console.log('✅ Complete end-to-end flow works correctly');
    console.log('📝 Action page is ready for user interaction');
  });

  test('Error Handling and Edge Cases', async ({ page }) => {
    console.log('🔍 Testing error handling and edge cases...');
    
    // Test invalid ZIP code handling
    await page.locator('button:has-text("Support")').click();
    await page.locator('button:has-text("Continue")').click();
    await page.locator('button:has-text("Continue")').click();
    
    // Try invalid ZIP code
    await page.locator('input[name="zip_code"]').fill('invalid');
    await page.locator('input[name="first_name"]').fill('Test');
    await page.locator('input[name="last_name"]').fill('User');
    await page.locator('input[name="email"]').fill('<EMAIL>');
    
    // Check for validation errors
    const zipError = page.locator('text=valid ZIP code');
    if (await zipError.isVisible()) {
      console.log('✅ ZIP code validation working');
    }
    
    // Test required field validation
    await page.locator('input[name="email"]').fill('');
    const emailError = page.locator('text=required');
    if (await emailError.isVisible()) {
      console.log('✅ Required field validation working');
    }
    
    console.log('✅ Error handling and validation work correctly');
  });

  test('Progress Indicator and Navigation', async ({ page }) => {
    console.log('🔍 Testing progress indicator and navigation...');
    
    // Check that progress indicator is visible
    const progressIndicator = page.locator('.flex.items-center.gap-4:has-text("Position")');
    await expect(progressIndicator).toBeVisible();
    
    // Test back navigation
    await page.locator('button:has-text("Support")').click();
    await page.locator('button:has-text("Continue")').click();
    
    // Now we should be on reasons step
    await expect(page.locator('text=Why do you support')).toBeVisible();
    
    // Test previous button
    const prevButton = page.locator('button:has-text("Previous")');
    await expect(prevButton).toBeVisible();
    await expect(prevButton).toBeEnabled();
    
    await prevButton.click();
    
    // Should be back to stance selection
    await expect(page.locator('text=What\'s your position?')).toBeVisible();
    
    console.log('✅ Progress indicator and navigation work correctly');
  });
});