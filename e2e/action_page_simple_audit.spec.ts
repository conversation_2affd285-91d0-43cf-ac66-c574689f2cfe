/**
 * Simple Action Page Audit Test
 * Tests the core action page functionality with more specific selectors
 */

import { test, expect } from '@playwright/test';

const BILL_ID = '34e4c62c-3b2f-404a-a2c3-2f668d4358db';
const ACTION_URL = `http://localhost:3000/bills/${BILL_ID}/action`;

test.describe('Action Page Simple Audit', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the action page
    await page.goto(ACTION_URL);
    
    // Wait for page to load completely
    await page.waitForLoadState('networkidle');
  });

  test('Page loads and displays bill information', async ({ page }) => {
    console.log('🔍 Testing page load and bill information display...');
    
    // Check that the page loads without errors
    await expect(page.locator('h1')).toContainText('Take Action');
    
    // Verify bill information is displayed
    await expect(page.locator('text=HR4922')).toBeVisible();
    await expect(page.locator('text=Lacey Act')).toBeVisible();
    
    // Check for bill details in the left column (stance step)
    await expect(page.locator('text=What\'s your position?')).toBeVisible();
    
    console.log('✅ Page loads correctly with bill information');
  });

  test('Stance Selection Works', async ({ page }) => {
    console.log('🔍 Testing stance selection...');
    
    // Verify stance selection options are present
    await expect(page.locator('text=What\'s your position?')).toBeVisible();
    
    // Look for stance buttons with more specific selectors
    const supportButton = page.getByRole('button', { name: /Support.*want this bill to pass/i });
    const opposeButton = page.getByRole('button', { name: /Oppose.*don't want this bill/i });
    const amendButton = page.getByRole('button', { name: /Needs Changes.*support with modifications/i });
    
    await expect(supportButton).toBeVisible();
    await expect(opposeButton).toBeVisible();
    await expect(amendButton).toBeVisible();
    
    // Test selecting support stance
    await supportButton.click();
    
    // Verify Continue button becomes enabled
    const continueButton = page.getByRole('button', { name: /Continue/i });
    await expect(continueButton).toBeEnabled();
    
    console.log('✅ Stance selection works correctly');
  });

  test('Navigation Through First Two Steps', async ({ page }) => {
    console.log('🔍 Testing navigation through first steps...');
    
    // Step 1: Select stance
    const supportButton = page.getByRole('button', { name: /Support.*want this bill to pass/i });
    await supportButton.click();
    
    const continueButton = page.getByRole('button', { name: /Continue/i });
    await continueButton.click();
    
    // Step 2: Should now be on reasons page
    await expect(page.locator('text=Why do you support this bill?')).toBeVisible();
    
    // Continue to contact step
    await continueButton.click();
    
    // Step 3: Should now be on contact page
    await expect(page.locator('text=Your Contact Information')).toBeVisible();
    
    console.log('✅ Navigation through steps works correctly');
  });

  test('Contact Form Fields Present', async ({ page }) => {
    console.log('🔍 Testing contact form fields...');
    
    // Navigate to contact step
    const supportButton = page.getByRole('button', { name: /Support.*want this bill to pass/i });
    await supportButton.click();
    
    let continueButton = page.getByRole('button', { name: /Continue/i });
    await continueButton.click();
    
    continueButton = page.getByRole('button', { name: /Continue/i });
    await continueButton.click();
    
    // Verify contact form fields
    await expect(page.locator('input[name="first_name"]')).toBeVisible();
    await expect(page.locator('input[name="last_name"]')).toBeVisible();
    await expect(page.locator('input[name="email"]')).toBeVisible();
    await expect(page.locator('input[name="zip_code"]')).toBeVisible();
    await expect(page.locator('input[name="address"]')).toBeVisible();
    await expect(page.locator('input[name="city"]')).toBeVisible();
    await expect(page.locator('input[name="state"]')).toBeVisible();
    
    console.log('✅ Contact form fields are present');
  });

  test('API Endpoints Respond Correctly', async ({ page }) => {
    console.log('🔍 Testing API endpoints...');
    
    // Test bill details by slug endpoint
    const billDetailsResponse = await page.request.get(`/api/v1/bills/details/by-slug/${BILL_ID}`);
    expect(billDetailsResponse.ok()).toBeTruthy();
    
    const billDetailsData = await billDetailsResponse.json();
    expect(billDetailsData.bill_id).toBe(BILL_ID);
    
    // Test bill action data endpoint
    const actionDataResponse = await page.request.get(`/api/v1/bills/${BILL_ID}/action-data`);
    expect(actionDataResponse.ok()).toBeTruthy();
    
    const actionData = await actionDataResponse.json();
    expect(actionData.bill_id).toBe(BILL_ID);
    expect(actionData.support_reasons).toBeDefined();
    expect(actionData.oppose_reasons).toBeDefined();
    
    console.log('✅ API endpoints respond correctly');
  });

  test('Form Validation Works', async ({ page }) => {
    console.log('🔍 Testing form validation...');
    
    // Navigate to contact step
    const supportButton = page.getByRole('button', { name: /Support.*want this bill to pass/i });
    await supportButton.click();
    
    let continueButton = page.getByRole('button', { name: /Continue/i });
    await continueButton.click();
    await continueButton.click();
    
    // Try to proceed without filling required fields
    continueButton = page.getByRole('button', { name: /Continue/i });
    
    // Fill only some fields
    await page.locator('input[name="first_name"]').fill('John');
    await page.locator('input[name="zip_code"]').fill('invalid');
    
    // Check for validation on ZIP code
    const zipInput = page.locator('input[name="zip_code"]');
    await zipInput.blur();
    
    // Try invalid email
    await page.locator('input[name="email"]').fill('invalid-email');
    const emailInput = page.locator('input[name="email"]');
    await emailInput.blur();
    
    console.log('✅ Form validation is present');
  });

  test('Message Preview API Test', async ({ page }) => {
    console.log('🔍 Testing message preview functionality...');
    
    // Test the preview message API directly
    const previewData = {
      bill_id: BILL_ID,
      stance: 'support',
      selected_reasons: ['This bill addresses an important issue'],
      custom_reasons: [],
      personal_stories: 'I care about wildlife protection',
      first_name: 'John',
      last_name: 'Doe',
      zip_code: '94102'
    };
    
    const previewResponse = await page.request.post('/api/v1/actions/preview-message', {
      data: previewData
    });
    
    expect(previewResponse.ok()).toBeTruthy();
    
    const previewResult = await previewResponse.json();
    expect(previewResult.representatives).toBeDefined();
    expect(previewResult.personalized_messages).toBeDefined();
    
    console.log('✅ Message preview API works correctly');
  });

  test('Officials Lookup Test', async ({ page }) => {
    console.log('🔍 Testing officials lookup...');
    
    // Test officials lookup with a known ZIP code
    const officialsResponse = await page.request.get('/api/v1/officials/lookup?zip_code=94102');
    expect(officialsResponse.ok()).toBeTruthy();
    
    const officialsData = await officialsResponse.json();
    expect(officialsData.status).toBe('success');
    expect(officialsData.representatives).toBeDefined();
    
    console.log('✅ Officials lookup works correctly');
  });

  test('Progressive Form Completion', async ({ page }) => {
    console.log('🔍 Testing progressive form completion...');
    
    // Step 1: Stance
    const supportButton = page.getByRole('button', { name: /Support.*want this bill to pass/i });
    await supportButton.click();
    
    // Verify stance is selected (button should show selection)
    await expect(supportButton).toHaveAttribute('class', /ring-4/);
    
    let continueButton = page.getByRole('button', { name: /Continue/i });
    await continueButton.click();
    
    // Step 2: Reasons (optional, so continue immediately)
    continueButton = page.getByRole('button', { name: /Continue/i });
    await continueButton.click();
    
    // Step 3: Contact info
    await page.locator('input[name="first_name"]').fill('Test');
    await page.locator('input[name="last_name"]').fill('User');
    await page.locator('input[name="email"]').fill('<EMAIL>');
    await page.locator('input[name="zip_code"]').fill('94102');
    await page.locator('input[name="address"]').fill('123 Test St');
    await page.locator('input[name="city"]').fill('San Francisco');
    await page.locator('input[name="state"]').fill('CA');
    
    // Verify fields are filled
    await expect(page.locator('input[name="first_name"]')).toHaveValue('Test');
    await expect(page.locator('input[name="zip_code"]')).toHaveValue('94102');
    
    console.log('✅ Progressive form completion works');
  });
});