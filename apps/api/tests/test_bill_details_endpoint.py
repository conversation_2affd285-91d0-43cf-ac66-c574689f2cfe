import pytest
from fastapi.testclient import TestClient
from app.main import app
from app.db.database import get_db
from app.models.bill import Bill, BillType
from app.models.bill_details import BillDetails
from datetime import datetime


@pytest.mark.integration
@pytest.mark.database
def test_bill_details_by_slug_endpoint(test_client: TestClient, test_db_session):
    """Test the bill details by slug endpoint directly."""
    
    # Create a test bill
    test_bill = Bill(
        title="Test Parents Bill of Rights Act",
        bill_number="HR5",
        bill_type=BillType.HOUSE_BILL,
        session_year=118,
        chamber="house",
        state="federal",
        summary="A test bill for testing purposes",
        full_text="This is the full text of the test bill...",
        sponsor_name="Test Sponsor",
        created_at=datetime.utcnow(),
        updated_at=datetime.utcnow()
    )
    test_db_session.add(test_bill)
    test_db_session.commit()
    test_db_session.refresh(test_bill)
    
    # Create test bill details
    test_details = BillDetails(
        bill_id=test_bill.id,
        seo_slug="hr5-118",
        seo_title="HR5 - Parents Bill of Rights Act",
        seo_meta_description="Test bill for parental rights in education",
        canonical_url="https://modernaction.io/bills/hr5-118",
        hero_summary="TLDR: This is a test bill summary for HR5 Parents Bill of Rights Act",
        hero_summary_citations=[
            {"quote": "test citation", "start_offset": 0, "end_offset": 13}
        ],
        overview={
            "what_does": {
                "content": "Establishes parental rights in education",
                "citations": [{"quote": "parental rights", "start_offset": 100, "end_offset": 115}]
            }
        },
        positions={
            "support_reasons": [
                {
                    "claim": "Increases parental involvement",
                    "justification": "Parents should have more say in their children's education",
                    "citations": []
                }
            ],
            "oppose_reasons": [
                {
                    "claim": "May limit school autonomy", 
                    "justification": "Schools need flexibility to operate effectively",
                    "citations": []
                }
            ],
            "amend_reasons": [
                {
                    "claim": "Clarify implementation guidelines",
                    "justification": "The bill needs clearer implementation guidance",
                    "citations": []
                }
            ]
        },
        message_templates={},
        tags=["education", "parental-rights"],
        other_details=[],
        source_index=[
            {
                "heading": "Section 1",
                "start_offset": 0,
                "end_offset": 100,
                "anchor_id": "sec1",
                "summary": "Introduction"
            }
        ],
        needs_human_review=False,
        created_at=datetime.utcnow(),
        updated_at=datetime.utcnow()
    )
    test_db_session.add(test_details)
    test_db_session.commit()
    
    # Test the endpoint
    response = test_client.get('/api/v1/bills/details/by-slug/hr5-118')
    assert response.status_code == 200, f"Expected 200 but got {response.status_code}: {response.text}"
    
    data = response.json()
    
    # Verify the response structure
    assert data['seo_slug'] == 'hr5-118'
    assert data['hero_summary'].startswith('TLDR')
    assert 'overview' in data
    assert 'what_does' in data['overview']
    assert 'content' in data['overview']['what_does']
    assert data['overview']['what_does']['content'] == "Establishes parental rights in education"
    assert isinstance(data['source_index'], list)
    assert len(data['source_index']) > 0
    
    # Verify citations structure
    assert 'citations' in data['overview']['what_does']
    assert isinstance(data['overview']['what_does']['citations'], list)


@pytest.mark.integration
@pytest.mark.database
def test_bill_details_by_slug_not_found(test_client: TestClient, test_db_session):
    """Test the bill details endpoint returns 404 for non-existent slug."""
    
    response = test_client.get('/api/v1/bills/details/by-slug/nonexistent-slug')
    assert response.status_code == 404
    
    data = response.json()
    assert data['detail'] == "Bill details not found"