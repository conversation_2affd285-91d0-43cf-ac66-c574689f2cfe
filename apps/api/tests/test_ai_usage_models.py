"""
Comprehensive tests for AI Usage tracking models.

Tests cover all model functionality, relationships, constraints, and edge cases
to ensure world-class quality and reliability.
"""

import pytest
from datetime import datetime, timedelta, timezone
from decimal import Decimal
from sqlalchemy.exc import IntegrityError
from sqlalchemy import text

from app.models.ai_usage import AIUsageLog, AIUsageSummary, AIBudgetAlert
from app.models.bill import Bill, BillStatus, BillType


class TestAIUsageLog:
    """Comprehensive tests for AIUsageLog model"""

    def test_create_basic_ai_usage_log(self, test_db_session):
        """Test creating a basic AI usage log entry"""
        log = AIUsageLog(
            id="test-log-001",
            operation_type="bill_analysis",
            model_name="gpt-4-turbo",
            prompt_tokens=1000,
            completion_tokens=500,
            total_tokens=1500,
            prompt_cost=0.01,
            completion_cost=0.015,
            total_cost=0.025
        )
        
        test_db_session.add(log)
        test_db_session.commit()
        
        # Verify log was created with correct values
        saved_log = test_db_session.query(AIUsageLog).filter_by(id="test-log-001").first()
        assert saved_log is not None
        assert saved_log.operation_type == "bill_analysis"
        assert saved_log.model_name == "gpt-4-turbo"
        assert saved_log.provider == "openai"  # Default value
        assert saved_log.prompt_tokens == 1000
        assert saved_log.completion_tokens == 500
        assert saved_log.total_tokens == 1500
        assert saved_log.prompt_cost == 0.01
        assert saved_log.completion_cost == 0.015
        assert saved_log.total_cost == 0.025
        assert saved_log.success is True  # Default value
        assert saved_log.created_at is not None
        assert saved_log.updated_at is None  # Explicitly set to None

    def test_ai_usage_log_with_bill_relationship(self, test_db_session):
        """Test AI usage log with bill relationship"""
        # Create a bill first
        bill = Bill(
            id="test-bill-001",
            title="Test Bill",
            bill_number="HR1234",
            full_text="Test bill content",
            status=BillStatus.INTRODUCED,
            bill_type=BillType.HOUSE_BILL,
            session_year=118,
            chamber="house"
        )
        test_db_session.add(bill)
        test_db_session.commit()
        
        # Create AI usage log linked to bill
        log = AIUsageLog(
            id="test-log-002",
            operation_type="comprehensive_analysis",
            operation_subtype="support_reasons",
            bill_id="test-bill-001",
            model_name="gpt-4-turbo",
            prompt_tokens=2000,
            completion_tokens=800,
            total_tokens=2800,
            total_cost=0.056,
            response_time_ms=1500.5,
            prompt_length=8000,
            response_length=3200,
            user_id="user-123",
            session_id="session-456"
        )
        
        test_db_session.add(log)
        test_db_session.commit()
        
        # Verify relationship works both ways
        saved_log = test_db_session.query(AIUsageLog).filter_by(id="test-log-002").first()
        assert saved_log.bill_id == "test-bill-001"
        assert saved_log.bill.title == "Test Bill"
        
        # Test bill -> ai_usage_logs relationship
        saved_bill = test_db_session.query(Bill).filter_by(id="test-bill-001").first()
        assert len(saved_bill.ai_usage_logs) == 1
        assert saved_bill.ai_usage_logs[0].operation_type == "comprehensive_analysis"

    def test_ai_usage_log_failed_operation(self, test_db_session):
        """Test logging failed AI operations"""
        log = AIUsageLog(
            id="test-log-003",
            operation_type="reason_generation",
            model_name="gpt-3.5-turbo",
            provider="openai",
            prompt_tokens=500,
            completion_tokens=0,  # No completion for failed request
            total_tokens=500,
            prompt_cost=0.0005,
            completion_cost=0.0,
            total_cost=0.0005,
            success=False,
            error_message="Rate limit exceeded",
            response_time_ms=100.0
        )
        
        test_db_session.add(log)
        test_db_session.commit()
        
        saved_log = test_db_session.query(AIUsageLog).filter_by(id="test-log-003").first()
        assert saved_log.success is False
        assert saved_log.error_message == "Rate limit exceeded"
        assert saved_log.completion_tokens == 0
        assert saved_log.completion_cost == 0.0

    def test_ai_usage_log_required_fields(self, test_db_session):
        """Test that required fields are enforced"""
        # Missing operation_type should fail
        with pytest.raises(IntegrityError):
            log = AIUsageLog(
                id="test-log-004",
                model_name="gpt-4-turbo"
            )
            test_db_session.add(log)
            test_db_session.commit()
        
        test_db_session.rollback()
        
        # Missing model_name should fail
        with pytest.raises(IntegrityError):
            log = AIUsageLog(
                id="test-log-005",
                operation_type="bill_analysis"
            )
            test_db_session.add(log)
            test_db_session.commit()

    def test_ai_usage_log_defaults(self, test_db_session):
        """Test default values are applied correctly"""
        log = AIUsageLog(
            id="test-log-006",
            operation_type="summary",
            model_name="gpt-4"
        )
        
        test_db_session.add(log)
        test_db_session.commit()
        
        saved_log = test_db_session.query(AIUsageLog).filter_by(id="test-log-006").first()
        assert saved_log.provider == "openai"
        assert saved_log.prompt_tokens == 0
        assert saved_log.completion_tokens == 0
        assert saved_log.total_tokens == 0
        assert saved_log.prompt_cost == 0.0
        assert saved_log.completion_cost == 0.0
        assert saved_log.total_cost == 0.0
        assert saved_log.success is True


class TestAIUsageSummary:
    """Comprehensive tests for AIUsageSummary model"""

    def test_create_daily_usage_summary(self, test_db_session):
        """Test creating a daily usage summary"""
        summary_date = datetime(2024, 1, 15)
        summary = AIUsageSummary(
            id="summary-daily-001",
            date_bucket=summary_date,
            period_type="daily",
            operation_type="bill_analysis",
            model_name="gpt-4-turbo",
            total_requests=25,
            successful_requests=23,
            failed_requests=2,
            total_prompt_tokens=50000,
            total_completion_tokens=25000,
            total_tokens=75000,
            total_prompt_cost=0.50,
            total_completion_cost=0.75,
            total_cost=1.25,
            avg_response_time_ms=1200.5,
            avg_tokens_per_request=3000.0,
            avg_cost_per_request=0.05,
            bills_processed=10,
            avg_cost_per_bill=0.125
        )
        
        test_db_session.add(summary)
        test_db_session.commit()
        
        saved_summary = test_db_session.query(AIUsageSummary).filter_by(id="summary-daily-001").first()
        assert saved_summary.date_bucket == summary_date
        assert saved_summary.period_type == "daily"
        assert saved_summary.total_requests == 25
        assert saved_summary.successful_requests == 23
        assert saved_summary.failed_requests == 2
        assert saved_summary.total_cost == 1.25
        assert saved_summary.avg_cost_per_bill == 0.125

    def test_create_monthly_aggregate_summary(self, test_db_session):
        """Test creating a monthly aggregate summary"""
        summary = AIUsageSummary(
            id="summary-monthly-001",
            date_bucket=datetime(2024, 1, 1),
            period_type="monthly",
            operation_type="all",  # Aggregate across all operations
            model_name="all",      # Aggregate across all models
            total_requests=1000,
            successful_requests=950,
            failed_requests=50,
            total_tokens=2000000,
            total_cost=50.75,
            bills_processed=200,
            avg_cost_per_bill=0.254
        )
        
        test_db_session.add(summary)
        test_db_session.commit()
        
        saved_summary = test_db_session.query(AIUsageSummary).filter_by(id="summary-monthly-001").first()
        assert saved_summary.period_type == "monthly"
        assert saved_summary.operation_type == "all"
        assert saved_summary.model_name == "all"
        assert saved_summary.total_requests == 1000
        assert saved_summary.total_cost == 50.75

    def test_usage_summary_defaults(self, test_db_session):
        """Test default values for usage summary"""
        summary = AIUsageSummary(
            id="summary-defaults-001",
            date_bucket=datetime(2024, 1, 1)
        )
        
        test_db_session.add(summary)
        test_db_session.commit()
        
        saved_summary = test_db_session.query(AIUsageSummary).filter_by(id="summary-defaults-001").first()
        assert saved_summary.period_type == "daily"
        assert saved_summary.total_requests == 0
        assert saved_summary.successful_requests == 0
        assert saved_summary.failed_requests == 0
        assert saved_summary.total_cost == 0.0
        assert saved_summary.bills_processed == 0


class TestAIBudgetAlert:
    """Comprehensive tests for AIBudgetAlert model"""

    def test_create_daily_budget_alert(self, test_db_session):
        """Test creating a daily budget alert"""
        alert = AIBudgetAlert(
            id="alert-daily-001",
            alert_name="Daily Spending Limit",
            alert_type="daily",
            threshold_amount=10.0,
            threshold_tokens=100000,
            operation_type="bill_analysis",
            model_name="gpt-4-turbo",
            notification_emails=["<EMAIL>", "<EMAIL>"],
            webhook_url="https://api.example.com/alerts"
        )
        
        test_db_session.add(alert)
        test_db_session.commit()
        
        saved_alert = test_db_session.query(AIBudgetAlert).filter_by(id="alert-daily-001").first()
        assert saved_alert.alert_name == "Daily Spending Limit"
        assert saved_alert.alert_type == "daily"
        assert saved_alert.threshold_amount == 10.0
        assert saved_alert.threshold_tokens == 100000
        assert saved_alert.is_active is True
        assert saved_alert.times_triggered == 0
        assert "<EMAIL>" in saved_alert.notification_emails

    def test_trigger_budget_alert(self, test_db_session):
        """Test triggering a budget alert"""
        alert = AIBudgetAlert(
            id="alert-trigger-001",
            alert_name="Per Bill Limit",
            alert_type="per_bill",
            threshold_amount=0.50,
            is_active=True
        )
        
        test_db_session.add(alert)
        test_db_session.commit()
        
        # Simulate triggering the alert
        alert.last_triggered = datetime.now(timezone.utc).replace(tzinfo=None)
        alert.times_triggered += 1
        test_db_session.commit()
        
        saved_alert = test_db_session.query(AIBudgetAlert).filter_by(id="alert-trigger-001").first()
        assert saved_alert.times_triggered == 1
        assert saved_alert.last_triggered is not None

    def test_budget_alert_defaults(self, test_db_session):
        """Test default values for budget alerts"""
        alert = AIBudgetAlert(
            id="alert-defaults-001",
            alert_name="Test Alert",
            alert_type="monthly",
            threshold_amount=100.0
        )
        
        test_db_session.add(alert)
        test_db_session.commit()
        
        saved_alert = test_db_session.query(AIBudgetAlert).filter_by(id="alert-defaults-001").first()
        assert saved_alert.is_active is True
        assert saved_alert.times_triggered == 0
        assert saved_alert.last_triggered is None


class TestAIUsageIntegration:
    """Integration tests for AI usage models working together"""

    def test_complete_ai_usage_workflow(self, test_db_session):
        """Test a complete workflow from individual logs to summaries and alerts"""
        # Create a bill
        bill = Bill(
            id="integration-bill-001",
            title="Integration Test Bill",
            bill_number="HR9999",
            full_text="Test content",
            status=BillStatus.INTRODUCED,
            bill_type=BillType.HOUSE_BILL,
            session_year=118,
            chamber="house"
        )
        test_db_session.add(bill)
        
        # Create multiple AI usage logs
        logs = [
            AIUsageLog(
                id=f"integration-log-{i:03d}",
                operation_type="bill_analysis",
                bill_id="integration-bill-001",
                model_name="gpt-4-turbo",
                prompt_tokens=1000 + i * 100,
                completion_tokens=500 + i * 50,
                total_tokens=1500 + i * 150,
                total_cost=0.025 + i * 0.010  # Increased to ensure total > 0.20
            )
            for i in range(5)
        ]
        
        for log in logs:
            test_db_session.add(log)
        
        # Create a usage summary
        summary = AIUsageSummary(
            id="integration-summary-001",
            date_bucket=datetime(2024, 1, 15),
            operation_type="bill_analysis",
            model_name="gpt-4-turbo",
            total_requests=5,
            successful_requests=5,
            total_tokens=sum(log.total_tokens for log in logs),
            total_cost=sum(log.total_cost for log in logs),
            bills_processed=1
        )
        test_db_session.add(summary)
        
        # Create a budget alert
        alert = AIBudgetAlert(
            id="integration-alert-001",
            alert_name="Integration Test Alert",
            alert_type="daily",
            threshold_amount=0.20,
            operation_type="bill_analysis"
        )
        test_db_session.add(alert)
        
        test_db_session.commit()
        
        # Verify all components work together
        saved_bill = test_db_session.query(Bill).filter_by(id="integration-bill-001").first()
        assert len(saved_bill.ai_usage_logs) == 5
        
        total_cost = sum(log.total_cost for log in saved_bill.ai_usage_logs)
        assert total_cost == summary.total_cost
        
        # Check if alert threshold would be exceeded (with small tolerance for floating point precision)
        assert total_cost >= alert.threshold_amount - 0.001

    def test_ai_usage_model_constraints(self, test_db_session):
        """Test model constraints and data integrity"""
        # Test that costs are properly calculated
        log = AIUsageLog(
            id="constraint-test-001",
            operation_type="test",
            model_name="gpt-4",
            prompt_tokens=1000,
            completion_tokens=500,
            total_tokens=1500,
            prompt_cost=0.01,
            completion_cost=0.015,
            total_cost=0.025  # Should match sum of prompt_cost + completion_cost
        )
        
        test_db_session.add(log)
        test_db_session.commit()
        
        saved_log = test_db_session.query(AIUsageLog).filter_by(id="constraint-test-001").first()
        assert saved_log.total_cost == saved_log.prompt_cost + saved_log.completion_cost
        assert saved_log.total_tokens == saved_log.prompt_tokens + saved_log.completion_tokens


class TestAIUsageEdgeCases:
    """Edge case and boundary testing for AI usage models"""

    def test_zero_cost_operations(self, test_db_session):
        """Test handling of zero-cost operations (e.g., cached responses)"""
        log = AIUsageLog(
            id="zero-cost-001",
            operation_type="cached_response",
            model_name="gpt-4-turbo",
            prompt_tokens=0,
            completion_tokens=0,
            total_tokens=0,
            prompt_cost=0.0,
            completion_cost=0.0,
            total_cost=0.0,
            response_time_ms=5.0  # Very fast cached response
        )

        test_db_session.add(log)
        test_db_session.commit()

        saved_log = test_db_session.query(AIUsageLog).filter_by(id="zero-cost-001").first()
        assert saved_log.total_cost == 0.0
        assert saved_log.total_tokens == 0
        assert saved_log.response_time_ms == 5.0

    def test_high_volume_operations(self, test_db_session):
        """Test handling of high-volume, expensive operations"""
        log = AIUsageLog(
            id="high-volume-001",
            operation_type="comprehensive_analysis",
            model_name="gpt-4-turbo",
            prompt_tokens=100000,  # Very large prompt
            completion_tokens=50000,  # Large completion
            total_tokens=150000,
            prompt_cost=1.0,  # $1.00 for prompt
            completion_cost=1.5,  # $1.50 for completion
            total_cost=2.5,  # $2.50 total
            response_time_ms=30000.0,  # 30 seconds
            prompt_length=400000,  # 400k characters
            response_length=200000   # 200k characters
        )

        test_db_session.add(log)
        test_db_session.commit()

        saved_log = test_db_session.query(AIUsageLog).filter_by(id="high-volume-001").first()
        assert saved_log.total_tokens == 150000
        assert saved_log.total_cost == 2.5
        assert saved_log.response_time_ms == 30000.0

    def test_negative_values_handling(self, test_db_session):
        """Test that negative values are handled appropriately"""
        # Negative values should be allowed for refunds/adjustments
        log = AIUsageLog(
            id="negative-001",
            operation_type="refund_adjustment",
            model_name="gpt-4-turbo",
            prompt_tokens=0,
            completion_tokens=0,
            total_tokens=0,
            prompt_cost=-0.05,  # Refund
            completion_cost=0.0,
            total_cost=-0.05,
            success=True
        )

        test_db_session.add(log)
        test_db_session.commit()

        saved_log = test_db_session.query(AIUsageLog).filter_by(id="negative-001").first()
        assert saved_log.total_cost == -0.05
        assert saved_log.prompt_cost == -0.05

    def test_very_long_strings(self, test_db_session):
        """Test handling of very long string fields"""
        long_error = "A" * 10000  # Very long error message

        log = AIUsageLog(
            id="long-string-001",
            operation_type="test_long_error",
            model_name="gpt-4-turbo",
            success=False,
            error_message=long_error
        )

        test_db_session.add(log)
        test_db_session.commit()

        saved_log = test_db_session.query(AIUsageLog).filter_by(id="long-string-001").first()
        assert len(saved_log.error_message) == 10000
        assert saved_log.error_message == long_error

    def test_unicode_and_special_characters(self, test_db_session):
        """Test handling of unicode and special characters"""
        unicode_operation = "分析法案_🏛️📊"
        unicode_error = "错误信息: API调用失败 💥"

        log = AIUsageLog(
            id="unicode-001",
            operation_type=unicode_operation,
            model_name="gpt-4-turbo",
            success=False,
            error_message=unicode_error
        )

        test_db_session.add(log)
        test_db_session.commit()

        saved_log = test_db_session.query(AIUsageLog).filter_by(id="unicode-001").first()
        assert saved_log.operation_type == unicode_operation
        assert saved_log.error_message == unicode_error

    def test_concurrent_operations(self, test_db_session):
        """Test handling of concurrent operations with same timestamp"""
        timestamp = datetime.now(timezone.utc).replace(tzinfo=None)

        logs = [
            AIUsageLog(
                id=f"concurrent-{i:03d}",
                operation_type="concurrent_test",
                model_name="gpt-4-turbo",
                total_cost=0.01 * i,
                created_at=timestamp
            )
            for i in range(10)
        ]

        for log in logs:
            test_db_session.add(log)

        test_db_session.commit()

        # Verify all logs were saved with same timestamp
        saved_logs = test_db_session.query(AIUsageLog).filter(
            AIUsageLog.operation_type == "concurrent_test"
        ).all()

        assert len(saved_logs) == 10
        for log in saved_logs:
            assert log.created_at == timestamp


class TestAIUsageQueryPerformance:
    """Performance and query optimization tests"""

    def test_bulk_insert_performance(self, test_db_session):
        """Test bulk insertion of AI usage logs"""
        # Create 100 logs for performance testing
        logs = [
            AIUsageLog(
                id=f"bulk-{i:05d}",
                operation_type="bulk_test",
                model_name="gpt-4-turbo",
                prompt_tokens=1000 + i,
                completion_tokens=500 + i,
                total_tokens=1500 + i,
                total_cost=0.025 + (i * 0.001)
            )
            for i in range(100)
        ]

        # Bulk insert
        test_db_session.bulk_save_objects(logs)
        test_db_session.commit()

        # Verify all logs were inserted
        count = test_db_session.query(AIUsageLog).filter(
            AIUsageLog.operation_type == "bulk_test"
        ).count()

        assert count == 100

    def test_aggregation_queries(self, test_db_session):
        """Test aggregation queries for cost analysis"""
        # Create test data
        test_date = datetime(2024, 1, 15)
        logs = [
            AIUsageLog(
                id=f"agg-{i:03d}",
                operation_type="bill_analysis",
                model_name="gpt-4-turbo",
                total_cost=0.1,
                created_at=test_date,
                success=True
            )
            for i in range(10)
        ]

        # Add one failed operation
        logs.append(AIUsageLog(
            id="agg-failed-001",
            operation_type="bill_analysis",
            model_name="gpt-4-turbo",
            total_cost=0.05,
            created_at=test_date,
            success=False
        ))

        for log in logs:
            test_db_session.add(log)
        test_db_session.commit()

        # Test aggregation queries
        from sqlalchemy import func

        # Total cost for the day
        total_cost = test_db_session.query(
            func.sum(AIUsageLog.total_cost)
        ).filter(
            func.date(AIUsageLog.created_at) == test_date.date()
        ).scalar()

        assert abs(total_cost - 1.05) < 0.001  # 10 * 0.1 + 1 * 0.05 (with floating point tolerance)

        # Success rate
        success_count = test_db_session.query(AIUsageLog).filter(
            func.date(AIUsageLog.created_at) == test_date.date(),
            AIUsageLog.success == True
        ).count()

        total_count = test_db_session.query(AIUsageLog).filter(
            func.date(AIUsageLog.created_at) == test_date.date()
        ).count()

        success_rate = success_count / total_count
        assert success_rate == 10/11  # 10 successful out of 11 total

    def test_date_range_queries(self, test_db_session):
        """Test efficient date range queries"""
        base_date = datetime(2024, 1, 1)

        # Create logs across multiple days
        for day in range(7):  # One week of data
            for hour in range(0, 24, 6):  # Every 6 hours
                log = AIUsageLog(
                    id=f"date-{day:02d}-{hour:02d}",
                    operation_type="daily_test",
                    model_name="gpt-4-turbo",
                    total_cost=0.01,
                    created_at=base_date + timedelta(days=day, hours=hour)
                )
                test_db_session.add(log)

        test_db_session.commit()

        # Query for specific date range
        start_date = base_date + timedelta(days=2)
        end_date = base_date + timedelta(days=5)

        logs_in_range = test_db_session.query(AIUsageLog).filter(
            AIUsageLog.created_at >= start_date,
            AIUsageLog.created_at < end_date,
            AIUsageLog.operation_type == "daily_test"
        ).all()

        # Should have 3 days * 4 logs per day = 12 logs
        assert len(logs_in_range) == 12


class TestAIUsageDataIntegrity:
    """Data integrity and validation tests"""

    def test_foreign_key_constraints(self, test_db_session):
        """Test foreign key constraints work properly"""
        # Try to create AI usage log with non-existent bill_id
        log = AIUsageLog(
            id="fk-test-001",
            operation_type="test",
            model_name="gpt-4-turbo",
            bill_id="non-existent-bill"
        )

        test_db_session.add(log)

        # This should fail due to foreign key constraint
        with pytest.raises(IntegrityError):
            test_db_session.commit()

        test_db_session.rollback()

    def test_data_consistency_validation(self, test_db_session):
        """Test data consistency rules"""
        # Create log where total_tokens != prompt_tokens + completion_tokens
        log = AIUsageLog(
            id="consistency-001",
            operation_type="test",
            model_name="gpt-4-turbo",
            prompt_tokens=1000,
            completion_tokens=500,
            total_tokens=2000,  # Should be 1500
            total_cost=0.025
        )

        test_db_session.add(log)
        test_db_session.commit()

        # While the database allows this, we can add application-level validation
        saved_log = test_db_session.query(AIUsageLog).filter_by(id="consistency-001").first()

        # Application-level validation check
        expected_total = saved_log.prompt_tokens + saved_log.completion_tokens
        assert saved_log.total_tokens != expected_total  # This inconsistency exists

        # In a real application, we'd want to add a check constraint or validation

    def test_timestamp_accuracy(self, test_db_session):
        """Test timestamp accuracy and timezone handling"""
        before_creation = datetime.now(timezone.utc).replace(tzinfo=None)

        log = AIUsageLog(
            id="timestamp-001",
            operation_type="timestamp_test",
            model_name="gpt-4-turbo"
        )

        test_db_session.add(log)
        test_db_session.commit()

        after_creation = datetime.now(timezone.utc).replace(tzinfo=None)

        saved_log = test_db_session.query(AIUsageLog).filter_by(id="timestamp-001").first()

        # Verify timestamp is within expected range
        assert before_creation <= saved_log.created_at <= after_creation

        # Verify timezone handling (should be UTC)
        assert saved_log.created_at.tzinfo is None  # Naive datetime in UTC
