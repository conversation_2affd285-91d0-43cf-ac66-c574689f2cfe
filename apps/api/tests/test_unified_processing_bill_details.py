import os
import json
import pytest
from unittest.mock import patch, <PERSON><PERSON>, MagicMock
from fastapi.testclient import Test<PERSON>lient

from app.main import app
from app.db.database import get_db
from app.services.unified_bill_processing_service import UnifiedBillProcessingService


@pytest.mark.integration
@pytest.mark.database
@pytest.mark.skip(reason="Complex integration test with mocking issues - functionality tested in test_bill_details_endpoint.py")
@patch('app.services.unified_bill_processing_service.CongressGovAPI')
@patch('app.services.unified_bill_processing_service.AIService')
@patch('app.services.ai_service.openai.AsyncOpenAI')
def test_unified_processing_persists_bill_details(mock_openai_class, mock_ai_service_class, mock_congress_api_class, test_client: TestClient, test_db_session):
    # Arrange: ensure OpenAI appears enabled to the service (tests use mock keys)
    os.environ['OPENAI_API_KEY'] = os.environ.get('OPENAI_API_KEY', 'test-key')

    # Clean up any existing bill to ensure we go through the create path, not update path
    from app.models.bill import Bill
    from app.models.bill_details import BillDetails
    existing_bill = test_db_session.query(Bill).filter(Bill.bill_number == "HR5", Bill.session_year == 118).first()
    if existing_bill:
        # Delete bill details first (foreign key constraint)
        test_db_session.query(BillDetails).filter(BillDetails.bill_id == existing_bill.id).delete()
        # Delete the bill
        test_db_session.delete(existing_bill)
        test_db_session.commit()

    # Mock AI service analyze_bill_balanced method to return proper structure
    async def mock_analyze_bill_balanced(bill_text, bill_metadata, source_index=None):
        return {
            "success": True,
            "processing_level": "balanced_premium_efficient",
            "summary": {
                "tldr": "TLDR: This is a test bill summary for HR5 Parents Bill of Rights Act",
                "who_affected": "Parents, students, and school districts",
                "why_matters": "This bill affects parental rights in education",
                "budget_impact": "No significant budget impact",
                "key_points": ["Parental rights", "Education oversight"],
                "support_reasons": ["Increases parental involvement"],
                "oppose_reasons": ["May limit school autonomy"],
                "amend_suggestions": ["Clarify implementation guidelines"]
            },
            "extraction": {
                "key_points": ["Parental rights in education"],
                "complete_analysis": [],
                "additional_details": {},
                "user_content": {}
            },
            "details_payload": {
                "hero_summary": "TLDR: This is a test bill summary for HR5 Parents Bill of Rights Act",
                "hero_summary_citations": [
                    {"text": "test citation", "start": 0, "end": 13}
                ],
                "overview": {
                    "what_does": {
                        "content": "Establishes parental rights in education",
                        "citations": [{"text": "parental rights", "start": 100, "end": 115}]
                    }
                },
                "positions": {
                    "support_reasons": ["Increases parental involvement"],
                    "oppose_reasons": ["May limit school autonomy"],
                    "amend_reasons": ["Clarify implementation guidelines"]
                },
                "message_templates": {},
                "tags": ["education", "parental-rights"],
                "other_details": []
            },
            "_metadata": {
                "model": "test_mock",
                "cost": 0.05,
                "quality_validated": True
            }
        }

    # Mock the AI service method
    mock_ai_service = mock_ai_service_class.return_value
    mock_ai_service.analyze_bill_balanced = mock_analyze_bill_balanced

    # Mock Congress.gov API
    mock_congress_api = Mock()
    mock_congress_api.enabled = True
    mock_congress_api.parse_bill_number.return_value = {
        'bill_type': 'hr',
        'number': 5,
        'congress': 118
    }
    mock_congress_api.get_bill_by_number.return_value = {
        'title': 'Parents Bill of Rights Act',
        'number': 'HR5',
        'congress': 118,
        'introducedDate': '2023-01-01',
        'latestAction': {'text': 'Introduced in House'},
        'sponsors': [{'name': 'Test Sponsor'}]
    }
    # Make get_bill_full_text async
    async def mock_get_bill_full_text(*args, **kwargs):
        return "This is the full text of HR5 Parents Bill of Rights Act..."

    mock_congress_api.get_bill_full_text = mock_get_bill_full_text
    mock_congress_api_class.return_value = mock_congress_api

    # Note: AI service is already mocked via analyze_bill_balanced above
    # No additional AI service mocking needed

    # Act: call admin endpoint
    payload = {"bill_number":"HR5","session":"118","environment":"test"}
    res = test_client.post('/api/v1/admin/process-bill-details', json=payload)
    assert res.status_code == 200, res.text
    data = res.json()
    assert data.get('success') is True

    # Assert: details exist and have content
    slug_res = test_client.get('/api/v1/bills/details/by-slug/hr5-118')
    assert slug_res.status_code == 200, slug_res.text
    details = slug_res.json()

    assert details['hero_summary'].startswith('TLDR')
    assert details['overview']['what_does']['content']
    assert isinstance(details['source_index'], list) and len(details['source_index']) > 0

    # Citations should be attached to at least one section by enrichment
    wd_cites = details['overview']['what_does'].get('citations') or []
    # Not guaranteed deterministic, but should be a list
    assert isinstance(wd_cites, list)

