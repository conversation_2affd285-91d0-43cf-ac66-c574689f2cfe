"""add_contact_messages_table

Revision ID: d492c21a2884
Revises: 0d3f6892c11e
Create Date: 2025-08-21 01:11:31.761909

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'd492c21a2884'
down_revision: Union[str, Sequence[str], None] = '0d3f6892c11e'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('contact_messages',
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('email', sa.String(length=255), nullable=False),
    sa.Column('subject', sa.String(length=500), nullable=False),
    sa.Column('category', sa.String(length=100), nullable=False),
    sa.Column('message', sa.Text(), nullable=False),
    sa.Column('user_id', sa.String(), nullable=True),
    sa.Column('status', sa.String(length=50), nullable=False),
    sa.Column('priority', sa.String(length=20), nullable=False),
    sa.Column('assigned_to', sa.String(length=255), nullable=True),
    sa.Column('admin_notes', sa.Text(), nullable=True),
    sa.Column('responded_at', sa.DateTime(), nullable=True),
    sa.Column('response_method', sa.String(length=50), nullable=True),
    sa.Column('phone_number', sa.String(length=20), nullable=True),
    sa.Column('preferred_contact_method', sa.String(length=50), nullable=False),
    sa.Column('ip_address', sa.String(length=45), nullable=True),
    sa.Column('user_agent', sa.String(length=500), nullable=True),
    sa.Column('referrer', sa.String(length=500), nullable=True),
    sa.Column('is_spam', sa.Boolean(), nullable=False),
    sa.Column('spam_score', sa.String(length=10), nullable=True),
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_contact_messages_email'), 'contact_messages', ['email'], unique=False)
    op.create_index(op.f('ix_contact_messages_id'), 'contact_messages', ['id'], unique=False)
    op.create_index(op.f('ix_contact_messages_user_id'), 'contact_messages', ['user_id'], unique=False)
    op.alter_column('action_analytics_daily', 'top_support_reasons',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               nullable=True)
    op.alter_column('action_analytics_daily', 'top_oppose_reasons',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               nullable=True)
    op.alter_column('action_analytics_daily', 'top_amend_reasons',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               nullable=True)
    op.alter_column('action_analytics_daily', 'geographic_distribution',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               nullable=True)
    op.alter_column('action_analytics_realtime', 'trending_reasons',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               nullable=True)
    op.alter_column('action_analytics_realtime', 'state_distribution',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               nullable=True)
    op.drop_index(op.f('idx_ai_usage_logs_bill_id'), table_name='ai_usage_logs')
    op.drop_index(op.f('idx_ai_usage_logs_created_at'), table_name='ai_usage_logs')
    op.drop_index(op.f('idx_ai_usage_logs_operation_type'), table_name='ai_usage_logs')
    op.create_index(op.f('ix_bill_details_id'), 'bill_details', ['id'], unique=False)
    op.alter_column('bill_status_pipeline', 'bill_id',
               existing_type=sa.VARCHAR(length=36),
               type_=sa.String(length=255),
               existing_nullable=False)
    op.alter_column('bill_summary_versions', 'bill_id',
               existing_type=sa.VARCHAR(length=36),
               type_=sa.String(length=255),
               existing_nullable=False)
    op.drop_column('bill_summary_versions', 'simple_summary')
    op.alter_column('bill_values_analysis', 'analysis_reasoning',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               type_=sa.Text(),
               existing_nullable=True)
    op.drop_column('bills', 'simple_summary')
    op.drop_index(op.f('idx_officials_current_term'), table_name='officials')
    op.drop_index(op.f('idx_officials_first_last_name'), table_name='officials')
    op.drop_index(op.f('idx_officials_full_name'), table_name='officials')
    op.drop_index(op.f('idx_officials_govtrack_id'), table_name='officials')
    op.create_index(op.f('ix_officials_full_name'), 'officials', ['full_name'], unique=False)
    op.create_index(op.f('ix_officials_govtrack_id'), 'officials', ['govtrack_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_officials_govtrack_id'), table_name='officials')
    op.drop_index(op.f('ix_officials_full_name'), table_name='officials')
    op.create_index(op.f('idx_officials_govtrack_id'), 'officials', ['govtrack_id'], unique=False)
    op.create_index(op.f('idx_officials_full_name'), 'officials', ['full_name'], unique=False)
    op.create_index(op.f('idx_officials_first_last_name'), 'officials', ['first_name', 'last_name'], unique=False)
    op.create_index(op.f('idx_officials_current_term'), 'officials', ['current_term_start', 'current_term_end'], unique=False)
    op.add_column('bills', sa.Column('simple_summary', sa.TEXT(), autoincrement=False, nullable=True))
    op.alter_column('bill_values_analysis', 'analysis_reasoning',
               existing_type=sa.Text(),
               type_=postgresql.JSONB(astext_type=sa.Text()),
               existing_nullable=True)
    op.add_column('bill_summary_versions', sa.Column('simple_summary', sa.TEXT(), autoincrement=False, nullable=True))
    op.alter_column('bill_summary_versions', 'bill_id',
               existing_type=sa.String(length=255),
               type_=sa.VARCHAR(length=36),
               existing_nullable=False)
    op.alter_column('bill_status_pipeline', 'bill_id',
               existing_type=sa.String(length=255),
               type_=sa.VARCHAR(length=36),
               existing_nullable=False)
    op.drop_index(op.f('ix_bill_details_id'), table_name='bill_details')
    op.create_index(op.f('idx_ai_usage_logs_operation_type'), 'ai_usage_logs', ['operation_type'], unique=False)
    op.create_index(op.f('idx_ai_usage_logs_created_at'), 'ai_usage_logs', ['created_at'], unique=False)
    op.create_index(op.f('idx_ai_usage_logs_bill_id'), 'ai_usage_logs', ['bill_id'], unique=False)
    op.alter_column('action_analytics_realtime', 'state_distribution',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               nullable=False)
    op.alter_column('action_analytics_realtime', 'trending_reasons',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               nullable=False)
    op.alter_column('action_analytics_daily', 'geographic_distribution',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               nullable=False)
    op.alter_column('action_analytics_daily', 'top_amend_reasons',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               nullable=False)
    op.alter_column('action_analytics_daily', 'top_oppose_reasons',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               nullable=False)
    op.alter_column('action_analytics_daily', 'top_support_reasons',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               nullable=False)
    op.drop_index(op.f('ix_contact_messages_user_id'), table_name='contact_messages')
    op.drop_index(op.f('ix_contact_messages_id'), table_name='contact_messages')
    op.drop_index(op.f('ix_contact_messages_email'), table_name='contact_messages')
    op.drop_table('contact_messages')
    # ### end Alembic commands ###
