"""merge_heads_contact_messages

Revision ID: 0d3f6892c11e
Revises: 010_hero_summary_citations, add_ai_usage_tracking
Create Date: 2025-08-21 01:11:12.137024

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '0d3f6892c11e'
down_revision: Union[str, Sequence[str], None] = ('010_hero_summary_citations', 'add_ai_usage_tracking')
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    pass


def downgrade() -> None:
    """Downgrade schema."""
    pass
