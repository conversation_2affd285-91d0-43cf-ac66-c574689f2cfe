# app/schemas/contact_message.py
from pydantic import BaseModel, EmailStr, validator
from typing import Optional
from datetime import datetime

class ContactMessageCreate(BaseModel):
    name: str
    email: EmailStr
    subject: str
    category: str = 'general'
    message: str
    phone_number: Optional[str] = None
    preferred_contact_method: str = 'email'
    
    @validator('name')
    def name_must_not_be_empty(cls, v):
        if not v or not v.strip():
            raise ValueError('Name cannot be empty')
        return v.strip()
    
    @validator('subject')
    def subject_must_not_be_empty(cls, v):
        if not v or not v.strip():
            raise ValueError('Subject cannot be empty')
        return v.strip()
    
    @validator('message')
    def message_must_not_be_empty(cls, v):
        if not v or not v.strip():
            raise ValueError('Message cannot be empty')
        if len(v.strip()) < 10:
            raise ValueError('Message must be at least 10 characters long')
        return v.strip()
    
    @validator('category')
    def category_must_be_valid(cls, v):
        valid_categories = ['general', 'support', 'partnerships', 'press', 'feedback', 'technical', 'other']
        if v not in valid_categories:
            raise ValueError(f'Category must be one of: {", ".join(valid_categories)}')
        return v
    
    @validator('preferred_contact_method')
    def contact_method_must_be_valid(cls, v):
        valid_methods = ['email', 'phone', 'either']
        if v not in valid_methods:
            raise ValueError(f'Contact method must be one of: {", ".join(valid_methods)}')
        return v

class ContactMessageUpdate(BaseModel):
    status: Optional[str] = None
    priority: Optional[str] = None
    assigned_to: Optional[str] = None
    admin_notes: Optional[str] = None
    response_method: Optional[str] = None
    
    @validator('status')
    def status_must_be_valid(cls, v):
        if v is None:
            return v
        valid_statuses = ['unread', 'read', 'responded', 'resolved', 'archived']
        if v not in valid_statuses:
            raise ValueError(f'Status must be one of: {", ".join(valid_statuses)}')
        return v
    
    @validator('priority')
    def priority_must_be_valid(cls, v):
        if v is None:
            return v
        valid_priorities = ['low', 'normal', 'high', 'urgent']
        if v not in valid_priorities:
            raise ValueError(f'Priority must be one of: {", ".join(valid_priorities)}')
        return v
    
    @validator('response_method')
    def response_method_must_be_valid(cls, v):
        if v is None:
            return v
        valid_methods = ['email', 'phone', 'in_person']
        if v not in valid_methods:
            raise ValueError(f'Response method must be one of: {", ".join(valid_methods)}')
        return v

class ContactMessageResponse(BaseModel):
    id: str
    name: str
    email: str
    subject: str
    category: str
    message: str
    status: str
    priority: str
    assigned_to: Optional[str] = None
    admin_notes: Optional[str] = None
    phone_number: Optional[str] = None
    preferred_contact_method: str
    responded_at: Optional[datetime] = None
    response_method: Optional[str] = None
    is_spam: bool = False
    created_at: datetime
    updated_at: datetime
    user_id: Optional[str] = None
    
    # Computed fields
    is_urgent: bool
    short_message: str
    
    class Config:
        from_attributes = True

class ContactMessageListResponse(BaseModel):
    id: str
    name: str
    email: str
    subject: str
    category: str
    short_message: str
    status: str
    priority: str
    is_urgent: bool
    created_at: datetime
    assigned_to: Optional[str] = None
    
    class Config:
        from_attributes = True

class ContactMessageStatsResponse(BaseModel):
    total_messages: int
    unread_count: int
    urgent_count: int
    today_count: int
    this_week_count: int
    by_category: dict
    by_status: dict