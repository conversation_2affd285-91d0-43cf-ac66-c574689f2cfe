# app/api/v1/endpoints/contact_messages.py
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Request
from sqlalchemy.orm import Session
from datetime import datetime, timedelta

from app.core.auth import get_current_user, verify_admin_user
from app.db.database import get_db
from app.models.contact_message import ContactMessage
from app.models.user import User
from app.schemas.contact_message import (
    ContactMessageCreate, ContactMessageUpdate, ContactMessageResponse,
    ContactMessageListResponse, ContactMessageStatsResponse
)
from sqlalchemy import func, text

router = APIRouter()

@router.post("/", response_model=ContactMessageResponse)
async def create_contact_message(
    contact_data: ContactMessageCreate,
    request: Request,
    db: Session = Depends(get_db)
):
    """Create a new contact message."""
    
    # Create the contact message
    contact_message = ContactMessage(
        name=contact_data.name,
        email=contact_data.email,
        subject=contact_data.subject,
        category=contact_data.category,
        message=contact_data.message,
        phone_number=contact_data.phone_number,
        preferred_contact_method=contact_data.preferred_contact_method,
        user_id=None,  # No user authentication required for contact form
        # Add request metadata for analytics/spam prevention
        ip_address=request.client.host if request.client else None,
        user_agent=request.headers.get("User-Agent"),
        referrer=request.headers.get("Referer")
    )
    
    # Basic spam detection based on content
    spam_keywords = ['casino', 'viagra', 'lottery', 'prize', 'winner', 'urgent money']
    message_lower = contact_data.message.lower()
    subject_lower = contact_data.subject.lower()
    
    if any(keyword in message_lower or keyword in subject_lower for keyword in spam_keywords):
        contact_message.is_spam = True
        contact_message.spam_score = "high"
    
    # Set priority based on category and urgency
    if contact_message.is_urgent:
        contact_message.priority = "urgent"
    elif contact_data.category in ['technical', 'support']:
        contact_message.priority = "high"
    
    db.add(contact_message)
    db.commit()
    db.refresh(contact_message)
    
    return contact_message

@router.get("/", response_model=List[ContactMessageListResponse])
async def get_contact_messages(
    skip: int = 0,
    limit: int = 50,
    status_filter: Optional[str] = None,
    category_filter: Optional[str] = None,
    priority_filter: Optional[str] = None,
    search: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(verify_admin_user)
):
    """Get all contact messages (admin only)."""
    
    query = db.query(ContactMessage)
    
    # Apply filters
    if status_filter:
        query = query.filter(ContactMessage.status == status_filter)
    
    if category_filter:
        query = query.filter(ContactMessage.category == category_filter)
    
    if priority_filter:
        query = query.filter(ContactMessage.priority == priority_filter)
    
    if search:
        search_term = f"%{search}%"
        query = query.filter(
            ContactMessage.name.ilike(search_term) |
            ContactMessage.email.ilike(search_term) |
            ContactMessage.subject.ilike(search_term) |
            ContactMessage.message.ilike(search_term)
        )
    
    # Order by priority and creation date
    query = query.order_by(
        ContactMessage.priority.desc(),
        ContactMessage.created_at.desc()
    )
    
    messages = query.offset(skip).limit(limit).all()
    
    return messages

@router.get("/stats", response_model=ContactMessageStatsResponse)
async def get_contact_message_stats(
    db: Session = Depends(get_db),
    current_user: User = Depends(verify_admin_user)
):
    """Get contact message statistics (admin only)."""
    
    # Get current date for time-based stats
    today = datetime.now().date()
    week_ago = today - timedelta(days=7)
    
    # Basic counts
    total_messages = db.query(ContactMessage).count()
    unread_count = db.query(ContactMessage).filter(ContactMessage.status == 'unread').count()
    
    # Urgent messages (both by priority and computed urgency)
    urgent_count = db.query(ContactMessage).filter(
        (ContactMessage.priority == 'urgent') |
        (ContactMessage.category.in_(['technical', 'support']))
    ).count()
    
    # Time-based counts
    today_count = db.query(ContactMessage).filter(
        func.date(ContactMessage.created_at) == today
    ).count()
    
    this_week_count = db.query(ContactMessage).filter(
        func.date(ContactMessage.created_at) >= week_ago
    ).count()
    
    # Category breakdown
    category_stats = db.query(
        ContactMessage.category,
        func.count(ContactMessage.id).label('count')
    ).group_by(ContactMessage.category).all()
    
    by_category = {stat.category: stat.count for stat in category_stats}
    
    # Status breakdown
    status_stats = db.query(
        ContactMessage.status,
        func.count(ContactMessage.id).label('count')
    ).group_by(ContactMessage.status).all()
    
    by_status = {stat.status: stat.count for stat in status_stats}
    
    return ContactMessageStatsResponse(
        total_messages=total_messages,
        unread_count=unread_count,
        urgent_count=urgent_count,
        today_count=today_count,
        this_week_count=this_week_count,
        by_category=by_category,
        by_status=by_status
    )

@router.get("/{message_id}", response_model=ContactMessageResponse)
async def get_contact_message(
    message_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(verify_admin_user)
):
    """Get a specific contact message (admin only)."""
    
    message = db.query(ContactMessage).filter(ContactMessage.id == message_id).first()
    if not message:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Contact message not found"
        )
    
    # Mark as read if it was unread
    if message.status == 'unread':
        message.status = 'read'
        db.commit()
        db.refresh(message)
    
    return message

@router.patch("/{message_id}", response_model=ContactMessageResponse)
async def update_contact_message(
    message_id: str,
    update_data: ContactMessageUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(verify_admin_user)
):
    """Update a contact message (admin only)."""
    
    message = db.query(ContactMessage).filter(ContactMessage.id == message_id).first()
    if not message:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Contact message not found"
        )
    
    # Update fields
    update_dict = update_data.dict(exclude_unset=True)
    for field, value in update_dict.items():
        setattr(message, field, value)
    
    # Set response timestamp if status changed to responded
    if update_data.status == 'responded' and message.responded_at is None:
        message.responded_at = datetime.utcnow()
    
    db.commit()
    db.refresh(message)
    
    return message

@router.delete("/{message_id}")
async def delete_contact_message(
    message_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(verify_admin_user)
):
    """Delete a contact message (admin only)."""
    
    message = db.query(ContactMessage).filter(ContactMessage.id == message_id).first()
    if not message:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Contact message not found"
        )
    
    db.delete(message)
    db.commit()
    
    return {"message": "Contact message deleted successfully"}

@router.post("/{message_id}/mark-as-responded")
async def mark_as_responded(
    message_id: str,
    response_method: str = "email",
    db: Session = Depends(get_db),
    current_user: User = Depends(verify_admin_user)
):
    """Mark a contact message as responded (admin only)."""
    
    message = db.query(ContactMessage).filter(ContactMessage.id == message_id).first()
    if not message:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Contact message not found"
        )
    
    message.status = "responded"
    message.responded_at = datetime.utcnow()
    message.response_method = response_method
    
    db.commit()
    
    return {"message": "Contact message marked as responded"}

@router.post("/bulk-update")
async def bulk_update_messages(
    message_ids: List[str],
    update_data: ContactMessageUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(verify_admin_user)
):
    """Bulk update multiple contact messages (admin only)."""
    
    messages = db.query(ContactMessage).filter(ContactMessage.id.in_(message_ids)).all()
    
    if not messages:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="No contact messages found"
        )
    
    # Update fields
    update_dict = update_data.dict(exclude_unset=True)
    
    for message in messages:
        for field, value in update_dict.items():
            setattr(message, field, value)
        
        # Set response timestamp if status changed to responded
        if update_data.status == 'responded' and message.responded_at is None:
            message.responded_at = datetime.utcnow()
    
    db.commit()
    
    return {
        "message": f"Successfully updated {len(messages)} contact messages",
        "updated_count": len(messages)
    }