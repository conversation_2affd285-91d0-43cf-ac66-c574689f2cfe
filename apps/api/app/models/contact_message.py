# app/models/contact_message.py
from sqlalchemy import Column, String, Text, DateTime, ForeignKey, Boolean
from sqlalchemy.orm import relationship
from app.db.base_class import Base

class ContactMessage(Base):
    __tablename__ = "contact_messages"

    # Contact form fields
    name = Column(String(255), nullable=False)
    email = Column(String(255), nullable=False, index=True)
    subject = Column(String(500), nullable=False)
    category = Column(String(100), nullable=False, default='general')  # general, support, partnerships, press, feedback, other
    message = Column(Text, nullable=False)
    
    # Optional user association (if they're logged in)
    user_id = Column(String, ForeignKey("users.id"), nullable=True, index=True)
    
    # Admin management fields
    status = Column(String(50), default='unread', nullable=False)  # unread, read, responded, resolved, archived
    priority = Column(String(20), default='normal', nullable=False)  # low, normal, high, urgent
    assigned_to = Column(String(255), nullable=True)  # Admin user who's handling this
    admin_notes = Column(Text, nullable=True)  # Internal notes for admins
    
    # Response tracking
    responded_at = Column(DateTime, nullable=True)
    response_method = Column(String(50), nullable=True)  # email, phone, in_person
    
    # Contact information for follow-up
    phone_number = Column(String(20), nullable=True)
    preferred_contact_method = Column(String(50), default='email', nullable=False)  # email, phone, either
    
    # System fields
    ip_address = Column(String(45), nullable=True)  # For spam prevention
    user_agent = Column(String(500), nullable=True)  # For analytics
    referrer = Column(String(500), nullable=True)  # Where they came from
    
    # Spam protection
    is_spam = Column(Boolean, default=False, nullable=False)
    spam_score = Column(String(10), nullable=True)  # If using external spam detection
    
    # Relationships
    user = relationship("User", backref="contact_messages")

    def __repr__(self):
        return f"<ContactMessage(name='{self.name}', email='{self.email}', subject='{self.subject[:50]}...', status='{self.status}')>"

    @property
    def is_urgent(self) -> bool:
        """Check if message should be treated as urgent based on category and content."""
        urgent_categories = ['technical', 'support']
        urgent_keywords = ['urgent', 'emergency', 'critical', 'broken', 'down', 'error', 'bug']
        
        if self.category in urgent_categories:
            return True
            
        message_lower = self.message.lower()
        subject_lower = self.subject.lower()
        
        return any(keyword in message_lower or keyword in subject_lower for keyword in urgent_keywords)

    @property
    def short_message(self) -> str:
        """Return truncated message for admin list view."""
        if len(self.message) <= 100:
            return self.message
        return self.message[:97] + "..."