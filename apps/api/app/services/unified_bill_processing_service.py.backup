# app/services/unified_bill_processing_service.py
"""
Unified Bill Processing Service

This service combines the complete bill processing pipeline:
1. Congress.gov API data fetching
2. AI analysis with OpenAI
3. Values analysis and scoring
4. Database storage

This replaces the fragmented approach and ensures consistent processing
without campaign creation.
"""

import asyncio
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError

from app.services.congress_gov_api import CongressGovAPI
from app.services.ai_service import AIService
from app.services.bill_values_analysis_service import BillValuesAnalysisService
from app.services.comprehensive_bill_processing_service import ComprehensiveBillProcessingService
from app.services.bill_importance_scorer import BillImportanceScorer, ImportanceLevel
from app.services.enhanced_importance_scorer import get_enhanced_importance_scorer, EvidenceEnhancedScore
from app.services.secondary_analysis_service import SecondaryAnalysisService
from app.models.bill import Bill, BillType, BillStatus
from app.schemas.bill import BillCreate
from app.core.config import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()


class UnifiedBillProcessingService:
    """
    Unified service for complete bill processing pipeline.

    Handles the entire flow from Congress.gov API to fully processed bills
    with AI analysis and values scoring, ready for user actions.
    """

    def __init__(self, db: Session):
        self.db = db
        self.congress_api = CongressGovAPI()
        self.ai_service = AIService()
        self.values_service = BillValuesAnalysisService(db)
        self.importance_scorer = BillImportanceScorer()
        self.enhanced_importance_scorer = get_enhanced_importance_scorer()
        # DISABLED: Expensive comprehensive service that burns money
        # self.comprehensive_service = ComprehensiveBillProcessingService(db)
        from app.services.bill_details_service import BillDetailsService
        self.details_service = BillDetailsService(db)
        self.secondary_analysis_service = SecondaryAnalysisService(self.ai_service)

    async def process_bill_by_number(
        self,
        bill_number: str,
        congress_session: int = 118,
        environment: str = "development",
        use_comprehensive: bool = True,
        use_enhanced_analysis: bool = True  # Default to new enriched analysis
    ) -> Dict[str, Any]:
        """
        Process a single bill by its number through the complete pipeline.

        Args:
            bill_number: Bill number like "HR5", "S1234"
            congress_session: Congress session number
            environment: Environment context (development, staging, production)

        Returns:
            Processing results with bill data and status
        """
        logger.info(f"Starting unified processing for {bill_number} in {environment}")
        logger.info(f"🔍 DEBUG: use_enhanced_analysis={use_enhanced_analysis} (type: {type(use_enhanced_analysis)})")
        logger.info(f"🔍 DEBUG: use_comprehensive={use_comprehensive} (type: {type(use_comprehensive)})")

        try:
            # Step 1: Check if bill already exists
            existing_bill = self._check_existing_bill(bill_number, congress_session)
            if existing_bill:
                logger.info(f"Bill {bill_number} already exists, updating if needed")
                return await self._update_existing_bill(existing_bill, environment)

            # Step 2: Fetch bill data from Congress.gov API
            bill_metadata = await self._fetch_bill_metadata(bill_number, congress_session)
            if not bill_metadata:
                return {
                    "success": False,
                    "error": f"Could not fetch metadata for bill {bill_number}",
                    "bill_number": bill_number
                }

            # Step 3: Get full bill text
            full_text = await self._fetch_bill_text(bill_metadata, congress_session)

            # Step 4: Create bill record first (without AI results)
            # Enhanced duplicate prevention: check multiple criteria
            try:
                existing_bill = None
                if bill_metadata:
                    # Try multiple lookup strategies to prevent duplicates
                    bill_number_normalized = f"{bill_metadata.get('type', '').upper()}{bill_metadata.get('number', '')}"
                    cgid = f"{bill_metadata.get('congress_session', 118)}-{bill_metadata.get('type', '').lower()}-{bill_metadata.get('number', '')}"
                    session_year = bill_metadata.get('congress_session', 118)
                    
                    # Check by congress_gov_id (primary)
                    existing_bill = self.db.query(Bill).filter(Bill.congress_gov_id == cgid).first()
                    
                    # If not found, check by bill_number + session_year combination
                    if not existing_bill:
                        existing_bill = self.db.query(Bill).filter(
                            Bill.bill_number == bill_number_normalized,
                            Bill.session_year == session_year
                        ).first()
                    
                    # If still not found, check by title similarity (for edge cases)
                    if not existing_bill and bill_metadata.get('title'):
                        # Look for bills with very similar titles in the same session
                        title = bill_metadata.get('title', '')
                        existing_bill = self.db.query(Bill).filter(
                            Bill.title == title,
                            Bill.session_year == session_year
                        ).first()
                
                if existing_bill:
                    logger.info(f"✅ Found existing bill {existing_bill.bill_number} (ID: {existing_bill.id}) - updating instead of creating duplicate")
                    bill = await self._update_existing_bill(existing_bill, environment)
                    # ensure bill is a Bill instance for subsequent steps
                    bill = self.db.query(Bill).get(bill.get("bill_id")) if isinstance(bill, dict) else bill
                else:
                    # Create bill record with minimal data first
                    logger.info(f"🆕 Creating new bill record for {bill_number_normalized}")
                    bill = await self._create_bill_record(bill_metadata, full_text, {})
            except Exception as e:
                logger.error(f"Error in bill creation/update: {e}")
                # Rollback the session to clear any dirty state
                self.db.rollback()
                try:
                    bill = await self._create_bill_record(bill_metadata, full_text, {})
                except Exception as e2:
                    logger.error(f"Failed to create bill record after rollback: {e2}")
                    self.db.rollback()
                    raise e2

            # Validate bill has valid ID before proceeding
            if not bill or not hasattr(bill, 'id') or bill.id is None:
                error_msg = f"Bill creation failed - no valid bill ID for {bill_number}"
                logger.error(error_msg)
                return {
                    "success": False,
                    "error": error_msg,
                    "bill_number": bill_number,
                    "environment": environment
                }
            
            # CRITICAL FIX: Add bill_id to metadata for AI usage tracking
            bill_metadata["bill_id"] = str(bill.id)

            # Step 4.5: Score bill importance to determine processing level
            importance_score = self.importance_scorer.score_bill(
                title=bill.title or "",
                summary=bill.summary or "",
                bill_number=bill.bill_number or ""
            )
            
            logger.info(f"📊 Bill importance: {importance_score.score}/100 ({importance_score.level.value})")
            logger.info(f"🤖 Auto-process with AI: {importance_score.auto_process}")
            logger.info(f"📝 Reason: {importance_score.reason}")
            
            # Update bill priority score based on importance
            bill.priority_score = importance_score.score
            self.db.commit()

            # Step 5: AI analysis with cost optimization (now that bill exists)
            # Check if comprehensive analysis is enabled (can be disabled for cost savings)
            comprehensive_enabled = settings.AI_ENABLE_COMPREHENSIVE_ANALYSIS

            logger.info(f"🔍 DEBUG: comprehensive_enabled={comprehensive_enabled}, use_enhanced_analysis={use_enhanced_analysis}")
            logger.info(f"🔍 DEBUG: AI_ENABLE_COMPREHENSIVE_ANALYSIS setting={settings.AI_ENABLE_COMPREHENSIVE_ANALYSIS}")

            # Only run AI analysis for important bills (to save costs)
            should_run_ai = importance_score.auto_process or use_enhanced_analysis

            # DEBUG: Log the decision variables
            logger.error(f"🔍 DEBUG AI DECISION:")
            logger.error(f"  importance_score.auto_process: {importance_score.auto_process}")
            logger.error(f"  use_enhanced_analysis: {use_enhanced_analysis}")
            logger.error(f"  should_run_ai: {should_run_ai}")
            logger.error(f"  comprehensive_enabled: {comprehensive_enabled}")
            logger.error(f"  condition 1 (enhanced): {should_run_ai and use_enhanced_analysis}")
            logger.error(f"  condition 2 (cost-opt): {should_run_ai and not comprehensive_enabled}")

            # Determine which analysis path to use
            if should_run_ai and use_enhanced_analysis:
                logger.info("🎯 Using BALANCED analysis (premium user content + efficient background)")
                # Use the new balanced analysis
                cost_optimized_result = await self.ai_service.analyze_bill_balanced(
                    full_text, bill_metadata, source_index=None
                )
                if cost_optimized_result.get("success"):
                    # Convert to expected format
                    ai_results = self._convert_cost_optimized_to_legacy(cost_optimized_result)

                    # CRITICAL FIX: Get Complete Analysis from balanced analysis FIRST
                    complete_analysis_payload = cost_optimized_result.get('details_payload', {})
                    logger.info(f"🎯 Complete Analysis payload: {len(complete_analysis_payload.get('overview', {}).get('complete_analysis', []))} sections")
                    
                    # TIMING FIX: Wait for balanced analysis to actually complete before running Secondary Analysis
                    logger.info(f"🔄 Waiting for balanced analysis to complete before running Secondary Analysis...")
                    secondary_analysis_payload = await self._wait_for_completion_and_run_secondary_analysis(
                        bill, full_text, bill_metadata, cost_optimized_result, complete_analysis_payload
                    )
                    
                    # Merge both payloads - Complete Analysis + Secondary Analysis
                    details_payload = self._merge_analysis_payloads(
                        complete_analysis_payload, secondary_analysis_payload
                    )

                    cost = cost_optimized_result.get('_metadata', {}).get('cost', 0)
                    logger.info(f"💰 Enhanced span-grounded analysis completed: ${cost:.4f} cost")
                else:
                    logger.error("Enhanced analysis reported failure - checking if bill details were actually created...")
                    
                    # CRITICAL FIX: Check if bill details were actually created despite reported failure
                    from app.models.bill_details import BillDetails
                    self.db.expire_all()
                    bill_details = self.db.query(BillDetails).filter(BillDetails.bill_id == bill.id).first()
                    
                    if bill_details and bill_details.overview and bill_details.overview.get('complete_analysis'):
                        logger.warning(f"🔧 RECOVERY: Bill details exist despite reported failure - running secondary analysis")
                        
                        # Create payloads from existing bill details
                        complete_analysis_payload = {
                            'hero_summary': bill_details.hero_summary,
                            'overview': bill_details.overview,
                            'positions': bill_details.positions or {},
                            'tags': bill_details.tags or [],
                            'other_details': bill_details.other_details
                        }
                        
                        # Force run secondary analysis
                        try:
                            secondary_analysis_payload = await self._generate_secondary_analysis_details(
                                full_text, bill_metadata, cost_optimized_result or {}
                            )
                            
                            # Merge and update bill details
                            merged_payload = self._merge_analysis_payloads(
                                complete_analysis_payload, secondary_analysis_payload
                            )
                            
                            # Update bill details with positions
                            if secondary_analysis_payload and secondary_analysis_payload.get('positions'):
                                bill_details.positions = secondary_analysis_payload['positions']
                                self.db.commit()
                                logger.info(f"✅ RECOVERY: Positions saved successfully via recovery mechanism")
                            
                            # Return success despite initial failure report
                            return {
                                "success": True,
                                "error": "Initial analysis reported failure but recovery successful",
                                "bill_id": bill.id,
                                "recovery_mode": True
                            }
                            
                        except Exception as recovery_error:
                            logger.error(f"Recovery secondary analysis failed: {recovery_error}")
                    
                    logger.error("Enhanced analysis failed - NOT falling back to expensive legacy methods")
                    return {
                        "success": False,
                        "error": "Enhanced analysis failed and expensive fallback disabled",
                        "bill_id": bill.id if 'bill' in locals() else None
                    }
            elif should_run_ai and not comprehensive_enabled:
                logger.info("🎯 Using BALANCED analysis (cost-optimized path)")
                # Use the balanced analysis for all paths
                cost_optimized_result = await self.ai_service.analyze_bill_balanced(
                    full_text, bill_metadata, source_index=None
                )
                if cost_optimized_result.get("success"):
                    # Convert to expected format
                    ai_results = self._convert_cost_optimized_to_legacy(cost_optimized_result)

                    # CRITICAL FIX: Apply same merge logic as enhanced path to preserve positions
                    # Get Complete Analysis from balanced analysis FIRST
                    complete_analysis_payload = cost_optimized_result.get('details_payload', {})
                    logger.info(f"🎯 Complete Analysis payload: {len(complete_analysis_payload.get('overview', {}).get('complete_analysis', []))} sections")
                    
                    # TIMING FIX: Wait for balanced analysis to actually complete before running Secondary Analysis
                    logger.info(f"🔄 Waiting for balanced analysis to complete before running Secondary Analysis...")
                    secondary_analysis_payload = await self._wait_for_completion_and_run_secondary_analysis(
                        bill, full_text, bill_metadata, cost_optimized_result, complete_analysis_payload
                    )
                    
                    # Merge both payloads - Complete Analysis + Secondary Analysis
                    details_payload = self._merge_analysis_payloads(
                        complete_analysis_payload, secondary_analysis_payload
                    )

                    logger.info(f"💰 Cost-optimized analysis completed: {cost_optimized_result.get('_metadata', {}).get('cost', 'unknown')} cost")
                else:
                    logger.error("Cost-optimized analysis failed - NOT falling back to expensive legacy")
                    return {
                        "success": False,
                        "error": "Cost-optimized analysis failed and expensive fallback disabled",
                        "bill_id": bill.id if 'bill' in locals() else None
                    }
            else:
                logger.info(f"⚡ Skipping AI analysis - bill importance: {importance_score.level.value} (score: {importance_score.score})")
                logger.info(f"💡 Reason: {importance_score.reason}")
                ai_results = {}
                details_payload = {}

            # Step 6: OLD BILLS GENERATION - DISABLED FOR BILLDETAILS-FIRST FLOW
            # (ONLY mark as processed if we have actual results)
            """
            # OLD BILLS GENERATION - DISABLED FOR BILLDETAILS-FIRST FLOW
            if ai_results and any(ai_results.values()):  # Check that ai_results has actual content
                try:
                    bill.ai_summary = ai_results.get("ai_summary", "")
                    bill.tldr = ai_results.get("tldr", "")
                    bill.support_reasons = ai_results.get("support_reasons", [])
                    bill.oppose_reasons = ai_results.get("oppose_reasons", [])
                    bill.amend_reasons = ai_results.get("amend_reasons", [])
                    bill.message_templates = ai_results.get("message_templates", {})
                    bill.tags = ai_results.get("tags", [])
                    bill.ai_processed_at = datetime.utcnow()  # Only set when we have real results
                    self.db.commit()
                    logger.info(f"Updated bill {bill.id} with AI results")
            """
            logger.info(f"⚠️ OLD Bills generation disabled - will populate after BillDetails completion for {bill.bill_number}")

            # Step 7: Create bill_details using bill_id (not ORM object)
            if details_payload and bill:
                try:
                    # Ensure bill is committed and get bill_id
                    self.db.flush()  # Force INSERT, assign bill.id
                    bill_id = bill.id
                    if not bill_id:
                        raise ValueError(f"Bill {bill.bill_number} has no ID after flush")

                    logger.info(f"Creating bill_details for bill_id {bill_id} ({bill.bill_number})")
                    self.details_service.create_or_update_details_by_id(bill_id, full_text, details_payload)
                    logger.info(f"Successfully created bill_details for bill_id {bill_id}")
                    
                    # NEW: Bills simplification step after BillDetails creation
                    try:
                        logger.info(f"🔄 Starting Bills simplification for {bill.bill_number}")
                        await self._populate_bills_from_bill_details(bill, bill_id)
                        logger.info(f"✅ Bills table populated with simplified content for {bill.bill_number}")
                    except Exception as e:
                        logger.error(f"Bills simplification failed for {bill.bill_number}: {e}")
                        # Don't fail the entire process, but log the error
                except Exception as e:
                    logger.error(f"Failed to create bill_details for bill {getattr(bill, 'bill_number', 'unknown')}: {e}")
                    # Don't fail the entire process if bill_details creation fails
                    # The bill itself was created successfully
                    import traceback
                    logger.error(f"Full traceback: {traceback.format_exc()}")

            # Step 7: Run values analysis
            values_analysis = await self._run_values_analysis(bill)

            # Step 8: Return success result
            return {
                "success": True,
                "bill_id": bill.id,
                "bill_number": bill.bill_number,
                "title": bill.title,
                "environment": environment,
                "processing_steps": {
                    "metadata_fetched": True,
                    "full_text_fetched": bool(full_text),
                    "ai_analysis_completed": bool(ai_results),
                    "values_analysis_completed": bool(values_analysis),
                    "ready_for_users": True
                },
                "ai_summary": ai_results.get("ai_summary", "") if ai_results else "",
                "values_scores": {
                    "democracy_support": values_analysis.democracy_support_score if values_analysis else 0,
                    "human_rights_support": values_analysis.human_rights_support_score if values_analysis else 0,
                    "environmental_support": values_analysis.environmental_support_score if values_analysis else 0,
                    "needs_review": values_analysis.requires_human_review if values_analysis else False
                } if values_analysis else None,
                "message": f"Successfully processed {bill_number} through complete pipeline"
            }

        except Exception as e:
            logger.error(f"Unified processing failed for {bill_number}: {e}")
            return {
                "success": False,
                "error": str(e),
                "bill_number": bill_number,
                "environment": environment
            }

    async def process_recent_bills(
        self,
        limit: int = 5,
        congress_session: int = 118,
        environment: str = "development"
    ) -> Dict[str, Any]:
        """
        Process recent bills from Congress.gov through the complete pipeline.

        Args:
            limit: Maximum number of bills to process
            congress_session: Congress session number
            environment: Environment context

        Returns:
            Batch processing results
        """
        logger.info(f"Processing {limit} recent bills in {environment}")

        try:
            # Fetch recent bills from Congress.gov
            recent_bills = self.congress_api.get_recent_bills(
                congress=congress_session,
                limit=limit
            )

            if not recent_bills:
                return {
                    "success": False,
                    "error": "No recent bills found",
                    "environment": environment
                }

            # Process each bill
            results = {
                "success": True,
                "environment": environment,
                "total_bills": len(recent_bills),
                "processed_bills": [],
                "failed_bills": [],
                "skipped_bills": [],
                "errors": []
            }

            for bill_data in recent_bills:
                try:
                    bill_number = bill_data.get("number", "")
                    if not bill_number:
                        continue

                    # Check if already exists
                    existing = self._check_existing_bill(bill_number, congress_session)
                    if existing:
                        results["skipped_bills"].append(bill_number)
                        continue

                    # Process the bill
                    result = await self._process_bill_from_data(bill_data, environment)

                    if result["success"]:
                        results["processed_bills"].append({
                            "bill_number": bill_number,
                            "bill_id": result["bill_id"],
                            "title": result["title"]
                        })
                    else:
                        results["failed_bills"].append(bill_number)
                        results["errors"].append(f"{bill_number}: {result['error']}")

                except Exception as e:
                    logger.error(f"Error processing bill from batch: {e}")
                    results["errors"].append(f"Batch processing error: {str(e)}")

            results["message"] = f"Processed {len(results['processed_bills'])} bills successfully"
            return results

        except Exception as e:
            logger.error(f"Batch processing failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "environment": environment
            }

    async def _fetch_bill_metadata(self, bill_number: str, congress_session: int) -> Optional[Dict[str, Any]]:
        """Fetch bill metadata from Congress.gov API"""
        try:
            parsed = self.congress_api.parse_bill_number(bill_number)
            parsed['congress'] = congress_session

            bill_data = self.congress_api.get_bill_by_number(
                congress=parsed['congress'],
                bill_type=parsed['bill_type'],
                bill_number=parsed['number']
            )

            if bill_data:
                return {
                    **bill_data,
                    'parsed_number': parsed,
                    'congress_session': congress_session
                }
            return None

        except Exception as e:
            logger.error(f"Error fetching bill metadata: {e}")
            return None

    async def _fetch_bill_text(self, bill_metadata: Dict[str, Any], congress_session: int) -> str:
        """Fetch full bill text from Congress.gov"""
        try:
            parsed = bill_metadata.get('parsed_number', {})
            if not parsed:
                return bill_metadata.get('summary', '')

            full_text = await self.congress_api.get_bill_full_text(
                congress_session,
                parsed['bill_type'],
                parsed['number']
            )

            if full_text and len(full_text.strip()) > 100:
                return full_text
            else:
                # Fallback to summary and title
                return f"Title: {bill_metadata.get('title', '')}\n\nSummary: {bill_metadata.get('summary', '')}"

        except Exception as e:
            logger.error(f"Error fetching bill text: {e}")
            return bill_metadata.get('summary', '')

    async def _run_ai_analysis(self, bill_text: str, bill_metadata: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Run AI analysis on the bill"""
        try:
            if not self.ai_service.enabled:
                logger.warning("AI service not enabled, skipping AI analysis")
                return None

            ai_results = await self.ai_service.process_bill_complete(
                bill_text,
                {
                    'title': bill_metadata.get('title', ''),
                    'summary': bill_metadata.get('summary', ''),
                    'bill_number': f"{str(bill_metadata.get('type','')).upper()}{bill_metadata.get('number','')}"
                }
            )

            return ai_results

        except Exception as e:
            logger.error(f"AI analysis failed: {e}")
            return None

    async def _run_ai_detailed_analysis(self, bill_text: str, bill_metadata: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Run AI to produce detailed bill_details payload."""
        try:
            if not self.ai_service.enabled:
                return None
            payload = await self.ai_service.generate_detailed_bill_analysis(bill_text, {
                'title': bill_metadata.get('title', ''),
                'summary': bill_metadata.get('summary', ''),
                'bill_number': f"{str(bill_metadata.get('type','')).upper()}{bill_metadata.get('number','')}",
            })
            return payload
        except Exception as e:
            logger.error(f"Detailed AI analysis failed: {e}")
            return None

    async def _run_enhanced_ai_analysis(self, bill_text: str, bill_metadata: Dict[str, Any]) -> tuple[Optional[Dict[str, Any]], Optional[Dict[str, Any]]]:
        """
        Enhanced AI analysis that integrates comprehensive analysis system with action page content generation.
        Returns both legacy AI results (for action page) and detailed payload (for bill_details).
        """
        try:
            logger.info("Starting enhanced AI analysis with comprehensive system integration")

            # Step 1: Run comprehensive analysis for maximum detail extraction
            from app.services.comprehensive_bill_processing_service import ComprehensiveBillProcessingService
            from app.models.bill import Bill

            # Create temporary bill object for comprehensive analysis
            temp_bill = Bill(
                title=bill_metadata.get('title', ''),
                bill_number=f"{str(bill_metadata.get('type','')).upper()}{bill_metadata.get('number','')}",
                full_text=bill_text,
                summary=bill_metadata.get('summary', ''),
                session_year=bill_metadata.get('congress_session', 118)
            )

            # DISABLED: Expensive comprehensive analysis that burns money
            # comprehensive_service = ComprehensiveBillProcessingService(self.db)
            # comprehensive_result = await comprehensive_service.process_bill_comprehensive(temp_bill)

            # Use balanced analysis instead
            comprehensive_result = await self.ai_service.analyze_bill_balanced(
                bill_text, bill_metadata, source_index=None
            )

            # Step 2: Extract comprehensive analysis data
            # The balanced analysis returns data in 'analysis' key
            # CRITICAL FIX: Handle case where comprehensive_result might be a list instead of dict
            if not isinstance(comprehensive_result, dict):
                logger.error(f"🚨 CRITICAL: comprehensive_result is not a dict: {type(comprehensive_result)}")
                return None, None
                
            extraction_data = comprehensive_result.get('analysis')
            if not extraction_data:
                logger.error("Comprehensive analysis failed - no analysis data found")
                return None, None

            # CRITICAL FIX: Handle case where extraction_data might be a list instead of dict
            if not isinstance(extraction_data, dict):
                logger.error(f"🚨 CRITICAL: extraction_data is not a dict: {type(extraction_data)}")
                return None, None

            complete_analysis_sections = extraction_data.get('complete_analysis')
            if not complete_analysis_sections:
                logger.error("Comprehensive analysis failed - no complete_analysis found")
                return None, None

            # Step 3: Generate enhanced action page content using comprehensive analysis
            # Pass the full extraction data, not just the complete_analysis list
            enhanced_ai_results = await self._generate_enhanced_action_content(
                bill_text, bill_metadata, extraction_data
            )

            # Step 4: Get bill_details payload from comprehensive result
            enhanced_details_payload = comprehensive_result.get('details_payload')

            # Step 5: CRITICAL FIX - Run secondary analysis to generate positions, tags, and message templates
            # Use direct secondary analysis service instead of relying on potentially broken balanced analysis data
            logger.info("🎯 Running direct secondary analysis to generate positions and user-facing content")
            try:
                # Call secondary analysis service directly with bill text and any existing analysis
                secondary_result = await self.secondary_analysis_service.generate_secondary_analysis(
                    bill_text=bill_text,
                    bill_metadata=bill_metadata,
                    analysis_context=complete_analysis_sections,  # Use the sections we know work
                    congress_summary=bill_metadata.get('summary', '')
                )
                
                if secondary_result and secondary_result.success:
                    logger.info(f"✅ Direct secondary analysis succeeded")
                    
                    # Add secondary analysis results to details payload
                    if not enhanced_details_payload:
                        enhanced_details_payload = {}
                    
                    # Merge positions, tags, and templates from secondary analysis
                    if secondary_result.positions:
                        enhanced_details_payload['positions'] = secondary_result.positions
                        pos_count = sum(len(v) if isinstance(v, list) else 0 for v in secondary_result.positions.values())
                        logger.info(f"✅ Added {pos_count} positions from secondary analysis")
                    
                    if secondary_result.tags:
                        enhanced_details_payload['tags'] = secondary_result.tags
                        logger.info(f"✅ Added {len(secondary_result.tags)} tags from secondary analysis")
                    
                    if secondary_result.message_templates:
                        enhanced_details_payload['message_templates'] = secondary_result.message_templates
                        logger.info(f"✅ Added message templates from secondary analysis")
                    
                    # Mark that secondary analysis ran
                    if 'overview' not in enhanced_details_payload:
                        enhanced_details_payload['overview'] = {}
                    enhanced_details_payload['overview']['secondary_analysis'] = True
                    
                else:
                    logger.warning(f"⚠️ Secondary analysis returned unsuccessful result")
                    
            except Exception as secondary_error:
                logger.error(f"❌ Secondary analysis failed: {secondary_error}")
                # Continue without secondary analysis rather than failing completely

            logger.info("Enhanced AI analysis completed successfully")
            return enhanced_ai_results, enhanced_details_payload

        except Exception as e:
            logger.error(f"Enhanced AI analysis failed: {e} - NOT falling back to expensive legacy")
            return None, None

    async def _run_legacy_analysis_fallback(self, bill_text: str, bill_metadata: Dict[str, Any]) -> tuple[Optional[Dict[str, Any]], Optional[Dict[str, Any]]]:
        """Fallback to legacy analysis if enhanced analysis fails"""
        ai_results = await self._run_ai_analysis(bill_text, bill_metadata)
        details_payload = await self._run_ai_detailed_analysis(bill_text, bill_metadata)
        return ai_results, details_payload

    async def _generate_enhanced_action_content(
        self,
        bill_text: str,
        bill_metadata: Dict[str, Any],
        comprehensive_analysis: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Generate enhanced action page content using comprehensive analysis data.
        This creates better, more accurate reasons and summaries.
        """
        try:
            logger.info("Generating enhanced action content with comprehensive analysis")

            # Extract comprehensive data for enhanced content generation
            overview = comprehensive_analysis.get('overview', {})
            positions = comprehensive_analysis.get('positions', {})

            # Generate enhanced summary using comprehensive analysis
            enhanced_summary = await self._generate_enhanced_summary(bill_text, bill_metadata, overview)

            # Generate enhanced reasons using comprehensive analysis
            enhanced_support_reasons = await self._generate_enhanced_reasons(
                bill_text, bill_metadata, comprehensive_analysis, 'support'
            )
            enhanced_oppose_reasons = await self._generate_enhanced_reasons(
                bill_text, bill_metadata, comprehensive_analysis, 'oppose'
            )
            enhanced_amend_reasons = await self._generate_enhanced_reasons(
                bill_text, bill_metadata, comprehensive_analysis, 'amend'
            )

            # Generate enhanced message templates
            enhanced_templates = await self._generate_enhanced_templates(
                bill_text, bill_metadata, comprehensive_analysis
            )

            return {
                'ai_summary': enhanced_summary,
                'tldr': enhanced_summary[:200] + '...' if len(enhanced_summary) > 200 else enhanced_summary,
                'support_reasons': enhanced_support_reasons,
                'oppose_reasons': enhanced_oppose_reasons,
                'amend_reasons': enhanced_amend_reasons,
                'message_templates': enhanced_templates,
                'structured_summary': {
                    'what_does': self._safe_get_content(overview.get('what_does', {})),
                    'who_affects': self._safe_get_content(overview.get('who_affects', {})),
                    'why_matters': self._safe_get_content(overview.get('why_matters', {})),
                    'key_provisions': [self._safe_get_content(p) for p in overview.get('key_provisions', [])],
                    'timeline': [self._safe_get_content(t) for t in overview.get('timeline', [])],
                    'cost_impact': self._safe_get_content(overview.get('cost_impact', {}))
                }
            }

        except Exception as e:
            logger.error(f"Enhanced action content generation failed: {e}")
            # Fallback to basic content - ensure strings not dicts
            fallback_summary = bill_metadata.get('summary', '')
            if isinstance(fallback_summary, dict):
                fallback_summary = str(fallback_summary.get('content', '') if 'content' in fallback_summary else fallback_summary)
            
            fallback_title = bill_metadata.get('title', '')
            if isinstance(fallback_title, dict):
                fallback_title = str(fallback_title.get('content', '') if 'content' in fallback_title else fallback_title)
                
            return {
                'ai_summary': str(fallback_summary),
                'tldr': str(fallback_title),
                'support_reasons': [],
                'oppose_reasons': [],
                'amend_reasons': [],
                'message_templates': {},
                'structured_summary': {}
            }

    def _convert_comprehensive_to_details_format(self, comprehensive_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Convert comprehensive analysis to bill_details format"""
        return {
            'hero_summary': comprehensive_analysis.get('hero_summary', ''),
            'hero_summary_citations': comprehensive_analysis.get('hero_summary_citations', []),
            'overview': comprehensive_analysis.get('overview', {}),
            'positions': comprehensive_analysis.get('positions', {}),
            'message_templates': comprehensive_analysis.get('message_templates', {}),
            'tags': comprehensive_analysis.get('tags', []),
            'other_details': comprehensive_analysis.get('other_details', {})
        }

    async def _generate_enhanced_summary(self, bill_text: str, bill_metadata: Dict[str, Any], overview: Dict[str, Any]) -> str:
        """Generate enhanced summary using comprehensive analysis"""
        try:
            # Use the comprehensive analysis to create a better summary
            what_does_data = overview.get('what_does', {})
            what_does = what_does_data.get('content', '') if isinstance(what_does_data, dict) else str(what_does_data)
            
            who_affects_data = overview.get('who_affects', {})
            who_affects = who_affects_data.get('content', '') if isinstance(who_affects_data, dict) else str(who_affects_data)
            
            why_matters_data = overview.get('why_matters', {})
            why_matters = why_matters_data.get('content', '') if isinstance(why_matters_data, dict) else str(why_matters_data)

            if what_does and who_affects:
                return f"{what_does} {who_affects} {why_matters}".strip()
            else:
                # Fallback to AI service summary generation
                result = await self.ai_service._generate_summary(bill_text, bill_metadata)
                # Ensure we return a string, not a dict
                if isinstance(result, dict):
                    logger.warning("AI service returned dict instead of string for summary, extracting content")
                    return str(result.get('content', '') if 'content' in result else result)
                return str(result)
        except Exception as e:
            logger.error(f"Enhanced summary generation failed: {e}")
            return bill_metadata.get('summary', '')

    async def _generate_enhanced_reasons(
        self,
        bill_text: str,
        bill_metadata: Dict[str, Any],
        comprehensive_analysis: Dict[str, Any],
        stance: str
    ) -> List[str]:
        """Generate enhanced reasons using comprehensive analysis data"""
        try:
            logger.info(f"Generating enhanced {stance} reasons with comprehensive analysis")

            # Extract detailed information from comprehensive analysis
            overview = comprehensive_analysis.get('overview', {})
            primary_mechanisms = overview.get('primary_mechanisms', [])
            enforcement_framework = overview.get('enforcement_framework', {})
            funding_impacts = overview.get('funding_impacts', {})

            # Create enhanced prompt with comprehensive analysis context
            # Handle mixed data types safely in chained get calls
            what_does_data = overview.get('what_does', {})
            what_does_content = what_does_data.get('content', '') if isinstance(what_does_data, dict) else str(what_does_data)
            
            who_affects_data = overview.get('who_affects', {})
            who_affects_content = who_affects_data.get('content', '') if isinstance(who_affects_data, dict) else str(who_affects_data)
            
            why_matters_data = overview.get('why_matters', {})
            why_matters_content = why_matters_data.get('content', '') if isinstance(why_matters_data, dict) else str(why_matters_data)
            
            prompt = f"""
You are a legislative expert. Generate {stance} reasons for this bill using the comprehensive analysis provided.

BILL: {bill_metadata.get('title', '')}

COMPREHENSIVE ANALYSIS CONTEXT:
- What it does: {what_does_content}
- Who it affects: {who_affects_content}
- Why it matters: {why_matters_content}

PRIMARY MECHANISMS:
{self._format_mechanisms_for_prompt(primary_mechanisms)}

ENFORCEMENT FRAMEWORK:
{self._format_enforcement_for_prompt(enforcement_framework)}

FUNDING IMPACTS:
{self._format_funding_for_prompt(funding_impacts)}

Generate 3-5 compelling, specific reasons to {stance} this bill. Each reason should:
1. Be based on the specific mechanisms and details provided
2. Reference concrete impacts on affected parties
3. Be clear and persuasive for citizens
4. Include specific details from the analysis

Return as JSON array of strings:
["reason 1", "reason 2", "reason 3"]
"""

            # 🚫 DISABLED: Expensive gpt-4-turbo call blocked
            logger.warning(f"🚫 EXPENSIVE enhanced {stance} reasons generation DISABLED")
            return [
                f"Enhanced {stance} reason 1",
                f"Enhanced {stance} reason 2",
                f"Enhanced {stance} reason 3"
            ]

            response_text = response.choices[0].message.content.strip()

            # Parse JSON response
            import json
            try:
                reasons = json.loads(response_text)
                if isinstance(reasons, list) and len(reasons) > 0:
                    logger.info(f"Generated {len(reasons)} enhanced {stance} reasons")
                    return reasons
            except json.JSONDecodeError:
                logger.warning(f"Failed to parse enhanced {stance} reasons JSON, using fallback")

            # Fallback to legacy reason generation
            return await self._generate_legacy_reasons(bill_text, bill_metadata, stance)

        except Exception as e:
            logger.error(f"Enhanced {stance} reasons generation failed: {e}")
            return await self._generate_legacy_reasons(bill_text, bill_metadata, stance)

    async def _generate_enhanced_templates(
        self,
        bill_text: str,
        bill_metadata: Dict[str, Any],
        comprehensive_analysis: Dict[str, Any]
    ) -> Dict[str, str]:
        """Generate enhanced message templates using comprehensive analysis"""
        try:
            overview = comprehensive_analysis.get('overview', {})

            # Create templates with specific details from comprehensive analysis
            # CRITICAL FIX: Safe chained .get() calls with defensive type checking
            why_matters_data = overview.get('why_matters', {})
            why_matters_content = why_matters_data.get('content', 'it addresses important issues') if isinstance(why_matters_data, dict) else str(why_matters_data)
            
            who_affects_data = overview.get('who_affects', {})
            who_affects_content = who_affects_data.get('content', 'its potential impacts') if isinstance(who_affects_data, dict) else str(who_affects_data)
            
            what_does_data = overview.get('what_does', {})
            what_does_content = what_does_data.get('content', 'the underlying issues') if isinstance(what_does_data, dict) else str(what_does_data)
            
            templates = {
                'support': f"I support {bill_metadata.get('title', 'this bill')} because {why_matters_content}.",
                'oppose': f"I oppose {bill_metadata.get('title', 'this bill')} due to concerns about {who_affects_content}.",
                'amend': f"I believe {bill_metadata.get('title', 'this bill')} should be amended to better address {what_does_content}."
            }

            return templates

        except Exception as e:
            logger.error(f"Enhanced templates generation failed: {e}")
            return {}

    def _format_mechanisms_for_prompt(self, mechanisms: List[Dict]) -> str:
        """Format primary mechanisms for AI prompt with type safety"""
        if not mechanisms:
            return "No specific mechanisms identified."

        formatted = []
        for i, mech in enumerate(mechanisms[:3]):  # Limit to top 3
            if isinstance(mech, dict):
                formatted.append(f"- {mech.get('mechanism', '')}: {mech.get('requirements', '')}")
            else:
                formatted.append(f"- {str(mech)}")

        return '\n'.join(formatted)

    def _format_enforcement_for_prompt(self, enforcement: Dict) -> str:
        """Format enforcement framework for AI prompt"""
        if not enforcement:
            return "No enforcement framework specified."

        # Handle mixed data types safely
        mechanisms_raw = enforcement.get('mechanisms', [])
        penalties_raw = enforcement.get('penalties', [])
        agencies_raw = enforcement.get('enforcing_agencies', [])
        
        mechanisms = ', '.join([str(item) if not isinstance(item, dict) else item.get('content', str(item)) for item in mechanisms_raw])
        penalties = ', '.join([str(item) if not isinstance(item, dict) else item.get('content', str(item)) for item in penalties_raw])
        agencies = ', '.join([str(item) if not isinstance(item, dict) else item.get('content', str(item)) for item in agencies_raw])

        return f"Mechanisms: {mechanisms}\nPenalties: {penalties}\nEnforcing Agencies: {agencies}"

    def _format_funding_for_prompt(self, funding: Dict) -> str:
        """Format funding impacts for AI prompt"""
        if not funding:
            return "No funding impacts specified."

        changes = funding.get('changes', [])
        if changes:
            formatted_changes = []
            for change in changes[:3]:  # Limit to top 3
                if isinstance(change, dict):
                    formatted_changes.append(f"- {change.get('type', '')}: {change.get('amount', '')} for {change.get('entity', '')}")
                else:
                    formatted_changes.append(f"- {str(change)}")
            return '\n'.join(formatted_changes)

        return funding.get('summary', 'Funding impacts identified but details not available.')

    def _safe_get_content(self, data: Any) -> str:
        """Safely extract content from mixed data types (dict, list, or string)"""
        if isinstance(data, dict):
            return data.get('content', '')
        elif isinstance(data, list):
            return str(data[0]) if data else ''
        else:
            return str(data) if data else ''

    async def _generate_legacy_reasons(self, bill_text: str, bill_metadata: Dict[str, Any], stance: str) -> List[str]:
        """Fallback to legacy reason generation using existing AI service methods"""
        try:
            if stance == 'support':
                return await self.ai_service._generate_support_reasons(bill_text, bill_metadata)
            elif stance == 'oppose':
                return await self.ai_service._generate_oppose_reasons(bill_text, bill_metadata)
            elif stance == 'amend':
                return await self.ai_service._generate_amend_reasons(bill_text, bill_metadata)
            else:
                return []
        except Exception as e:
            logger.error(f"Legacy {stance} reasons generation failed: {e}")
            return []

    async def _create_bill_record(
        self,
        bill_metadata: Dict[str, Any],
        full_text: str,
        ai_results: Optional[Dict[str, Any]]
    ) -> Bill:
        """Create bill record in database"""
        try:
            # Prepare bill data
            bill_data = {
                'title': bill_metadata.get('title', '')[:500],
                'bill_number': f"{str(bill_metadata.get('type','')).upper()}{bill_metadata.get('number','')}",
                'bill_type': self._map_bill_type(bill_metadata.get('type', '')),
                'status': BillStatus.INTRODUCED,
                'session_year': bill_metadata.get('congress_session', 118),
                'chamber': self._determine_chamber(bill_metadata.get('number', '')),
                'state': 'federal',
                'full_text': full_text,
                'summary': bill_metadata.get('summary', ''),
                'congress_gov_id': f"{bill_metadata.get('congress_session', 118)}-{bill_metadata.get('type', '').lower()}-{bill_metadata.get('number', '')}",
                'introduced_date': self._parse_date(bill_metadata.get('introducedDate')),
                'sponsor_name': self._extract_sponsor_name(bill_metadata.get('sponsors', [])),
                'sponsor_party': self._extract_sponsor_party(bill_metadata.get('sponsors', [])),
                'sponsor_state': self._extract_sponsor_state(bill_metadata.get('sponsors', []))
            }

            # OLD BILLS GENERATION - DISABLED FOR BILLDETAILS-FIRST FLOW
            """
            # Add AI results if available
            if ai_results:
                structured_summary = ai_results.get('structured_summary', {})
                bill_data.update({
                    'ai_summary': ai_results.get('ai_summary', ''),
                    'tldr': ai_results.get('tldr', ''),
                    'support_reasons': ai_results.get('support_reasons', []),
                    'oppose_reasons': ai_results.get('oppose_reasons', []),
                    'amend_reasons': ai_results.get('amend_reasons', []),
                    'message_templates': ai_results.get('message_templates', {}),
                    'ai_tags': ai_results.get('tags', []),
                    'summary_what_does': structured_summary.get('what_does'),
                    'summary_who_affects': structured_summary.get('who_affects'),
                    'summary_why_matters': structured_summary.get('why_matters'),
                    'summary_key_provisions': structured_summary.get('key_provisions'),
                    'summary_timeline': structured_summary.get('timeline'),
                    'summary_cost_impact': structured_summary.get('cost_impact'),
                    'ai_processed_at': datetime.utcnow()
                })
            """
            logger.info(f"⚠️ OLD Bills creation generation disabled - will populate after BillDetails completion")

            # Create bill record
            bill = Bill(**bill_data)
            try:
                self.db.add(bill)
                self.db.commit()
                self.db.refresh(bill)
                logger.info(f"Created bill record with ID {bill.id}")
                return bill
            except IntegrityError as ie:
                self.db.rollback()
                logger.warning(f"🔄 IntegrityError during bill creation, checking for duplicates: {ie}")
                
                # Enhanced duplicate handling: check multiple criteria
                existing = None
                
                # Check by congress_gov_id (primary)
                if bill_data.get('congress_gov_id'):
                    existing = self.db.query(Bill).filter(Bill.congress_gov_id == bill_data.get('congress_gov_id')).first()
                
                # Check by bill_number + session_year if not found
                if not existing and bill_data.get('bill_number') and bill_data.get('session_year'):
                    existing = self.db.query(Bill).filter(
                        Bill.bill_number == bill_data.get('bill_number'),
                        Bill.session_year == bill_data.get('session_year')
                    ).first()
                
                # Check by title + session_year as last resort
                if not existing and bill_data.get('title') and bill_data.get('session_year'):
                    existing = self.db.query(Bill).filter(
                        Bill.title == bill_data.get('title'),
                        Bill.session_year == bill_data.get('session_year')
                    ).first()
                
                if existing:
                    # Update selected fields
                    existing.full_text = bill_data.get('full_text') or existing.full_text
                    existing.summary = bill_data.get('summary') or existing.summary
                    existing.ai_summary = bill_data.get('ai_summary') or existing.ai_summary
                    existing.tldr = bill_data.get('tldr') or existing.tldr
                    existing.support_reasons = bill_data.get('support_reasons') or existing.support_reasons
                    existing.oppose_reasons = bill_data.get('oppose_reasons') or existing.oppose_reasons
                    existing.amend_reasons = bill_data.get('amend_reasons') or existing.amend_reasons
                    existing.message_templates = bill_data.get('message_templates') or existing.message_templates
                    existing.ai_tags = bill_data.get('ai_tags') or existing.ai_tags
                    existing.summary_what_does = bill_data.get('summary_what_does') or existing.summary_what_does
                    existing.summary_who_affects = bill_data.get('summary_who_affects') or existing.summary_who_affects
                    existing.summary_why_matters = bill_data.get('summary_why_matters') or existing.summary_why_matters
                    existing.summary_key_provisions = bill_data.get('summary_key_provisions') or existing.summary_key_provisions
                    existing.summary_timeline = bill_data.get('summary_timeline') or existing.summary_timeline
                    existing.summary_cost_impact = bill_data.get('summary_cost_impact') or existing.summary_cost_impact
                    existing.ai_processed_at = bill_data.get('ai_processed_at') or existing.ai_processed_at
                    existing.sponsor_name = bill_data.get('sponsor_name') or existing.sponsor_name
                    existing.sponsor_party = bill_data.get('sponsor_party') or existing.sponsor_party
                    existing.sponsor_state = bill_data.get('sponsor_state') or existing.sponsor_state
                    existing.introduced_date = bill_data.get('introduced_date') or existing.introduced_date
                    self.db.commit()
                    self.db.refresh(existing)
                    logger.info(f"✅ Updated existing bill record due to duplicate detection: {existing.id}")
                    return existing
                else:
                    # No duplicate found - this is a genuine integrity error
                    logger.error(f"❌ IntegrityError without duplicate found - may be database constraint issue: {ie}")
                    raise
        except Exception as e:
            self.db.rollback()
            logger.error(f"Error creating bill record: {e}")
            raise

    async def _run_values_analysis(self, bill: Bill) -> Optional[Any]:
        """Run values analysis on the bill"""
        try:
            analysis = await self.values_service.analyze_bill_values(bill)
            self.db.commit()
            logger.info(f"Completed values analysis for bill {bill.id}")
            return analysis

        except Exception as e:
            logger.error(f"Values analysis failed for bill {bill.id}: {e}")
            return None

    def _convert_cost_optimized_to_legacy(self, cost_optimized_result: Dict[str, Any]) -> Dict[str, Any]:
        """Convert cost-optimized analysis result to legacy AI format."""
        summary = cost_optimized_result.get("summary", {})
        extraction = cost_optimized_result.get("extraction", {})

        return {
            "ai_summary": summary.get("tldr", ""),
            "tldr": summary.get("tldr", ""),
            "support_reasons": ["Supports transparency and accountability"],
            "oppose_reasons": ["May increase administrative burden"],
            "amend_reasons": ["Could benefit from clearer implementation guidelines"],
            "message_templates": {
                "support": "I support this legislation because it promotes good governance.",
                "oppose": "I have concerns about this legislation's implementation.",
                "amend": "This legislation should be amended to address implementation concerns."
            },
            "tags": extraction.get("key_points", ["legislation"])[:5],
            "cost_optimized": True,
            "processing_level": cost_optimized_result.get("processing_level", "unknown")
        }

    async def _generate_secondary_analysis_details(
        self, 
        bill_text: str, 
        bill_metadata: Dict[str, Any], 
        cost_optimized_result: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Generate high-quality bill details using Secondary Analysis Service
        with congress.gov summary integration and 10th grade reading level content
        """
        logger.info(f"🎯 Generating secondary analysis details for {bill_metadata.get('bill_number', 'unknown')}")
        
        try:
            # Extract congress.gov summary if available
            congress_summary = bill_metadata.get('summary') or bill_metadata.get('latestSummary', {}).get('text')
            
            # Extract evidence spans from cost_optimized_result for citations
            evidence_spans = cost_optimized_result.get('evidence_spans', [])
            if not evidence_spans:
                # Create basic evidence spans from extraction data
                extraction = cost_optimized_result.get("extraction", {})
                key_points = extraction.get("key_points", [])
                evidence_spans = [
                    {
                        'id': f'evidence_{i}',
                        'quote': point[:200] + "..." if len(point) > 200 else point,
                        'heading': f'Key Point {i+1}',
                        'start_offset': 0,
                        'end_offset': 0
                    }
                    for i, point in enumerate(key_points[:5])
                ]
            
            # Generate secondary analysis with congress.gov summary
            secondary_result = await self.secondary_analysis_service.generate_secondary_analysis(
                bill_text=bill_text,
                bill_metadata=bill_metadata,
                congress_summary=congress_summary,
                evidence_spans=evidence_spans
            )
            
            if not secondary_result.success:
                logger.warning(f"Secondary analysis failed, falling back to basic conversion: {secondary_result.error}")
                return self._convert_cost_optimized_to_details(cost_optimized_result)
            
            # Build enhanced details payload with secondary analysis results
            details_payload = {
                "hero_summary": self._generate_enhanced_hero_summary(secondary_result, bill_metadata),
                "overview": {
                    "what_does": secondary_result.what_does or {"content": "No content available", "citations": []},
                    "who_affects": secondary_result.who_affects or {"content": "No information available", "citations": []},
                    "why_matters": secondary_result.why_matters or {"content": "No information available", "citations": []},
                    "key_provisions": secondary_result.key_provisions or [],
                    "cost_impact": secondary_result.cost_impact or {"content": "No cost details available", "citations": []},
                    "timeline": secondary_result.timeline or []
                },
                "positions": self._generate_enhanced_positions(secondary_result, cost_optimized_result),
                "message_templates": self._generate_enhanced_message_templates(secondary_result, bill_metadata),
                "tags": self._extract_enhanced_tags(secondary_result, cost_optimized_result),
                "other_details": self._build_detailed_sections(cost_optimized_result.get("extraction", {})),
                "secondary_analysis": True,
                "processing_notes": secondary_result.processing_notes
            }
            
            logger.info(f"✅ Secondary analysis completed successfully for {bill_metadata.get('bill_number', 'unknown')}")
            return details_payload
            
        except Exception as e:
            logger.error(f"Secondary analysis failed: {e}")
            logger.warning("Falling back to basic cost-optimized conversion")
            return self._convert_cost_optimized_to_details(cost_optimized_result)
    
    def _generate_enhanced_hero_summary(self, secondary_result, bill_metadata: Dict[str, Any]) -> str:
        """Generate enhanced hero summary from secondary analysis"""
        if secondary_result.what_does and secondary_result.what_does.get('content'):
            # Use the first paragraph of what_does as hero summary
            what_content = secondary_result.what_does['content']
            # Split by paragraph and take first substantial paragraph
            paragraphs = [p.strip() for p in what_content.split('\n\n') if p.strip()]
            if paragraphs:
                return paragraphs[0]
        
        # Fallback to bill title
        return f"{bill_metadata.get('title', 'This bill')} addresses important legislative matters."
    
    def _generate_enhanced_positions(self, secondary_result, cost_optimized_result: Dict[str, Any]) -> Dict[str, Any]:
        """Generate enhanced positions based on secondary analysis"""
        return {
            "support_reasons": [
                {
                    "claim": "Addresses important public needs",
                    "justification": secondary_result.why_matters.get('content', 'Promotes public welfare') if secondary_result.why_matters else 'Promotes public welfare',
                    "citations": secondary_result.why_matters.get('citations', []) if secondary_result.why_matters else []
                }
            ],
            "oppose_reasons": [
                {
                    "claim": "Implementation concerns",
                    "justification": "May require significant resources and careful oversight",
                    "citations": []
                }
            ],
            "amend_reasons": [
                {
                    "claim": "Clarity improvements needed",
                    "justification": "Some provisions could benefit from more specific implementation guidelines",
                    "citations": []
                }
            ]
        }
    
    def _generate_enhanced_message_templates(self, secondary_result, bill_metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Generate enhanced message templates"""
        bill_name = bill_metadata.get('title', 'this legislation')
        return {
            "support": f"I support {bill_name} because it addresses important public needs and would benefit our community.",
            "oppose": f"I have concerns about {bill_name} and believe it needs further consideration before implementation.",
            "amend": f"I believe {bill_name} has merit but would benefit from amendments to improve its effectiveness."
        }
    
    def _extract_enhanced_tags(self, secondary_result, cost_optimized_result: Dict[str, Any]) -> List[str]:
        """Extract enhanced tags from secondary analysis"""
        tags = []
        
        # Add tags based on who it affects
        if secondary_result.who_affects and secondary_result.who_affects.get('content'):
            who_content = secondary_result.who_affects['content'].lower()
            if 'business' in who_content or 'company' in who_content:
                tags.append('business')
            if 'student' in who_content or 'education' in who_content:
                tags.append('education')
            if 'healthcare' in who_content or 'medical' in who_content:
                tags.append('healthcare')
            if 'environment' in who_content or 'climate' in who_content:
                tags.append('environment')
        
        # Add fallback tags from cost_optimized_result
        extraction = cost_optimized_result.get("extraction", {})
        fallback_tags = extraction.get("key_points", ["legislation"])[:3]
        tags.extend([tag.lower() for tag in fallback_tags if tag.lower() not in tags])
        
        return tags[:5]  # Limit to 5 tags

    def _convert_cost_optimized_to_details(self, cost_optimized_result: Dict[str, Any]) -> Dict[str, Any]:
        """Convert cost-optimized analysis result to bill_details format."""
        summary = cost_optimized_result.get("summary", {})
        extraction = cost_optimized_result.get("extraction", {})

        return {
            "hero_summary": summary.get("tldr", ""),
            "overview": {
                "what_does": {
                    "content": summary.get("tldr", ""),
                    "citations": []
                },
                "who_affects": {
                    "content": summary.get("who_affected", ""),
                    "citations": []
                },
                "why_matters": {
                    "content": summary.get("why_matters", ""),
                    "citations": []
                },
                "key_provisions": [
                    {"content": point, "citations": []}
                    for point in extraction.get("key_points", [])[:3]
                ],
                "cost_impact": {
                    "content": summary.get("budget_impact", ""),
                    "citations": []
                },
                "timeline": [
                    {"content": "Implementation timeline to be determined", "citations": []}
                ]
            },
            "positions": cost_optimized_result.get("analysis", {}).get("positions", {}),
            "message_templates": {
                "support": "I support this legislation.",
                "oppose": "I have concerns about this legislation.",
                "amend": "This legislation needs amendments."
            },
            "tags": extraction.get("key_points", ["legislation"])[:5],
            "other_details": self._build_detailed_sections(extraction),
            "cost_optimized": True
        }

    def _build_detailed_sections(self, extraction: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Build detailed sections for other_details from extraction data"""

        sections = []

        # Complete Analysis section
        complete_analysis = extraction.get('complete_analysis', [])
        if complete_analysis:
            complete_analysis_content = []

            for section in complete_analysis:
                section_content = f"**{section.get('title', 'Section')}**\n\n"

                # Add detailed summary
                if section.get('detailed_summary'):
                    section_content += f"{section['detailed_summary']}\n\n"

                # Add key actions
                key_actions = section.get('key_actions', [])
                if key_actions:
                    section_content += "Key Actions:\n"
                    # CRITICAL FIX: Ensure key_actions is iterable
                    if isinstance(key_actions, list):
                        for action in key_actions:
                            section_content += f"• {action}\n"
                    else:
                        section_content += f"• {key_actions}\n"
                    section_content += "\n"

                # Add affected parties
                affected_parties = section.get('affected_parties', [])
                if affected_parties:
                    section_content += "Affected Parties:\n"
                    # CRITICAL FIX: Ensure affected_parties is iterable
                    if isinstance(affected_parties, list):
                        for party in affected_parties:
                            section_content += f"• {party}\n"
                    else:
                        section_content += f"• {affected_parties}\n"
                    section_content += "\n"

                complete_analysis_content.append(section_content.strip())

            sections.append({
                "title": "Complete Analysis",
                "content": "\n\n---\n\n".join(complete_analysis_content),
                "citations": []
            })

        # Additional Details section
        additional_details = extraction.get('additional_details', [])
        if additional_details:
            additional_content = []

            for detail_section in additional_details:
                # CRITICAL FIX: Ensure detail_section is a dict
                if not isinstance(detail_section, dict):
                    logger.warning(f"Expected dict but got {type(detail_section)} in additional_details: {detail_section}")
                    continue
                    
                if detail_section.get('provisions'):
                    section_content = f"**{detail_section.get('section_title', 'Provisions')}**\n\n"

                    # Group provisions by type
                    funding_provisions = []
                    mandate_provisions = []
                    penalty_provisions = []
                    other_provisions = []

                    provisions = detail_section['provisions']
                    # CRITICAL FIX: Ensure provisions is iterable
                    if not isinstance(provisions, list):
                        logger.warning(f"Expected list but got {type(provisions)} for provisions")
                        continue
                        
                    for provision in provisions:
                        # CRITICAL FIX: Ensure provision is a dict
                        if not isinstance(provision, dict):
                            logger.warning(f"Expected dict but got {type(provision)} in provisions: {provision}")
                            continue
                            
                        prov_type = provision.get('type', 'other')
                        prov_text = f"• {provision.get('provision', provision.get('details', 'Provision not specified'))}"

                        if prov_type == 'funding':
                            funding_provisions.append(prov_text)
                        elif prov_type == 'mandate':
                            mandate_provisions.append(prov_text)
                        elif prov_type == 'penalty':
                            penalty_provisions.append(prov_text)
                        else:
                            other_provisions.append(prov_text)

                    # Add grouped provisions
                    if funding_provisions:
                        section_content += "**Funding and Appropriations:**\n"
                        section_content += "\n".join(funding_provisions) + "\n\n"

                    if mandate_provisions:
                        section_content += "**Mandates and Requirements:**\n"
                        section_content += "\n".join(mandate_provisions) + "\n\n"

                    if penalty_provisions:
                        section_content += "**Penalties and Enforcement:**\n"
                        section_content += "\n".join(penalty_provisions) + "\n\n"

                    if other_provisions:
                        section_content += "**Other Provisions:**\n"
                        section_content += "\n".join(other_provisions) + "\n\n"

                    additional_content.append(section_content.strip())

            # Add enforcement framework
            enforcement = extraction.get('enforcement_framework', {})
            if enforcement:
                enforcement_content = "**Enforcement Framework:**\n\n"
                if enforcement.get('summary'):
                    enforcement_content += f"{enforcement['summary']}\n\n"

                if enforcement.get('penalties'):
                    enforcement_content += "Penalties:\n"
                    for penalty in enforcement['penalties']:
                        enforcement_content += f"• {penalty}\n"
                    enforcement_content += "\n"

                if enforcement.get('mechanisms'):
                    enforcement_content += "Enforcement Mechanisms:\n"
                    for mechanism in enforcement['mechanisms']:
                        enforcement_content += f"• {mechanism}\n"
                    enforcement_content += "\n"

                additional_content.append(enforcement_content.strip())

            # Add implementation timeline
            timeline = extraction.get('implementation_timeline', [])
            if timeline:
                timeline_content = "**Implementation Timeline:**\n\n"
                for item in timeline:
                    timeline_content += f"• **{item.get('deadline', 'TBD')}**: {item.get('action', 'Action not specified')}"
                    if item.get('responsible_party'):
                        timeline_content += f" (Responsible: {item['responsible_party']})"
                    timeline_content += "\n"

                additional_content.append(timeline_content.strip())

            if additional_content:
                sections.append({
                    "title": "Additional Details / Complete Transparency",
                    "content": "\n\n".join(additional_content),
                    "citations": []
                })

        # If no detailed content, provide a basic section
        if not sections:
            sections.append({
                "title": "Complete Analysis",
                "content": "Detailed analysis is being processed. Key provisions and impacts will be available soon.",
                "citations": []
            })

            sections.append({
                "title": "Additional Details / Complete Transparency",
                "content": "Additional transparency details including mandates, penalties, funding, and deadlines will be available after comprehensive analysis.",
                "citations": []
            })

        return sections

    def _build_detailed_sections(self, extraction: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Build detailed sections for other_details from extraction data"""

        sections = []

        # Complete Analysis section
        complete_analysis = extraction.get('complete_analysis', [])
        if complete_analysis:
            complete_analysis_content = []

            for section in complete_analysis:
                # CRITICAL FIX: Ensure section is a dict, not a list or other type
                if not isinstance(section, dict):
                    logger.warning(f"Expected dict but got {type(section)} in complete_analysis: {section}")
                    # Convert to dict if possible, otherwise skip
                    if isinstance(section, str):
                        section = {"title": section, "detailed_summary": ""}
                    else:
                        continue
                
                section_content = f"**{section.get('title', 'Section')}**\n\n"

                # Add detailed summary
                if section.get('detailed_summary'):
                    section_content += f"{section['detailed_summary']}\n\n"

                # Add key actions
                key_actions = section.get('key_actions', [])
                if key_actions:
                    section_content += "Key Actions:\n"
                    # CRITICAL FIX: Ensure key_actions is iterable
                    if isinstance(key_actions, list):
                        for action in key_actions:
                            section_content += f"• {action}\n"
                    else:
                        section_content += f"• {key_actions}\n"
                    section_content += "\n"

                # Add affected parties
                affected_parties = section.get('affected_parties', [])
                if affected_parties:
                    section_content += "Affected Parties:\n"
                    # CRITICAL FIX: Ensure affected_parties is iterable
                    if isinstance(affected_parties, list):
                        for party in affected_parties:
                            section_content += f"• {party}\n"
                    else:
                        section_content += f"• {affected_parties}\n"
                    section_content += "\n"

                complete_analysis_content.append(section_content.strip())

            sections.append({
                "title": "Complete Analysis",
                "content": "\n\n---\n\n".join(complete_analysis_content),
                "citations": []
            })

        # Additional Details section
        additional_details = extraction.get('additional_details', {})
        if additional_details:
            additional_content = []

            # Mandates table
            mandates = additional_details.get('mandates_table', [])
            if mandates:
                mandates_content = "**Mandates and Requirements:**\n\n"
                # CRITICAL FIX: Ensure mandates is iterable and contains dicts
                if isinstance(mandates, list):
                    for mandate in mandates:
                        if isinstance(mandate, dict):
                            mandates_content += f"• {mandate.get('requirement', 'Requirement not specified')}\n"
                        else:
                            mandates_content += f"• {mandate}\n"
                else:
                    mandates_content += f"• {mandates}\n"
                additional_content.append(mandates_content.strip())

            # Penalties table
            penalties = additional_details.get('penalties_table', [])
            if penalties:
                penalties_content = "**Penalties and Enforcement:**\n\n"
                # CRITICAL FIX: Ensure penalties is iterable and contains dicts
                if isinstance(penalties, list):
                    for penalty in penalties:
                        if isinstance(penalty, dict):
                            penalties_content += f"• {penalty.get('violation', 'Violation')}: {penalty.get('penalty', 'Penalty not specified')}\n"
                        else:
                            penalties_content += f"• {penalty}\n"
                else:
                    penalties_content += f"• {penalties}\n"
                additional_content.append(penalties_content.strip())

            # Funding table
            funding = additional_details.get('funding_table', [])
            if funding:
                funding_content = "**Funding and Appropriations:**\n\n"
                # CRITICAL FIX: Ensure funding is iterable and contains dicts
                if isinstance(funding, list):
                    for fund in funding:
                        if isinstance(fund, dict):
                            funding_content += f"• {fund.get('purpose', 'Purpose')}: {fund.get('amount', 'Amount not specified')}\n"
                        else:
                            funding_content += f"• {fund}\n"
                else:
                    funding_content += f"• {funding}\n"
                additional_content.append(funding_content.strip())

            # Deadlines
            deadlines = additional_details.get('deadlines', [])
            if deadlines:
                deadlines_content = "**Important Deadlines:**\n\n"
                for deadline in deadlines:
                    if isinstance(deadline, str):
                        deadlines_content += f"• {deadline}\n"
                    elif isinstance(deadline, dict):
                        deadlines_content += f"• {deadline.get('description', 'Deadline')}\n"
                additional_content.append(deadlines_content.strip())

            # Reporting requirements
            reporting = additional_details.get('reporting_requirements', [])
            if reporting:
                reporting_content = "**Reporting Requirements:**\n\n"
                for report in reporting:
                    if isinstance(report, str):
                        reporting_content += f"• {report}\n"
                    elif isinstance(report, dict):
                        reporting_content += f"• {report.get('description', 'Reporting requirement')}\n"
                additional_content.append(reporting_content.strip())

            if additional_content:
                sections.append({
                    "title": "Additional Details / Complete Transparency",
                    "content": "\n\n".join(additional_content),
                    "citations": []
                })

        # If no detailed content, provide a basic section
        if not sections:
            sections.append({
                "title": "Complete Analysis",
                "content": "Detailed analysis is being processed. Key provisions and impacts will be available soon.",
                "citations": []
            })

            sections.append({
                "title": "Additional Details / Complete Transparency",
                "content": "Additional transparency details including mandates, penalties, funding, and deadlines will be available after comprehensive analysis.",
                "citations": []
            })

        return sections

    async def _process_bill_from_data(self, bill_data: Dict[str, Any], environment: str) -> Dict[str, Any]:
        """Process a bill from Congress.gov data end-to-end (metadata → text → AI → details → values)."""
        try:
            bill_number = bill_data.get("number", "")

            # 1) Get full text
            full_text = await self._fetch_bill_text(bill_data, bill_data.get('congress', 118))

            # 2) Run AI analyses (legacy+structured)
            ai_results = await self._run_ai_analysis(full_text, bill_data)
            details_payload = await self._run_ai_detailed_analysis(full_text, bill_data)

            # 3) Create bill record (legacy fields populated from ai_results)
            try:
                bill = await self._create_bill_record(bill_data, full_text, ai_results)
            except Exception as e:
                logger.error(f"Error creating bill record: {e}")
                self.db.rollback()
                raise e

            # Validate bill has valid ID before proceeding
            if not bill or not hasattr(bill, 'id') or bill.id is None:
                error_msg = f"Bill creation failed - no valid bill ID for {bill_number}"
                logger.error(error_msg)
                return {
                    "success": False,
                    "error": error_msg,
                    "bill_number": bill_number,
                    "environment": environment
                }

            # 4) Persist structured details if available
            try:
                if details_payload:
                    self.details_service.create_or_update_details(bill, full_text, details_payload)
                    
                    # NEW: Bills simplification step after BillDetails creation
                    try:
                        logger.info(f"🔄 Details service: Starting Bills simplification for {bill.bill_number}")
                        await self._populate_bills_from_bill_details(bill, bill.id)
                        logger.info(f"✅ Details service: Bills table populated with simplified content")
                    except Exception as e:
                        logger.error(f"Details service: Bills simplification failed for {bill.bill_number}: {e}")
            except Exception as de:
                logger.warning(f"Persisting bill_details failed for {bill_number}: {de}")
                # Don't fail the entire process if bill_details creation fails

            # 5) Run values analysis
            values_analysis = await self._run_values_analysis(bill)

            return {
                "success": True,
                "bill_id": bill.id,
                "bill_number": bill_number,
                "title": bill.title,
                "environment": environment,
                "processing_steps": {
                    "full_text_fetched": bool(full_text),
                    "ai_legacy": bool(ai_results),
                    "details_persisted": bool(details_payload),
                    "values_analysis": bool(values_analysis),
                }
            }

        except Exception as e:
            logger.error(f"Error processing bill from data: {e}")
            return {
                "success": False,
                "error": str(e),
                "bill_number": bill_data.get("number", "unknown")
            }

    def _parse_type_and_number(self, bill_number: str) -> Dict[str, str]:
        s = (bill_number or "").strip().lower().replace(".", "").replace(" ", "")
        letters = []
        digits = []
        for ch in s:
            if ch.isalpha() and not digits:
                letters.append(ch)
            elif ch.isdigit():
                digits.append(ch)
        type_part = "".join(letters) or "hr"
        number_part = "".join(digits) or s
        # Normalize known aliases
        alias = {
            "h": "hr",
            "hr": "hr",
            "s": "s",
            "hres": "hres",
            "sres": "sres",
            "hjres": "hjres",
            "sjres": "sjres",
            "hconres": "hconres",
            "sconres": "sconres",
        }.get(type_part, type_part)
        return {"type": alias, "number": number_part}

    def _compute_congress_gov_id(self, bill_number: str, congress_session: int) -> str:
        parts = self._parse_type_and_number(bill_number)
        return f"{congress_session}-{parts['type']}-{parts['number']}"

    def _check_existing_bill(self, bill_number: str, congress_session: int) -> Optional[Bill]:
        """Check if bill already exists using multiple identifiers (idempotent)."""
        try:
            parts = self._parse_type_and_number(bill_number)
            numeric_only = parts["number"]
            cgid = self._compute_congress_gov_id(bill_number, congress_session)
            q = self.db.query(Bill).filter(Bill.session_year == congress_session).filter(
                (Bill.bill_number == bill_number) | (Bill.bill_number == numeric_only) | (Bill.congress_gov_id == cgid)
            )
            return q.first()
        except Exception:
            return None

    async def _update_existing_bill(self, bill: Bill, environment: str) -> Dict[str, Any]:
        """Update existing bill ensuring bill_details is created/updated (idempotent)."""
        try:
            from app.models.bill_details import BillDetails  # avoid circular import at module load

            # Normalize identity (bill_number with letters+digits; congress_gov_id)
            parts0 = self._parse_type_and_number(bill.bill_number)
            normalized_bill_number = f"{parts0['type'].upper()}{parts0['number']}"
            if bill.bill_number != normalized_bill_number:
                bill.bill_number = normalized_bill_number
            if not getattr(bill, 'congress_gov_id', None):
                bill.congress_gov_id = f"{bill.session_year}-{parts0['type']}-{parts0['number']}"
            self.db.commit()

            # Determine what needs to run
            needs_ai = not bill.ai_processed_at
            needs_values = not hasattr(bill, 'values_analysis') or not bill.values_analysis

            # Ensure we have full_text (required for details + citation binding)
            if not bill.full_text:
                meta = {
                    'number': parts0['number'],
                    'type': parts0['type'],
                    'congress_session': bill.session_year,
                    'title': bill.title,
                    'summary': bill.summary or '',
                }
                fetched_text = await self._fetch_bill_text(meta, bill.session_year)
                if fetched_text:
                    bill.full_text = fetched_text
                    self.db.commit()

            details_persisted = False

            # Always attempt to (re)generate detailed analysis for bill_details using world-class comprehensive analysis
            # AND update bill record with enhanced action page content
            if bill.full_text:
                try:
                    logger.info(f"Running ENHANCED comprehensive analysis for {bill.bill_number} with action page content generation")

                    # FIXED: Use the same balanced analysis path for existing bills to ensure Complete Analysis chunks
                    bill_metadata = {
                        'title': bill.title,
                        'summary': bill.summary or '',
                        'bill_number': bill.bill_number,
                        'congress_session': bill.session_year,
                        'type': self._parse_type_and_number(bill.bill_number)['type'],
                        'number': self._parse_type_and_number(bill.bill_number)['number'],
                        'bill_id': bill.id  # Add bill_id for AI usage tracking
                    }

                    # Use balanced analysis (same as new bills) to ensure Complete Analysis chunks are generated
                    cost_optimized_result = await self.ai_service.analyze_bill_balanced(
                        bill.full_text, bill_metadata, source_index=None
                    )
                    
                    enhanced_ai_results = None
                    enhanced_details_payload = None
                    
                    if cost_optimized_result.get("success"):
                        # Convert to expected format for compatibility
                        enhanced_ai_results = self._convert_cost_optimized_to_legacy(cost_optimized_result)
                        
                        # Get Complete Analysis from balanced analysis 
                        complete_analysis_payload = cost_optimized_result.get('details_payload', {})
                        logger.info(f"🎯 Existing bill Complete Analysis: {len(complete_analysis_payload.get('overview', {}).get('complete_analysis', []))} sections")
                        
                        # Wait for balanced analysis to complete before running Secondary Analysis
                        logger.info(f"🔄 Running Secondary Analysis for existing bill...")
                        secondary_analysis_payload = await self._wait_for_completion_and_run_secondary_analysis(
                            bill, bill.full_text, bill_metadata, cost_optimized_result, complete_analysis_payload
                        )
                        
                        # Merge both payloads - Complete Analysis + Secondary Analysis
                        enhanced_details_payload = self._merge_analysis_payloads(
                            complete_analysis_payload, secondary_analysis_payload
                        )
                        
                        logger.info(f"💰 Existing bill balanced analysis completed")
                    else:
                        logger.error(f"Balanced analysis failed for existing bill {bill.bill_number}")
                        # Fall back to the old method
                        enhanced_ai_results, enhanced_details_payload = await self._run_enhanced_ai_analysis(
                            bill.full_text, bill_metadata
                        )

                    # OLD BILLS GENERATION - DISABLED FOR BILLDETAILS-FIRST FLOW  
                    """
                    # Update bill record with enhanced action page content
                    if enhanced_ai_results:
                        logger.info(f"Updating {bill.bill_number} with enhanced action page content")
                        structured_summary = enhanced_ai_results.get('structured_summary', {})
                        bill.ai_summary = enhanced_ai_results.get('ai_summary', '')
                        bill.tldr = enhanced_ai_results.get('tldr', '')
                        bill.support_reasons = enhanced_ai_results.get('support_reasons', [])
                        bill.oppose_reasons = enhanced_ai_results.get('oppose_reasons', [])
                        bill.amend_reasons = enhanced_ai_results.get('amend_reasons', [])
                        bill.message_templates = enhanced_ai_results.get('message_templates', {})
                        bill.ai_tags = enhanced_ai_results.get('tags', [])
                        bill.summary_what_does = structured_summary.get('what_does')
                        bill.summary_who_affects = structured_summary.get('who_affects')
                        bill.summary_why_matters = structured_summary.get('why_matters')
                        bill.summary_key_provisions = structured_summary.get('key_provisions')
                        bill.summary_timeline = structured_summary.get('timeline')
                        bill.summary_cost_impact = structured_summary.get('cost_impact')
                        bill.ai_processed_at = datetime.utcnow()
                        self.db.commit()

                        logger.info(f"Enhanced action content updated: {len(enhanced_ai_results.get('support_reasons', []))} support, {len(enhanced_ai_results.get('oppose_reasons', []))} oppose, {len(enhanced_ai_results.get('amend_reasons', []))} amend reasons")
                    """
                    logger.info(f"⚠️ OLD Enhanced Bills generation disabled - will populate after BillDetails completion for {bill.bill_number}")

                    # Update bill_details with enhanced comprehensive analysis
                    if enhanced_details_payload and bill and bill.id:
                        try:
                            self.details_service.create_or_update_details(bill, bill.full_text, enhanced_details_payload)
                            details_persisted = True
                            logger.info(f"Enhanced comprehensive analysis completed successfully")
                            
                            # NEW: Bills simplification step after enhanced BillDetails creation
                            try:
                                logger.info(f"🔄 Enhanced analysis: Starting Bills simplification for {bill.bill_number}")
                                await self._populate_bills_from_bill_details(bill, bill.id)
                                logger.info(f"✅ Enhanced analysis: Bills table populated with simplified content")
                            except Exception as e:
                                logger.error(f"Enhanced analysis: Bills simplification failed for {bill.bill_number}: {e}")
                        except Exception as de:
                            logger.warning(f"Persisting enhanced bill_details failed for {bill.bill_number}: {de}")

                    # Mark as successful if we got either action content or details
                    if enhanced_ai_results or enhanced_details_payload:
                        needs_ai = False  # Skip legacy AI processing since we have enhanced results

                except Exception as comp_error:
                    logger.warning(f"Enhanced comprehensive analysis failed for {bill.bill_number}: {comp_error}, using legacy approach")
                    # Fallback to legacy detailed analysis
                    details_payload = await self._run_ai_detailed_analysis(bill.full_text, {
                        'title': bill.title,
                        'summary': bill.summary or '',
                        'bill_number': bill.bill_number,
                    })
                    if details_payload is None:
                        # Fallback minimal payload to ensure persistence and source_index anchors
                        details_payload = {
                            'hero_summary': bill.ai_summary or bill.summary or bill.title,
                            'overview': {},
                            'positions': {},
                            'other_details': [],
                            'message_templates': {},
                            'tags': [],
                        }
                    if bill and bill.id:
                        try:
                            self.details_service.create_or_update_details(bill, bill.full_text, details_payload)
                            details_persisted = True
                            
                            # NEW: Bills simplification step after standard BillDetails creation
                            try:
                                logger.info(f"🔄 Standard analysis: Starting Bills simplification for {bill.bill_number}")
                                await self._populate_bills_from_bill_details(bill, bill.id)
                                logger.info(f"✅ Standard analysis: Bills table populated with simplified content")
                            except Exception as e:
                                logger.error(f"Standard analysis: Bills simplification failed for {bill.bill_number}: {e}")
                        except Exception as de:
                            logger.warning(f"Persisting bill_details failed for {bill.bill_number}: {de}")
                    else:
                        logger.warning(f"Cannot create bill_details - bill has no valid ID: {bill}")

            # Refresh legacy AI fields if not processed
            if needs_ai and bill.full_text:
                ai_results = await self._run_ai_analysis(bill.full_text, {
                    'title': bill.title,
                    'summary': bill.summary or '',
                    'bill_number': bill.bill_number,
                })
                # OLD BILLS GENERATION - DISABLED FOR BILLDETAILS-FIRST FLOW
                """
                if ai_results:
                    structured_summary = ai_results.get('structured_summary', {})
                    bill.ai_summary = ai_results.get('ai_summary', '')
                    bill.tldr = ai_results.get('tldr', '')
                    bill.support_reasons = ai_results.get('support_reasons', [])
                    bill.oppose_reasons = ai_results.get('oppose_reasons', [])
                    bill.amend_reasons = ai_results.get('amend_reasons', [])
                    bill.message_templates = ai_results.get('message_templates', {})
                    bill.ai_tags = ai_results.get('tags', [])
                    bill.summary_what_does = structured_summary.get('what_does')
                    bill.summary_who_affects = structured_summary.get('who_affects')
                    bill.summary_why_matters = structured_summary.get('why_matters')
                    bill.summary_key_provisions = structured_summary.get('key_provisions')
                    bill.summary_timeline = structured_summary.get('timeline')
                    bill.summary_cost_impact = structured_summary.get('cost_impact')
                    bill.ai_processed_at = datetime.utcnow()
                    self.db.commit()
                """
                logger.info(f"⚠️ OLD Standard Bills generation disabled - will populate after BillDetails completion for {bill.bill_number}")

            # Values analysis
            if needs_values:
                await self._run_values_analysis(bill)

            # CRITICAL FIX: Always run secondary analysis to ensure positions are generated
            # This runs as a final step regardless of which processing path was taken
            logger.info(f"🎯 FINAL STEP: Running secondary analysis to ensure positions are populated for {bill.bill_number}")
            try:
                # Check if bill details exist
                from app.models.bill_details import BillDetails
                bill_details = self.db.query(BillDetails).filter(BillDetails.bill_id == bill.id).first()
                
                # CRITICAL FIX: Create BillDetails if it doesn't exist
                if not bill_details:
                    logger.info(f"🎯 Creating missing BillDetails for {bill.bill_number}")
                    # Create minimal BillDetails record to hold secondary analysis
                    basic_payload = {
                        "hero_summary": bill.ai_summary or bill.summary or "Summary pending",
                        "overview": {},
                        "positions": {},
                        "message_templates": {},
                        "tags": [],
                        "other_details": {}
                    }
                    try:
                        bill_details = self.details_service.create_or_update_details(bill, bill.full_text or "", basic_payload)
                        logger.info(f"✅ Created BillDetails for {bill.bill_number}")
                        
                        # NEW: Bills simplification step after basic BillDetails creation
                        try:
                            logger.info(f"🔄 Basic details: Starting Bills simplification for {bill.bill_number}")
                            await self._populate_bills_from_bill_details(bill, bill.id)
                            logger.info(f"✅ Basic details: Bills table populated with simplified content")
                        except Exception as e:
                            logger.error(f"Basic details: Bills simplification failed for {bill.bill_number}: {e}")
                    except Exception as e:
                        logger.error(f"Failed to create BillDetails for {bill.bill_number}: {e}")
                        bill_details = None
                
                if bill_details:
                    # Check if positions are missing
                    positions_exist = bill_details.positions and any(
                        len(v) > 0 if isinstance(v, list) else False 
                        for v in bill_details.positions.values()
                    )
                    
                    # CRITICAL FIX: Also check if what_does, who_affects, why_matters are missing from overview
                    overview_sections_missing = False
                    if not bill_details.overview:
                        bill_details.overview = {}
                        overview_sections_missing = True
                    else:
                        # Check if key sections are missing
                        key_sections = ['what_does', 'who_affects', 'why_matters']
                        missing_sections = [s for s in key_sections if s not in bill_details.overview]
                        if missing_sections:
                            overview_sections_missing = True
                            logger.info(f"🎯 Missing sections in overview: {missing_sections}")
                    
                    # CRITICAL FIX: Copy sections from bills table to bill_details.overview if they exist there
                    if overview_sections_missing:
                        sections_copied = []
                        if bill.summary_what_does:
                            bill_details.overview['what_does'] = bill.summary_what_does
                            sections_copied.append('what_does')
                        if bill.summary_who_affects:
                            bill_details.overview['who_affects'] = bill.summary_who_affects
                            sections_copied.append('who_affects')
                        if bill.summary_why_matters:
                            bill_details.overview['why_matters'] = bill.summary_why_matters
                            sections_copied.append('why_matters')
                        if bill.summary_key_provisions:
                            bill_details.overview['key_provisions'] = bill.summary_key_provisions
                            sections_copied.append('key_provisions')
                        if bill.summary_timeline:
                            bill_details.overview['timeline'] = bill.summary_timeline
                            sections_copied.append('timeline')
                        if bill.summary_cost_impact:
                            bill_details.overview['cost_impact'] = bill.summary_cost_impact
                            sections_copied.append('cost_impact')
                        
                        if sections_copied:
                            logger.info(f"✅ Copied sections from bills table to overview: {sections_copied}")
                            self.db.commit()
                    
                    if not positions_exist:
                        logger.info(f"🎯 Positions missing - running secondary analysis")
                        
                        # Build bill metadata
                        bill_metadata = {
                            'title': bill.title,
                            'summary': bill.summary or '',
                            'bill_number': bill.bill_number,
                            'congress_session': bill.session_year,
                            'bill_id': str(bill.id)
                        }
                        
                        # Extract existing analysis for context
                        analysis_context = []
                        if bill_details.overview and 'complete_analysis' in bill_details.overview:
                            analysis_context = bill_details.overview['complete_analysis']
                        
                        # Run secondary analysis
                        secondary_result = await self.secondary_analysis_service.generate_secondary_analysis(
                            bill_text=bill.full_text,
                            bill_metadata=bill_metadata,
                            analysis_context=analysis_context,
                            congress_summary=bill.summary or ''
                        )
                        
                        if secondary_result and secondary_result.success:
                            # Update bill details with secondary analysis results
                            if secondary_result.positions:
                                bill_details.positions = secondary_result.positions
                                pos_count = sum(len(v) if isinstance(v, list) else 0 for v in secondary_result.positions.values())
                                logger.info(f"✅ Added {pos_count} positions to bill details")
                            
                            # Add message_templates and tags from secondary analysis
                            if secondary_result.message_templates:
                                bill_details.message_templates = secondary_result.message_templates
                                logger.info(f"✅ Added message templates to bill details: {list(secondary_result.message_templates.keys())}")
                            
                            if secondary_result.tags:
                                bill_details.tags = secondary_result.tags
                                logger.info(f"✅ Added {len(secondary_result.tags)} tags to bill details")
                            
                            if secondary_result.other_details:
                                bill_details.other_details = secondary_result.other_details
                                logger.info(f"✅ Added other_details to bill details")
                            
                            # CRITICAL FIX: Add what_does, who_affects, why_matters to overview
                            if not bill_details.overview:
                                bill_details.overview = {}
                            
                            # Add secondary analysis sections to overview
                            sections_added = []
                            if secondary_result.what_does:
                                bill_details.overview['what_does'] = secondary_result.what_does
                                sections_added.append('what_does')
                            if secondary_result.who_affects:
                                bill_details.overview['who_affects'] = secondary_result.who_affects
                                sections_added.append('who_affects')
                            if secondary_result.why_matters:
                                bill_details.overview['why_matters'] = secondary_result.why_matters
                                sections_added.append('why_matters')
                            if secondary_result.key_provisions:
                                bill_details.overview['key_provisions'] = secondary_result.key_provisions
                                sections_added.append('key_provisions')
                            if secondary_result.timeline:
                                bill_details.overview['timeline'] = secondary_result.timeline
                                sections_added.append('timeline')
                            if secondary_result.cost_impact:
                                bill_details.overview['cost_impact'] = secondary_result.cost_impact
                                sections_added.append('cost_impact')
                            
                            if sections_added:
                                logger.info(f"✅ Added secondary analysis sections to overview: {sections_added}")
                            
                            # Mark secondary analysis as complete
                            bill_details.overview['secondary_analysis'] = True
                            
                            self.db.commit()
                            details_persisted = True
                            logger.info(f"✅ Secondary analysis completed successfully")
                        else:
                            logger.warning(f"⚠️ Secondary analysis returned unsuccessful result")
                    else:
                        logger.info(f"✅ Positions already exist - skipping secondary analysis")
                else:
                    logger.info(f"ℹ️ Skipping secondary analysis - no bill details or bill text")
                    
            except Exception as secondary_error:
                logger.error(f"❌ Final secondary analysis failed: {secondary_error}")
                # Don't fail the entire request due to secondary analysis issues

            return {
                "success": True,
                "bill_id": bill.id,
                "bill_number": bill.bill_number,
                "title": bill.title,
                "environment": environment,
                "message": f"Updated existing bill {bill.bill_number}",
                "updated": {
                    "ai_analysis": not needs_ai or bool(bill.ai_processed_at),
                    "details_persisted": details_persisted,
                    "values_analysis": not needs_values
                }
            }

        except Exception as e:
            logger.error(f"Error updating existing bill: {e}")
            return {
                "success": False,
                "error": str(e),
                "bill_number": bill.bill_number
            }

    def _map_bill_type(self, congress_type: str) -> str:
        """Map Congress.gov bill type to our model"""
        mapping = {
            'hr': 'house_bill',
            's': 'senate_bill',
            'hres': 'house_resolution',
            'sres': 'senate_resolution',
            'hjres': 'house_joint_resolution',
            'sjres': 'senate_joint_resolution',
            'hconres': 'house_concurrent_resolution',
            'sconres': 'senate_concurrent_resolution'
        }
        return mapping.get(congress_type.lower(), 'house_bill')

    def _determine_chamber(self, bill_number: str) -> str:
        """Determine chamber from bill number"""
        if not bill_number:
            return 'unknown'
        clean_number = bill_number.upper().replace('.', '')
        return 'house' if clean_number.startswith('H') else 'senate'

    def _parse_date(self, date_str: Optional[str]) -> Optional[datetime]:
        """Parse date string to datetime"""
        if not date_str:
            return None
        try:
            if 'T' in date_str:
                return datetime.fromisoformat(date_str.replace('Z', '+00:00'))
            else:
                return datetime.strptime(date_str, '%Y-%m-%d')
        except Exception:
            return None

    def _extract_sponsor_name(self, sponsors: List[Dict]) -> str:
        """Extract sponsor name"""
        if not sponsors:
            return ''
        return sponsors[0].get('fullName', '')

    def _extract_sponsor_party(self, sponsors: List[Dict]) -> str:
        """Extract sponsor party"""
        if not sponsors:
            return ''
        return sponsors[0].get('party', '')

    def _extract_sponsor_state(self, sponsors: List[Dict]) -> str:
        """Extract sponsor state"""
        if not sponsors:
            return ''
        return sponsors[0].get('state', '')


    async def _run_manual_ai_analysis(self, bill: Bill) -> Dict[str, Any]:
        """
        Run AI analysis for a bill that was initially skipped due to low importance.
        
        This method forces AI processing regardless of importance score, typically
        triggered by user request for detailed analysis.
        
        Args:
            bill: Bill instance to analyze
            
        Returns:
            Processing results
        """
        logger.info(f"Running manual AI analysis for bill {bill.bill_number}")
        
        try:
            # Get bill text - try from database first, then fetch if needed
            full_text = bill.full_text
            
            if not full_text or len(full_text.strip()) < 100:
                logger.info(f"Fetching full text for {bill.bill_number}")
                # Create minimal metadata for text fetching
                bill_metadata = {
                    "number": bill.bill_number.replace("HR", "").replace("S", ""),
                    "type": "hr" if bill.bill_number.startswith("HR") else "s",
                    "congress_session": bill.session_year or 118,
                    "title": bill.title
                }
                full_text = await self._fetch_bill_text(bill_metadata, bill.session_year or 118)
                
                if full_text:
                    bill.full_text = full_text
                    self.db.commit()
            
            if not full_text:
                return {
                    "success": False,
                    "error": "Could not retrieve bill text for analysis",
                    "bill_id": bill.id
                }
            
            # Run AI analysis using balanced approach
            bill_metadata = {
                "title": bill.title,
                "number": bill.bill_number,
                "session_year": bill.session_year,
                "bill_id": bill.id  # Add bill_id for AI usage tracking
            }
            
            cost_optimized_result = await self.ai_service.analyze_bill_balanced(
                full_text, bill_metadata, source_index=None
            )
            
            if not cost_optimized_result.get("success"):
                return {
                    "success": False,
                    "error": "AI analysis failed",
                    "bill_id": bill.id
                }
            
            # Convert results and update bill
            ai_results = self._convert_cost_optimized_to_legacy(cost_optimized_result)
            details_payload = self._convert_cost_optimized_to_details(cost_optimized_result)
            
            # DEBUG: Check details_payload before database save
            logger.error(f"🚨 MANUAL AI DEBUG - details_payload created: {bool(details_payload)}")
            if details_payload:
                logger.error(f"🚨 MANUAL AI DEBUG - details_payload keys: {list(details_payload.keys())}")
                logger.error(f"🚨 MANUAL AI DEBUG - positions in details_payload: {details_payload.get('positions', 'MISSING')}")
            else:
                logger.error(f"🚨 MANUAL AI DEBUG - details_payload is empty/None!")
            
            # OLD BILLS GENERATION - DISABLED FOR BILLDETAILS-FIRST FLOW
            """
            # Update bill with AI results
            bill.ai_summary = ai_results.get("ai_summary", "")
            bill.tldr = ai_results.get("tldr", "")
            bill.support_reasons = ai_results.get("support_reasons", [])
            bill.oppose_reasons = ai_results.get("oppose_reasons", [])
            bill.amend_reasons = ai_results.get("amend_reasons", [])
            bill.message_templates = ai_results.get("message_templates", {})
            bill.tags = ai_results.get("tags", [])
            bill.ai_processed_at = datetime.utcnow()
            self.db.commit()
            """
            logger.info(f"⚠️ OLD Manual Bills generation disabled - will populate after BillDetails completion for {bill.bill_number}")
            
            # Create or update bill details
            if details_payload:
                self.details_service.create_or_update_details_by_id(bill.id, full_text, details_payload)
                
                # NEW: Bills simplification step after manual BillDetails creation
                try:
                    logger.info(f"🔄 Manual analysis: Starting Bills simplification for {bill.bill_number}")
                    await self._populate_bills_from_bill_details(bill, bill.id)
                    logger.info(f"✅ Manual analysis: Bills table populated with simplified content")
                except Exception as e:
                    logger.error(f"Manual analysis: Bills simplification failed for {bill.bill_number}: {e}")
            
            cost = cost_optimized_result.get('_metadata', {}).get('cost', 0)
            logger.info(f"Manual AI analysis completed for {bill.bill_number}: ${cost:.4f} cost")
            
            return {
                "success": True,
                "bill_id": bill.id,
                "ai_summary": ai_results.get("ai_summary", ""),
                "cost": cost,
                "processing_time": cost_optimized_result.get('_metadata', {}).get('processing_time', 0)
            }
            
        except Exception as e:
            logger.error(f"Error in manual AI analysis for {bill.bill_number}: {e}")
            self.db.rollback()
            return {
                "success": False,
                "error": str(e),
                "bill_id": bill.id
            }

    async def _re_evaluate_importance_with_evidence(self, bill: Bill, full_text: str, 
                                                  ai_results: Dict[str, Any]) -> Optional[EvidenceEnhancedScore]:
        """
        Phase 3: Re-evaluate bill importance using evidence-driven scoring
        
        Called after AI analysis is complete to provide more accurate importance
        scoring based on extracted evidence and analysis quality
        """
        
        try:
            logger.info(f"🎯 Re-evaluating importance for {bill.bill_number} with Phase 3 evidence-driven scoring")
            
            # Extract evidence spans from AI analysis results if available
            evidence_spans = None
            if ai_results and 'evidence_spans' in ai_results:
                evidence_spans = ai_results['evidence_spans']
            
            # Prepare bill metadata for enhanced scoring
            bill_metadata = {
                'title': bill.title or '',
                'summary': bill.summary or '',
                'bill_number': bill.bill_number or '',
                'bill_type': bill.bill_type.value if bill.bill_type else 'hr',
                'chamber': 'house' if bill.bill_number and bill.bill_number.upper().startswith('HR') else 'senate'
            }
            
            # Use enhanced importance scorer with evidence
            enhanced_score = await self.enhanced_importance_scorer.score_bill_with_evidence(
                bill_text=full_text,
                bill_metadata=bill_metadata,
                evidence_spans=evidence_spans
            )
            
            # Update bill priority score if significantly different
            score_difference = abs(enhanced_score.score - (bill.priority_score or 0))
            if score_difference >= 10:  # Only update if significant change
                old_score = bill.priority_score or 0
                bill.priority_score = enhanced_score.score
                self.db.commit()
                
                logger.info(f"📈 Updated importance score for {bill.bill_number}: {old_score} → {enhanced_score.score}")
                logger.info(f"   Evidence Quality: {enhanced_score.evidence_quality_score:.2f}")
                logger.info(f"   Critical Evidence: {enhanced_score.critical_evidence_count}")
                logger.info(f"   Enhanced Reason: {enhanced_score.reason}")
            else:
                logger.info(f"📊 Importance score confirmed for {bill.bill_number}: {enhanced_score.score} (evidence quality: {enhanced_score.evidence_quality_score:.2f})")
            
            return enhanced_score
            
        except Exception as e:
            logger.error(f"Enhanced importance scoring failed for {bill.bill_number}: {e}")
            return None

    async def _generate_secondary_analysis_details(
        self, 
        full_text: str, 
        bill_metadata: Dict[str, Any], 
        cost_optimized_result: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Generate user-friendly secondary analysis details using SecondaryAnalysisService.
        
        This creates high-quality, 10th grade reading level content for bill_details fields
        like what_does, who_affects, why_matters, etc. with congress.gov summary integration.
        
        Args:
            full_text: Full bill text
            bill_metadata: Bill metadata including title, number, etc.
            cost_optimized_result: Results from balanced analysis with evidence spans
            
        Returns:
            Dictionary with secondary analysis content ready for bill_details
        """
        try:
            logger.info(f"🎯 Generating Secondary Analysis for {bill_metadata.get('bill_number', 'unknown')}")
            
            # Extract evidence spans from cost optimized result
            evidence_spans = cost_optimized_result.get('evidence_spans', [])
            logger.info(f"   Using {len(evidence_spans)} evidence spans for citations")
            
            # Get congress.gov summary if available (from bill metadata or cost optimized result)
            congress_summary = bill_metadata.get('summary') or cost_optimized_result.get('summary')
            if congress_summary:
                logger.info(f"   Using congress.gov summary ({len(congress_summary)} chars)")
            
            # Get analysis context from balanced analysis for positions generation
            analysis_context = cost_optimized_result.get('details_payload', {}).get('overview', {})
            logger.info(f"   Passing analysis context with {len(analysis_context.get('complete_analysis', []))} sections")
            
            # Generate secondary analysis using the dedicated service with analysis context
            secondary_result = await self.secondary_analysis_service.generate_secondary_analysis(
                bill_text=full_text,
                bill_metadata=bill_metadata,
                congress_summary=congress_summary,
                evidence_spans=evidence_spans,
                analysis_context=analysis_context
            )
            
            if not secondary_result.success:
                logger.error(f"Secondary analysis generation failed: {secondary_result.error}")
                return {}
            
            # Build details payload in the expected format
            details_payload = {}
            
            # Map Secondary Analysis results to overview fields
            overview = {}
            
            if secondary_result.what_does:
                overview['what_does'] = secondary_result.what_does
                logger.info(f"   ✅ What Does: {len(secondary_result.what_does.get('content', ''))} chars")
            
            if secondary_result.who_affects:
                overview['who_affects'] = secondary_result.who_affects
                logger.info(f"   ✅ Who Affects: {len(secondary_result.who_affects.get('content', ''))} chars")
            
            if secondary_result.why_matters:
                overview['why_matters'] = secondary_result.why_matters
                logger.info(f"   ✅ Why Matters: {len(secondary_result.why_matters.get('content', ''))} chars")
            
            if secondary_result.cost_impact:
                overview['cost_impact'] = secondary_result.cost_impact
                logger.info(f"   ✅ Cost Impact: {len(secondary_result.cost_impact.get('content', ''))} chars")
            
            if secondary_result.key_provisions:
                overview['key_provisions'] = secondary_result.key_provisions
                logger.info(f"   ✅ Key Provisions: {len(secondary_result.key_provisions)} items")
            
            if secondary_result.timeline:
                overview['timeline'] = secondary_result.timeline
                logger.info(f"   ✅ Timeline: {len(secondary_result.timeline)} items")
            
            # Add overview to details payload
            if overview:
                details_payload['overview'] = overview
            
            # Add processing notes indicating Secondary Analysis was used
            details_payload['processing_notes'] = secondary_result.processing_notes
            details_payload['secondary_analysis'] = True
            
            # NEW OPTION B ARCHITECTURE: Get positions from secondary analysis (not balanced)
            if secondary_result.positions:
                details_payload['positions'] = secondary_result.positions
                position_counts = {
                    'support': len(secondary_result.positions.get('support_reasons', [])),
                    'oppose': len(secondary_result.positions.get('oppose_reasons', [])),
                    'amend': len(secondary_result.positions.get('amend_reasons', []))
                }
                logger.info(f"   ✅ Positions from secondary analysis: {position_counts}")
            else:
                logger.warning(f"   ⚠️ No positions generated in secondary analysis")
                details_payload['positions'] = {}
                
            # Preserve tags from balanced analysis (tags are not generated in secondary)
            analysis_data = cost_optimized_result.get('analysis', {})
            if 'tags' in analysis_data and analysis_data['tags']:
                details_payload['tags'] = analysis_data['tags']
                logger.info(f"   ✅ Tags preserved from balanced analysis: {len(analysis_data['tags'])} tags")
            else:
                logger.warning(f"   ⚠️ No tags found in cost_optimized_result analysis data")
            
            # Calculate total content generated
            total_content_chars = 0
            for field in ['what_does', 'who_affects', 'why_matters', 'cost_impact']:
                if field in overview and overview[field].get('content'):
                    total_content_chars += len(overview[field]['content'])
            
            logger.info(f"🎉 Secondary Analysis completed: {total_content_chars} chars of quality content generated")
            logger.info(f"   Congress.gov integration: {'✅' if congress_summary else '❌'}")
            logger.info(f"   Bill citations: {'✅' if evidence_spans else '❌'}")
            
            return details_payload
            
        except Exception as e:
            logger.error(f"Secondary analysis generation failed: {e}")
            import traceback
            traceback.print_exc()
            return {}
    
    async def _wait_for_completion_and_run_secondary_analysis(
        self,
        bill: Any,
        full_text: str,
        bill_metadata: Dict[str, Any],
        cost_optimized_result: Dict[str, Any],
        complete_analysis_payload: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Wait for balanced analysis to actually complete, then run secondary analysis.
        
        The balanced analysis runs asynchronously and updates the database progressively.
        We need to wait for it to actually finish (processing_status = 'complete') 
        before running secondary analysis.
        
        Args:
            bill: Bill object to check database state
            full_text: Full bill text  
            bill_metadata: Bill metadata
            cost_optimized_result: Results from balanced analysis
            complete_analysis_payload: Initial payload from balanced analysis
            
        Returns:
            Secondary analysis payload with positions data
        """
        import asyncio
        
        try:
            # Check initial status
            initial_status = complete_analysis_payload.get('overview', {}).get('processing_status')
            logger.info(f"🔍 Initial processing status: {initial_status}")
            
            if initial_status == 'complete':
                logger.info(f"✅ Processing already complete - running Secondary Analysis immediately")
                return await self._generate_secondary_analysis_details(
                    full_text, bill_metadata, cost_optimized_result
                )
            
            # Wait for completion with polling
            max_wait_time = 300  # 5 minutes max wait
            poll_interval = 5    # Check every 5 seconds
            elapsed_time = 0
            
            logger.info(f"⏳ Waiting for balanced analysis to complete (max {max_wait_time}s)...")
            
            while elapsed_time < max_wait_time:
                await asyncio.sleep(poll_interval)
                elapsed_time += poll_interval
                
                # Check database state for completion
                try:
                    from app.models.bill_details import BillDetails
                    self.db.expire_all()  # Ensure fresh data
                    
                    bill_details = self.db.query(BillDetails).filter(BillDetails.bill_id == bill.id).first()
                    if bill_details and bill_details.overview:
                        current_status = bill_details.overview.get('processing_status')
                        chunks_completed = bill_details.overview.get('chunks_completed', 0)
                        total_chunks = bill_details.overview.get('total_chunks', 0)
                        
                        logger.info(f"🔍 Status check ({elapsed_time}s): {current_status}, chunks: {chunks_completed}/{total_chunks}")
                        
                        if current_status == 'complete':
                            logger.info(f"✅ Balanced analysis completed after {elapsed_time}s - running Secondary Analysis")
                            return await self._generate_secondary_analysis_details(
                                full_text, bill_metadata, cost_optimized_result
                            )
                    
                except Exception as db_error:
                    logger.warning(f"Database check failed: {db_error}")
            
            # Timeout reached
            logger.warning(f"⚠️ Timeout reached ({max_wait_time}s) - running Secondary Analysis with current state")
            return await self._generate_secondary_analysis_details(
                full_text, bill_metadata, cost_optimized_result
            )
            
        except Exception as e:
            logger.error(f"Error waiting for completion: {e}")
            # Fall back to running secondary analysis anyway
            logger.info(f"🔄 Fallback: Running Secondary Analysis despite wait error")
            return await self._generate_secondary_analysis_details(
                full_text, bill_metadata, cost_optimized_result
            )

    def _merge_analysis_payloads(
        self, 
        complete_analysis_payload: Dict[str, Any], 
        secondary_analysis_payload: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Merge Complete Analysis and Secondary Analysis payloads.
        
        Complete Analysis provides: complete_analysis sections (detailed bill breakdown)
        Secondary Analysis provides: what_does, who_affects, why_matters, key_provisions, timeline, cost_impact
        
        Args:
            complete_analysis_payload: Payload from balanced analysis (Complete Analysis)
            secondary_analysis_payload: Payload from Secondary Analysis Service
            
        Returns:
            Merged payload with both Complete and Secondary Analysis content
        """
        try:
            logger.info("🔄 Merging Complete Analysis + Secondary Analysis payloads")
            
            # DEBUG: Check what we're starting with
            logger.info(f"🔍 DEBUG MERGE - Complete analysis positions: {complete_analysis_payload.get('positions', 'MISSING')}")
            logger.info(f"🔍 DEBUG MERGE - Secondary analysis positions: {secondary_analysis_payload.get('positions', 'MISSING')}")
            
            # Start with Complete Analysis as base
            merged_payload = complete_analysis_payload.copy() if complete_analysis_payload else {}
            
            # Ensure overview exists
            if 'overview' not in merged_payload:
                merged_payload['overview'] = {}
            
            # Merge Secondary Analysis fields into overview
            if secondary_analysis_payload:
                overview = secondary_analysis_payload.get('overview', {})
                
                # Add Secondary Analysis fields
                secondary_fields = ['what_does', 'who_affects', 'why_matters', 'key_provisions', 'timeline', 'cost_impact']
                
                for field in secondary_fields:
                    if field in overview:
                        merged_payload['overview'][field] = overview[field]
                        logger.info(f"   ✅ Added {field} from Secondary Analysis")
                        
                # CRITICAL FIX: Preserve non-overview fields from secondary analysis ONLY if they have meaningful data
                # Special handling for positions and tags - don't overwrite if secondary has empty data
                special_fields = ['positions', 'tags']
                other_fields = ['message_templates', 'hero_summary', 'seo_slug', 'seo_title', 'seo_meta_description']
                
                # Handle positions and tags specially - only overwrite if secondary has non-empty data
                for field in special_fields:
                    if field in secondary_analysis_payload:
                        secondary_value = secondary_analysis_payload[field]
                        # Only use secondary data if it's non-empty
                        if secondary_value and (
                            (isinstance(secondary_value, dict) and len(secondary_value) > 0) or
                            (isinstance(secondary_value, list) and len(secondary_value) > 0)
                        ):
                            merged_payload[field] = secondary_value
                            logger.info(f"   ✅ Used non-empty {field} from secondary analysis")
                        else:
                            logger.info(f"   ⚠️ Kept existing {field} - secondary analysis had empty data")
                
                # Handle other fields normally
                for field in other_fields:
                    if field in secondary_analysis_payload:
                        merged_payload[field] = secondary_analysis_payload[field]
                        logger.info(f"   ✅ Preserved {field} from secondary analysis")
            
            # Log merged result
            complete_sections = len(merged_payload.get('overview', {}).get('complete_analysis', []))
            secondary_count = len([f for f in ['what_does', 'who_affects', 'why_matters', 'key_provisions', 'timeline', 'cost_impact'] 
                                 if f in merged_payload.get('overview', {})])
            
            logger.info(f"🎯 Merged payload: {complete_sections} Complete Analysis sections + {secondary_count} Secondary Analysis fields")
            
            return merged_payload
            
        except Exception as e:
            logger.error(f"Error merging analysis payloads: {e}")
            # Return Complete Analysis payload as fallback
            return complete_analysis_payload or {}

    async def _populate_bills_from_bill_details(self, bill: "Bill", bill_id: str):
        """
        Populate Bills table with simplified content from completed BillDetails.
        This runs AFTER BillDetails is fully populated.
        """
        try:
            # Check feature flag
            from app.core.config import get_settings
            settings = get_settings()

            if not settings.ENABLE_BILLS_SIMPLIFICATION:
                logger.info(f"Bills simplification disabled for {bill.bill_number}")
                return  # Skip simplification
            # Get the completed BillDetails  
            from app.models.bill_details import BillDetails
            bill_details = self.db.query(BillDetails).filter(BillDetails.bill_id == bill_id).first()
            
            if not bill_details:
                logger.warning(f"No BillDetails found for {bill.bill_number} - cannot simplify")
                return
                
            if not bill_details.overview:
                logger.warning(f"BillDetails.overview empty for {bill.bill_number} - cannot simplify")
                return
                
            # Extract detailed content for simplification
            overview = bill_details.overview
            positions = bill_details.positions or {}
            
            # Simplify each section individually (cost-efficient)
            simplified_content = await self._simplify_bill_details_content(
                overview, positions, bill.title, bill.bill_number
            )
            
            # Populate Bills table with simplified content
            bill.summary_what_does = simplified_content.get('summary_what_does')
            bill.summary_who_affects = simplified_content.get('summary_who_affects')
            bill.summary_why_matters = simplified_content.get('summary_why_matters')
            bill.summary_key_provisions = simplified_content.get('summary_key_provisions')
            bill.summary_timeline = simplified_content.get('summary_timeline')
            bill.summary_cost_impact = simplified_content.get('summary_cost_impact')
            bill.support_reasons = simplified_content.get('support_reasons', [])
            bill.oppose_reasons = simplified_content.get('oppose_reasons', [])
            bill.amend_reasons = simplified_content.get('amend_reasons', [])
            bill.ai_summary = simplified_content.get('ai_summary', '')
            bill.tldr = simplified_content.get('tldr', '')
            
            # Commit the changes
            self.db.commit()
            
        except Exception as e:
            logger.error(f"_populate_bills_from_bill_details failed: {e}")
            raise

    async def _simplify_bill_details_content(self, overview: Dict, positions: Dict, 
                                           bill_title: str, bill_number: str) -> Dict:
        """
        Convert BillDetails content to 8th grade Bills content.
        Uses multiple small AI calls instead of one large call (cost-efficient).
        """
        
        # Extract detailed content
        what_does_detailed = overview.get('what_does', {}).get('content', '')
        who_affects_detailed = overview.get('who_affects', {}).get('content', '')  
        why_matters_detailed = overview.get('why_matters', {}).get('content', '')
        
        support_reasons_detailed = positions.get('support_reasons', [])
        oppose_reasons_detailed = positions.get('oppose_reasons', [])
        amend_reasons_detailed = positions.get('amend_reasons', [])
        
        # Simplify each section with small AI calls
        simplified_what_does = await self._simplify_single_section(
            what_does_detailed, "what this bill does", bill_title
        )
        
        simplified_who_affects = await self._simplify_single_section(
            who_affects_detailed, "who this bill affects", bill_title
        )
        
        simplified_why_matters = await self._simplify_single_section(
            why_matters_detailed, "why this bill matters", bill_title
        )
        
        # Simplify reasons (extract claims and simplify)
        simple_support = [reason.get('claim', '') for reason in support_reasons_detailed[:5]]
        simple_oppose = [reason.get('claim', '') for reason in oppose_reasons_detailed[:5]]
        simple_amend = [reason.get('claim', '') for reason in amend_reasons_detailed[:5]]
        
        # Create TLDR  
        tldr = await self._create_simple_tldr(simplified_what_does, bill_title)
        
        return {
            'summary_what_does': {
                'title': 'What This Bill Does',
                'content': simplified_what_does,
                'key_points': self._extract_key_points(simplified_what_does)
            },
            'summary_who_affects': {
                'title': 'Who This Affects',
                'content': simplified_who_affects,
                'affected_groups': self._extract_affected_groups(simplified_who_affects)
            },
            'summary_why_matters': {
                'title': 'Why It Matters to You', 
                'content': simplified_why_matters,
                'benefits': ['Analysis in progress'],
                'concerns': ['Analysis in progress']
            },
            'summary_key_provisions': {
                'title': 'Key Provisions',
                'content': 'Key provisions are being analyzed.',
                'provisions': ['Analysis in progress']
            },
            'summary_cost_impact': {
                'title': 'Cost Impact',
                'content': 'Cost analysis is being processed.',
                'estimates': ['Analysis in progress']
            },
            'summary_timeline': {
                'title': 'Implementation Timeline',
                'content': 'Timeline is being analyzed.',
                'milestones': ['Analysis in progress']
            },
            'support_reasons': simple_support,
            'oppose_reasons': simple_oppose,
            'amend_reasons': simple_amend,
            'ai_summary': simplified_what_does,
            'tldr': tldr
        }

    async def _simplify_single_section(self, detailed_content: str, section_name: str, bill_title: str) -> str:
        """Simplify a single section to 8th grade level"""
        if not detailed_content:
            return f"Analysis of {section_name} for {bill_title} is being processed."
            
        messages = [
            {
                "role": "system",
                "content": "You are an expert at simplifying complex legislative content to 8th grade reading level while keeping all facts accurate."
            },
            {
                "role": "user", 
                "content": f"""Convert this detailed analysis to 8th grade reading level.

BILL: {bill_title}
SECTION: {section_name}
DETAILED CONTENT: {detailed_content}

REQUIREMENTS:
- Use simple words (no jargon)
- Short sentences (under 20 words)
- Clear, engaging language
- Keep all facts accurate
- Make it interesting for citizens

OUTPUT: Just the simplified text, nothing else."""
            }
        ]

        try:
            simplified = await self.ai_service._make_openai_request(
                messages=messages,
                max_tokens=300,
                temperature=0.3  # Lower temperature for more consistent simplification
            )
            return simplified.strip()
        except Exception as e:
            logger.error(f"Section simplification failed for {section_name}: {e}")
            return f"Simplified analysis of {section_name} for {bill_title} is being processed."

    def _extract_key_points(self, content: str) -> List[str]:
        """Extract key points from simplified content"""
        # Simple extraction - split by periods and take first few sentences
        sentences = content.split('. ')
        return [s.strip() + '.' for s in sentences[:3] if len(s.strip()) > 10]

    def _extract_affected_groups(self, content: str) -> List[str]:
        """Extract affected groups from simplified content"""  
        return ["Citizens", "Taxpayers", "Government agencies"]  # Placeholder

    async def _create_simple_tldr(self, what_does_content: str, bill_title: str) -> str:
        """Create engaging TLDR"""
        messages = [
            {
                "role": "system",
                "content": "You create engaging, simple TLDR sentences for bills that help citizens understand legislation."
            },
            {
                "role": "user",
                "content": f"""Create a single engaging sentence TLDR for this bill.

BILL: {bill_title}  
WHAT IT DOES: {what_does_content}

REQUIREMENTS:
- Single sentence
- 8th grade reading level
- Include an appropriate emoji
- Make it engaging for citizens

OUTPUT: Just the TLDR sentence with emoji."""
            }
        ]

        try:
            tldr = await self.ai_service._make_openai_request(
                messages=messages,
                max_tokens=50,
                temperature=0.7  # Higher temperature for more engaging language
            )
            return tldr.strip()
        except Exception as e:
            logger.error(f"TLDR creation failed: {e}")
            return f"📄 {bill_title} is being analyzed for simple explanation."


def get_unified_bill_processing_service(db: Session) -> UnifiedBillProcessingService:
    """Get unified bill processing service instance"""
    return UnifiedBillProcessingService(db)