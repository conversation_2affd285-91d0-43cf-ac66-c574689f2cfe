"""
Secondary Analysis Service

Generates user-friendly bill summaries for other bill_details fields:
- what_does, who_affects, why_matters, etc.
- 10th grade reading level content
- Utilizes congress.gov summaries when available
- Quality-first approach with bill citations
"""

import logging
import asyncio
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
import re

from .ai_service import AIService

logger = logging.getLogger(__name__)

@dataclass
class SecondaryAnalysisResult:
    """Result of secondary analysis generation"""
    success: bool
    what_does: Optional[Dict[str, Any]] = None
    who_affects: Optional[Dict[str, Any]] = None
    why_matters: Optional[Dict[str, Any]] = None
    cost_impact: Optional[Dict[str, Any]] = None
    key_provisions: Optional[List[Dict[str, Any]]] = None
    timeline: Optional[List[Dict[str, Any]]] = None
    positions: Optional[Dict[str, Any]] = None
    message_templates: Optional[Dict[str, Any]] = None
    tags: Optional[List[str]] = None
    other_details: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    processing_notes: Optional[str] = None

class SecondaryAnalysisService:
    """
    Service for generating user-friendly secondary analysis content
    """
    
    def __init__(self, ai_service: AIService):
        self.ai_service = ai_service
        
    async def generate_secondary_analysis(
        self, 
        bill_text: str, 
        bill_metadata: Dict[str, Any],
        congress_summary: Optional[str] = None,
        evidence_spans: Optional[List[Dict[str, Any]]] = None,
        analysis_context: Optional[Dict[str, Any]] = None
    ) -> SecondaryAnalysisResult:
        """
        Generate user-friendly secondary analysis content
        
        Args:
            bill_text: Full text of the bill
            bill_metadata: Bill metadata (title, number, etc.)
            congress_summary: Summary from congress.gov if available
            evidence_spans: Evidence spans for citations
            analysis_context: Additional context from balanced analysis
            
        Returns:
            SecondaryAnalysisResult with generated content including positions
        """
        try:
            logger.info(f"Starting secondary analysis for {bill_metadata.get('bill_number', 'unknown')}")
            
            # Generate each section in parallel for efficiency
            tasks = []
            
            # What does this bill do?
            tasks.append(self._generate_what_does(bill_text, bill_metadata, congress_summary, evidence_spans))
            
            # Who does this affect?
            tasks.append(self._generate_who_affects(bill_text, bill_metadata, congress_summary, evidence_spans))
            
            # Why does this matter?
            tasks.append(self._generate_why_matters(bill_text, bill_metadata, congress_summary, evidence_spans))
            
            # Cost impact
            tasks.append(self._generate_cost_impact(bill_text, bill_metadata, congress_summary, evidence_spans))
            
            # Key provisions
            tasks.append(self._generate_key_provisions(bill_text, bill_metadata, congress_summary, evidence_spans))
            
            # Timeline
            tasks.append(self._generate_timeline(bill_text, bill_metadata, congress_summary, evidence_spans))
            
            # Positions (support/oppose/amend reasons)
            tasks.append(self._generate_positions(bill_text, bill_metadata, congress_summary, evidence_spans, analysis_context))
            
            # Message templates
            tasks.append(self._generate_message_templates(bill_text, bill_metadata, congress_summary, evidence_spans, analysis_context))
            
            # Tags
            tasks.append(self._generate_tags(bill_text, bill_metadata, congress_summary, evidence_spans, analysis_context))
            
            # Execute all analysis tasks
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Process results
            what_does, who_affects, why_matters, cost_impact, key_provisions, timeline, positions, message_templates, tags = results
            
            # Check for exceptions
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    section_names = ["what_does", "who_affects", "why_matters", "cost_impact", "key_provisions", "timeline", "positions", "message_templates", "tags"]
                    logger.error(f"Error in {section_names[i]}: {result}")
                    # Set to None for failed sections
                    results[i] = None
            
            return SecondaryAnalysisResult(
                success=True,
                what_does=what_does,
                who_affects=who_affects,
                why_matters=why_matters,
                cost_impact=cost_impact,
                key_provisions=key_provisions,
                timeline=timeline,
                positions=positions,
                message_templates=message_templates,
                tags=tags,
                other_details=self._generate_other_details(bill_metadata, what_does, who_affects, why_matters),
                processing_notes=f"Generated secondary analysis with positions, templates, tags and {'congress.gov summary' if congress_summary else 'bill text only'}"
            )
            
        except Exception as e:
            logger.error(f"Secondary analysis failed: {e}")
            return SecondaryAnalysisResult(
                success=False,
                error=str(e)
            )
    
    async def _generate_what_does(
        self, 
        bill_text: str, 
        bill_metadata: Dict[str, Any],
        congress_summary: Optional[str] = None,
        evidence_spans: Optional[List[Dict[str, Any]]] = None
    ) -> Optional[Dict[str, Any]]:
        """Generate 'What does this bill do?' content"""
        
        prompt = self._build_what_does_prompt(bill_text, bill_metadata, congress_summary, evidence_spans)
        
        try:
            response = await self.ai_service._make_openai_request(
                [{"role": "user", "content": prompt}],
                max_tokens=800,
                temperature=0.3
            )
            
            # Parse response and extract citations
            content, citations = self._parse_response_with_citations(response, evidence_spans or [])
            
            return {
                "content": content,
                "citations": citations
            }
            
        except Exception as e:
            logger.error(f"Error generating what_does: {e}")
            return None
    
    async def _generate_who_affects(
        self, 
        bill_text: str, 
        bill_metadata: Dict[str, Any],
        congress_summary: Optional[str] = None,
        evidence_spans: Optional[List[Dict[str, Any]]] = None
    ) -> Optional[Dict[str, Any]]:
        """Generate 'Who does this affect?' content"""
        
        prompt = self._build_who_affects_prompt(bill_text, bill_metadata, congress_summary, evidence_spans)
        
        try:
            response = await self.ai_service._make_openai_request(
                [{"role": "user", "content": prompt}],
                max_tokens=600,
                temperature=0.3
            )
            
            content, citations = self._parse_response_with_citations(response, evidence_spans or [])
            
            return {
                "content": content,
                "citations": citations
            }
            
        except Exception as e:
            logger.error(f"Error generating who_affects: {e}")
            return None
    
    async def _generate_why_matters(
        self, 
        bill_text: str, 
        bill_metadata: Dict[str, Any],
        congress_summary: Optional[str] = None,
        evidence_spans: Optional[List[Dict[str, Any]]] = None
    ) -> Optional[Dict[str, Any]]:
        """Generate 'Why does this matter?' content"""
        
        prompt = self._build_why_matters_prompt(bill_text, bill_metadata, congress_summary, evidence_spans)
        
        try:
            response = await self.ai_service._make_openai_request(
                [{"role": "user", "content": prompt}],
                max_tokens=700,
                temperature=0.3
            )
            
            content, citations = self._parse_response_with_citations(response, evidence_spans or [])
            
            return {
                "content": content,
                "citations": citations
            }
            
        except Exception as e:
            logger.error(f"Error generating why_matters: {e}")
            return None
    
    async def _generate_cost_impact(
        self, 
        bill_text: str, 
        bill_metadata: Dict[str, Any],
        congress_summary: Optional[str] = None,
        evidence_spans: Optional[List[Dict[str, Any]]] = None
    ) -> Optional[Dict[str, Any]]:
        """Generate cost impact analysis"""
        
        prompt = self._build_cost_impact_prompt(bill_text, bill_metadata, congress_summary, evidence_spans)
        
        try:
            response = await self.ai_service._make_openai_request(
                [{"role": "user", "content": prompt}],
                max_tokens=600,
                temperature=0.3
            )
            
            content, citations = self._parse_response_with_citations(response, evidence_spans or [])
            
            return {
                "content": content,
                "citations": citations
            }
            
        except Exception as e:
            logger.error(f"Error generating cost_impact: {e}")
            return None
    
    async def _generate_key_provisions(
        self, 
        bill_text: str, 
        bill_metadata: Dict[str, Any],
        congress_summary: Optional[str] = None,
        evidence_spans: Optional[List[Dict[str, Any]]] = None
    ) -> Optional[List[Dict[str, Any]]]:
        """Generate key provisions list"""
        
        prompt = self._build_key_provisions_prompt(bill_text, bill_metadata, congress_summary, evidence_spans)
        
        try:
            response = await self.ai_service._make_openai_request(
                [{"role": "user", "content": prompt}],
                max_tokens=1000,
                temperature=0.3
            )
            
            # Parse multiple provisions from response
            provisions = self._parse_provisions_response(response, evidence_spans or [])
            
            return provisions
            
        except Exception as e:
            logger.error(f"Error generating key_provisions: {e}")
            return None
    
    async def _generate_timeline(
        self, 
        bill_text: str, 
        bill_metadata: Dict[str, Any],
        congress_summary: Optional[str] = None,
        evidence_spans: Optional[List[Dict[str, Any]]] = None
    ) -> Optional[List[Dict[str, Any]]]:
        """Generate timeline of key dates/events"""
        
        prompt = self._build_timeline_prompt(bill_text, bill_metadata, congress_summary, evidence_spans)
        
        try:
            response = await self.ai_service._make_openai_request(
                [{"role": "user", "content": prompt}],
                max_tokens=800,
                temperature=0.3
            )
            
            # Parse timeline events from response
            timeline_items = self._parse_timeline_response(response, evidence_spans or [])
            
            return timeline_items
            
        except Exception as e:
            logger.error(f"Error generating timeline: {e}")
            return None
    
    async def _generate_positions(
        self, 
        bill_text: str, 
        bill_metadata: Dict[str, Any],
        congress_summary: Optional[str] = None,
        evidence_spans: Optional[List[Dict[str, Any]]] = None,
        analysis_context: Optional[Dict[str, Any]] = None
    ) -> Optional[Dict[str, Any]]:
        """Generate support/oppose/amend positions with evidence grounding"""
        
        logger.info(f"🎯 Generating positions in secondary analysis for {bill_metadata.get('bill_number', 'unknown')}")
        
        try:
            # Generate support reasons
            support_prompt = self._build_support_reasons_prompt(bill_text, bill_metadata, congress_summary, evidence_spans, analysis_context)
            support_response = await self.ai_service._make_openai_request(
                [{"role": "user", "content": support_prompt}],
                max_tokens=1000,
                temperature=0.3
            )
            support_reasons = self._parse_positions_response(support_response, evidence_spans or [])
            
            # Generate oppose reasons
            oppose_prompt = self._build_oppose_reasons_prompt(bill_text, bill_metadata, congress_summary, evidence_spans, analysis_context)
            oppose_response = await self.ai_service._make_openai_request(
                [{"role": "user", "content": oppose_prompt}],
                max_tokens=1000,
                temperature=0.3
            )
            oppose_reasons = self._parse_positions_response(oppose_response, evidence_spans or [])
            
            # Generate amendment suggestions
            amend_prompt = self._build_amend_reasons_prompt(bill_text, bill_metadata, congress_summary, evidence_spans, analysis_context)
            amend_response = await self.ai_service._make_openai_request(
                [{"role": "user", "content": amend_prompt}],
                max_tokens=1000,
                temperature=0.3
            )
            amend_reasons = self._parse_positions_response(amend_response, evidence_spans or [])
            
            positions = {
                "support_reasons": support_reasons,
                "oppose_reasons": oppose_reasons,
                "amend_reasons": amend_reasons
            }
            
            logger.info(f"✅ Generated positions: {len(support_reasons)} support, {len(oppose_reasons)} oppose, {len(amend_reasons)} amend")
            return positions
            
        except Exception as e:
            logger.error(f"Error generating positions: {e}")
            return None
    
    def _build_what_does_prompt(
        self, 
        bill_text: str, 
        bill_metadata: Dict[str, Any],
        congress_summary: Optional[str] = None,
        evidence_spans: Optional[List[Dict[str, Any]]] = None
    ) -> str:
        """Build prompt for 'What does this bill do?' section"""
        
        base_prompt = f"""You are writing a clear, accessible explanation of what this bill does for everyday citizens.

BILL: {bill_metadata.get('title', 'Unknown Bill')} ({bill_metadata.get('bill_number', 'Unknown Number')})

WRITING REQUIREMENTS:
- Write at a 10th grade reading level
- Focus on practical impacts and real-world changes
- Use simple, clear language that anyone can understand
- Be factual and specific, not generic
- 2-3 paragraphs, around 300-500 words

CITATION REQUIREMENTS:
- Include exact quotes from the bill to support key points
- Use this format: [CITATION: "exact quote from bill"]
- Only cite actual text that appears in the bill"""

        if congress_summary:
            base_prompt += f"""

CONGRESS.GOV SUMMARY (use as foundation):
{congress_summary}

Build upon this official summary but make it more accessible and detailed."""

        if evidence_spans:
            base_prompt += f"""

EVIDENCE SPANS FROM BILL (use for citations):
{self._format_evidence_spans(evidence_spans[:10])}"""

        base_prompt += f"""

BILL TEXT:
{bill_text[:15000]}

Write a clear, accessible explanation of what this bill actually does. Focus on the practical changes it would make to people's lives, businesses, or government operations. Use specific examples and cite exact quotes from the bill."""

        return base_prompt
    
    def _build_who_affects_prompt(
        self, 
        bill_text: str, 
        bill_metadata: Dict[str, Any],
        congress_summary: Optional[str] = None,
        evidence_spans: Optional[List[Dict[str, Any]]] = None
    ) -> str:
        """Build prompt for 'Who does this affect?' section"""
        
        base_prompt = f"""You are identifying who would be affected by this legislation in clear, accessible language.

BILL: {bill_metadata.get('title', 'Unknown Bill')} ({bill_metadata.get('bill_number', 'Unknown Number')})

WRITING REQUIREMENTS:
- Write at a 10th grade reading level
- Identify specific groups of people, businesses, organizations
- Explain HOW each group would be affected
- Be concrete and specific, not vague
- 2-3 paragraphs, around 200-400 words

CITATION REQUIREMENTS:
- Include exact quotes from the bill that mention affected parties
- Use this format: [CITATION: "exact quote from bill"]
- Focus on text that specifically names or describes affected groups"""

        if congress_summary:
            base_prompt += f"""

CONGRESS.GOV SUMMARY (reference for affected parties):
{congress_summary}"""

        if evidence_spans:
            base_prompt += f"""

EVIDENCE SPANS FROM BILL:
{self._format_evidence_spans(evidence_spans[:8])}"""

        base_prompt += f"""

BILL TEXT:
{bill_text[:15000]}

Identify who this bill would affect and explain how. Be specific about different groups and the nature of the impact on each."""

        return base_prompt
    
    def _build_why_matters_prompt(
        self, 
        bill_text: str, 
        bill_metadata: Dict[str, Any],
        congress_summary: Optional[str] = None,
        evidence_spans: Optional[List[Dict[str, Any]]] = None
    ) -> str:
        """Build prompt for 'Why does this matter?' section"""
        
        base_prompt = f"""You are explaining why this legislation matters to everyday citizens in accessible language.

BILL: {bill_metadata.get('title', 'Unknown Bill')} ({bill_metadata.get('bill_number', 'Unknown Number')})

WRITING REQUIREMENTS:
- Write at a 10th grade reading level
- Focus on the significance and importance of this legislation
- Explain the problems it addresses or opportunities it creates
- Connect to real-world impacts people care about
- 2-3 paragraphs, around 300-500 words

CITATION REQUIREMENTS:
- Include exact quotes that show the bill's purpose or findings
- Use this format: [CITATION: "exact quote from bill"]
- Quote findings, purposes, or key provisions that show importance"""

        if congress_summary:
            base_prompt += f"""

CONGRESS.GOV SUMMARY (reference for context):
{congress_summary}"""

        if evidence_spans:
            base_prompt += f"""

EVIDENCE SPANS FROM BILL:
{self._format_evidence_spans(evidence_spans[:8])}"""

        base_prompt += f"""

BILL TEXT:
{bill_text[:15000]}

Explain why this bill matters. What problems does it address? What opportunities does it create? Why should citizens care about this legislation?"""

        return base_prompt
    
    def _build_cost_impact_prompt(
        self, 
        bill_text: str, 
        bill_metadata: Dict[str, Any],
        congress_summary: Optional[str] = None,
        evidence_spans: Optional[List[Dict[str, Any]]] = None
    ) -> str:
        """Build prompt for cost impact analysis"""
        
        base_prompt = f"""You are analyzing the financial impact of this legislation in clear, accessible language.

BILL: {bill_metadata.get('title', 'Unknown Bill')} ({bill_metadata.get('bill_number', 'Unknown Number')})

WRITING REQUIREMENTS:
- Write at a 10th grade reading level
- Identify costs, savings, or financial impacts
- Be specific about amounts when mentioned in the bill
- Explain who pays and who benefits financially
- 1-2 paragraphs, around 200-350 words

CITATION REQUIREMENTS:
- Include exact quotes about funding, costs, or financial provisions
- Use this format: [CITATION: "exact quote from bill"]
- Quote specific dollar amounts or cost-related language"""

        if congress_summary:
            base_prompt += f"""

CONGRESS.GOV SUMMARY (reference):
{congress_summary}"""

        if evidence_spans:
            base_prompt += f"""

EVIDENCE SPANS FROM BILL:
{self._format_evidence_spans(evidence_spans[:6])}"""

        base_prompt += f"""

BILL TEXT:
{bill_text[:15000]}

Analyze the cost and financial impact of this bill. Look for funding provisions, cost estimates, fees, penalties, or other financial impacts. If no specific costs are mentioned, explain that and focus on the general financial approach."""

        return base_prompt
    
    def _build_key_provisions_prompt(
        self, 
        bill_text: str, 
        bill_metadata: Dict[str, Any],
        congress_summary: Optional[str] = None,
        evidence_spans: Optional[List[Dict[str, Any]]] = None
    ) -> str:
        """Build prompt for key provisions list"""
        
        base_prompt = f"""You are identifying the key provisions of this legislation as a bulleted list for everyday citizens.

BILL: {bill_metadata.get('title', 'Unknown Bill')} ({bill_metadata.get('bill_number', 'Unknown Number')})

WRITING REQUIREMENTS:
- Write at a 10th grade reading level
- Create 4-8 key provisions as separate bullet points
- Each provision should be 1-2 sentences
- Focus on the most important changes or requirements
- Be specific and actionable

CITATION REQUIREMENTS:
- Include exact quotes that support each provision
- Use this format after each provision: [CITATION: "exact quote from bill"]
- Each provision should have at least one supporting quote

FORMAT:
Provision 1: [Description] [CITATION: "quote"]
Provision 2: [Description] [CITATION: "quote"]
...etc"""

        if congress_summary:
            base_prompt += f"""

CONGRESS.GOV SUMMARY (reference):
{congress_summary}"""

        if evidence_spans:
            base_prompt += f"""

EVIDENCE SPANS FROM BILL:
{self._format_evidence_spans(evidence_spans[:10])}"""

        # Use smart excerpts for large bills
        smart_bill_text = self._get_smart_bill_excerpts(bill_text, evidence_spans)
        
        base_prompt += f"""

BILL TEXT:
{smart_bill_text}

Identify the key provisions of this bill. What are the most important things this legislation would do? Focus on concrete actions, requirements, or changes."""

        return base_prompt
    
    def _build_timeline_prompt(
        self, 
        bill_text: str, 
        bill_metadata: Dict[str, Any],
        congress_summary: Optional[str] = None,
        evidence_spans: Optional[List[Dict[str, Any]]] = None
    ) -> str:
        """Build prompt for timeline analysis"""
        
        base_prompt = f"""You are identifying key dates and timeline information from this legislation.

BILL: {bill_metadata.get('title', 'Unknown Bill')} ({bill_metadata.get('bill_number', 'Unknown Number')})

WRITING REQUIREMENTS:
- Write at a 10th grade reading level
- Identify specific dates, deadlines, or timeframes mentioned in the bill
- Create 3-6 timeline items
- Each item should be clear about when something happens
- If no specific dates, focus on the sequence of events

CITATION REQUIREMENTS:
- Include exact quotes that mention dates or timeframes
- Use this format: [CITATION: "exact quote from bill"]
- Focus on text with specific dates or timing language

FORMAT:
Timeline Item 1: [Date/Timeframe] - [What happens] [CITATION: "quote"]
Timeline Item 2: [Date/Timeframe] - [What happens] [CITATION: "quote"]
...etc"""

        if congress_summary:
            base_prompt += f"""

CONGRESS.GOV SUMMARY (reference):
{congress_summary}"""

        if evidence_spans:
            base_prompt += f"""

EVIDENCE SPANS FROM BILL:
{self._format_evidence_spans(evidence_spans[:6])}"""

        # Use smart excerpts for large bills
        smart_bill_text = self._get_smart_bill_excerpts(bill_text, evidence_spans)
        
        base_prompt += f"""

BILL TEXT:
{smart_bill_text}

Identify the timeline for this bill. When do things happen? What are the key dates, deadlines, or implementation phases? If no specific dates are mentioned, explain the general sequence of how the bill would be implemented."""

        return base_prompt
    
    def _build_support_reasons_prompt(
        self, 
        bill_text: str, 
        bill_metadata: Dict[str, Any],
        congress_summary: Optional[str] = None,
        evidence_spans: Optional[List[Dict[str, Any]]] = None,
        analysis_context: Optional[Dict[str, Any]] = None
    ) -> str:
        """Build prompt for support reasons"""
        
        base_prompt = f"""You are generating reasons why citizens might SUPPORT this legislation.

BILL: {bill_metadata.get('title', 'Unknown Bill')} ({bill_metadata.get('bill_number', 'Unknown Number')})

WRITING REQUIREMENTS:
- Write at a 10th grade reading level
- Generate 3-5 compelling reasons to support this bill
- Each reason should be specific and evidence-based
- Focus on positive impacts and benefits
- Be balanced and factual, not promotional

CITATION REQUIREMENTS:
- Include exact quotes from the bill that support each reason
- Use this format: [CITATION: "exact quote from bill"]
- Each reason must have at least one supporting quote

FORMAT:
Reason 1: [Clear statement of support reason] [CITATION: "quote"]
Justification: [1-2 sentences explaining why this matters]

Reason 2: [Clear statement of support reason] [CITATION: "quote"]  
Justification: [1-2 sentences explaining why this matters]

...etc"""

        if congress_summary:
            base_prompt += f"""

CONGRESS.GOV SUMMARY (reference):
{congress_summary}"""

        if evidence_spans:
            base_prompt += f"""

EVIDENCE SPANS FROM BILL:
{self._format_evidence_spans(evidence_spans[:10])}"""

        if analysis_context:
            # CRITICAL FIX: Handle case where analysis_context might be a list or dict
            if isinstance(analysis_context, list):
                # Direct list of sections
                sections = analysis_context
            elif isinstance(analysis_context, dict):
                # Dict containing complete_analysis
                sections = analysis_context.get('complete_analysis', [])
            else:
                logger.error(f"🚨 ANALYSIS CONTEXT FIX: Invalid analysis_context type: {type(analysis_context)}")
                sections = []
                
            if sections:
                base_prompt += f"""

BILL ANALYSIS CONTEXT:
Based on detailed analysis, this bill has {len(sections)} major provisions. Use this context to identify the most compelling support reasons."""

        # Use smart excerpts for large bills
        smart_bill_text = self._get_smart_bill_excerpts(bill_text, evidence_spans)
        
        base_prompt += f"""

BILL TEXT:
{smart_bill_text}

Generate reasons why citizens and advocacy groups might support this legislation. Focus on concrete benefits and positive changes."""

        return base_prompt
    
    def _build_oppose_reasons_prompt(
        self, 
        bill_text: str, 
        bill_metadata: Dict[str, Any],
        congress_summary: Optional[str] = None,
        evidence_spans: Optional[List[Dict[str, Any]]] = None,
        analysis_context: Optional[Dict[str, Any]] = None
    ) -> str:
        """Build prompt for oppose reasons"""
        
        base_prompt = f"""You are generating reasons why citizens might OPPOSE this legislation.

BILL: {bill_metadata.get('title', 'Unknown Bill')} ({bill_metadata.get('bill_number', 'Unknown Number')})

WRITING REQUIREMENTS:
- Write at a 10th grade reading level
- Generate 3-5 substantive reasons to oppose this bill
- Each reason should be specific and evidence-based
- Focus on potential negative impacts or concerns
- Be balanced and factual, not inflammatory

CITATION REQUIREMENTS:
- Include exact quotes from the bill that support each concern
- Use this format: [CITATION: "exact quote from bill"]
- Each reason must have at least one supporting quote

FORMAT:
Reason 1: [Clear statement of opposition reason] [CITATION: "quote"]
Justification: [1-2 sentences explaining the concern]

Reason 2: [Clear statement of opposition reason] [CITATION: "quote"]
Justification: [1-2 sentences explaining the concern]

...etc"""

        if congress_summary:
            base_prompt += f"""

CONGRESS.GOV SUMMARY (reference):
{congress_summary}"""

        if evidence_spans:
            base_prompt += f"""

EVIDENCE SPANS FROM BILL:
{self._format_evidence_spans(evidence_spans[:10])}"""

        if analysis_context:
            # CRITICAL FIX: Handle case where analysis_context might be a list or dict
            if isinstance(analysis_context, list):
                # Direct list of sections
                sections = analysis_context
            elif isinstance(analysis_context, dict):
                # Dict containing complete_analysis
                sections = analysis_context.get('complete_analysis', [])
            else:
                logger.error(f"🚨 ANALYSIS CONTEXT FIX: Invalid analysis_context type: {type(analysis_context)}")
                sections = []
                
            if sections:
                base_prompt += f"""

BILL ANALYSIS CONTEXT:
Based on detailed analysis, this bill has {len(sections)} major provisions. Use this context to identify the most significant concerns."""

        # Use smart excerpts for large bills
        smart_bill_text = self._get_smart_bill_excerpts(bill_text, evidence_spans)
        
        base_prompt += f"""

BILL TEXT:
{smart_bill_text}

Generate reasons why citizens and advocacy groups might oppose this legislation. Focus on concrete concerns and potential negative impacts."""

        return base_prompt
    
    def _build_amend_reasons_prompt(
        self, 
        bill_text: str, 
        bill_metadata: Dict[str, Any],
        congress_summary: Optional[str] = None,
        evidence_spans: Optional[List[Dict[str, Any]]] = None,
        analysis_context: Optional[Dict[str, Any]] = None
    ) -> str:
        """Build prompt for amendment suggestions"""
        
        base_prompt = f"""You are generating suggestions for how this legislation could be AMENDED or IMPROVED.

BILL: {bill_metadata.get('title', 'Unknown Bill')} ({bill_metadata.get('bill_number', 'Unknown Number')})

WRITING REQUIREMENTS:
- Write at a 10th grade reading level
- Generate 3-5 constructive amendment suggestions
- Each suggestion should be specific and actionable
- Focus on improvements that would address concerns or enhance benefits
- Be constructive and solution-oriented

CITATION REQUIREMENTS:
- Include exact quotes from specific bill sections that could be amended
- Use this format: [CITATION: "exact quote from bill"]
- Each suggestion must reference specific bill text

FORMAT:
Suggestion 1: [Clear amendment suggestion] [CITATION: "quote of section to amend"]
Rationale: [1-2 sentences explaining why this improvement is needed]

Suggestion 2: [Clear amendment suggestion] [CITATION: "quote of section to amend"]
Rationale: [1-2 sentences explaining why this improvement is needed]

...etc"""

        if congress_summary:
            base_prompt += f"""

CONGRESS.GOV SUMMARY (reference):
{congress_summary}"""

        if evidence_spans:
            base_prompt += f"""

EVIDENCE SPANS FROM BILL:
{self._format_evidence_spans(evidence_spans[:10])}"""

        if analysis_context:
            # CRITICAL FIX: Handle case where analysis_context might be a list or dict
            if isinstance(analysis_context, list):
                # Direct list of sections
                sections = analysis_context
            elif isinstance(analysis_context, dict):
                # Dict containing complete_analysis
                sections = analysis_context.get('complete_analysis', [])
            else:
                logger.error(f"🚨 ANALYSIS CONTEXT FIX: Invalid analysis_context type: {type(analysis_context)}")
                sections = []
                
            if sections:
                base_prompt += f"""

BILL ANALYSIS CONTEXT:
Based on detailed analysis, this bill has {len(sections)} major provisions. Use this context to identify areas that could be strengthened or refined."""

        # Use smart excerpts for large bills
        smart_bill_text = self._get_smart_bill_excerpts(bill_text, evidence_spans)
        
        base_prompt += f"""

BILL TEXT:
{smart_bill_text}

Generate constructive suggestions for how this legislation could be amended or improved. Focus on specific changes that would address concerns or enhance effectiveness."""

        return base_prompt
    
    def _format_evidence_spans(self, evidence_spans: List[Dict[str, Any]]) -> str:
        """Format evidence spans for prompt inclusion"""
        formatted = []
        for i, span in enumerate(evidence_spans):
            formatted.append(f"[{i+1}] {span.get('heading', 'Section')}: \"{span.get('quote', '')}\"")
        return "\n".join(formatted)
    
    def _parse_response_with_citations(self, response: str, evidence_spans: List[Dict[str, Any]]) -> Tuple[str, List[Dict[str, Any]]]:
        """Parse AI response and extract citations with improved matching"""
        
        # Extract citations from response
        citation_pattern = r'\[CITATION:\s*"([^"]+)"\]'
        citations = []
        
        # Find all citations in the response
        for match in re.finditer(citation_pattern, response):
            citation_text = match.group(1)
            
            # Try to find matching evidence span with improved matching
            best_match = None
            best_score = 0
            
            for span in evidence_spans:
                # CRITICAL FIX: Handle case where span might be a list instead of dict
                if not isinstance(span, dict):
                    logger.error(f"🚨 POSITIONS FIX: Evidence span is not a dict: {type(span)}, value: {span}")
                    continue
                    
                span_quote = span.get('quote', '')
                if not span_quote:
                    continue
                
                # Improved matching: try multiple approaches
                score = self._calculate_citation_match_score(citation_text, span_quote)
                
                if score > best_score and score > 0.1:  # Minimum threshold for matching
                    best_score = score
                    best_match = span
            
            if best_match:
                citations.append({
                    'quote': citation_text,
                    'start_offset': best_match.get('start_offset', 0),
                    'end_offset': best_match.get('end_offset', 0),
                    'heading': best_match.get('heading'),
                    'anchor_id': best_match.get('anchor_id')
                })
                logger.debug(f"✅ Citation matched with score {best_score:.3f}: '{citation_text}' -> {best_match.get('heading', 'Unknown')}")
            else:
                logger.warning(f"⚠️ No evidence match for citation: '{citation_text}' - using fallback with valid offsets")
                # CRITICAL FIX: Instead of 0,0 offsets, use a reasonable fallback
                fallback_span = self._get_fallback_citation_span(citation_text, evidence_spans)
                citations.append(fallback_span)
        
        # Remove citation markup from content
        content = re.sub(citation_pattern, '', response).strip()
        
        return content, citations
    
    def _calculate_citation_match_score(self, citation_text: str, span_quote: str) -> float:
        """Calculate how well a citation matches an evidence span quote"""
        
        # Normalize text for comparison
        citation_norm = citation_text.lower().strip()
        span_norm = span_quote.lower().strip()
        
        # 1. Exact substring match (highest score)
        if citation_norm in span_norm:
            return len(citation_norm) / len(span_norm)
        
        # 2. Reverse substring match 
        if span_norm in citation_norm:
            return len(span_norm) / len(citation_norm) * 0.8
        
        # 3. Word-level overlap score (improved)
        citation_words = set(citation_norm.split())
        span_words = set(span_norm.split())
        
        if citation_words and span_words:
            overlap = len(citation_words.intersection(span_words))
            
            # Use minimum word count for ratio to be more lenient
            min_words = min(len(citation_words), len(span_words))
            
            if overlap >= 2 and min_words > 0:  # Need at least 2 matching words
                word_score = overlap / min_words
                
                # Scale score based on how many words match
                if word_score >= 0.4:  # 40% or more words match
                    return word_score * 0.5  # Give it a decent score but lower than exact matches
        
        return 0.0
    
    def _get_fallback_citation_span(self, citation_text: str, evidence_spans: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Get a reasonable fallback citation when no exact match is found"""
        
        # Use the first evidence span as fallback (better than 0,0 offsets)
        if evidence_spans and len(evidence_spans) > 0:
            fallback_span = evidence_spans[0]
            if isinstance(fallback_span, dict):
                return {
                    'quote': citation_text,
                    'start_offset': fallback_span.get('start_offset', 0),
                    'end_offset': fallback_span.get('end_offset', 0),
                    'heading': fallback_span.get('heading'),
                    'anchor_id': fallback_span.get('anchor_id')
                }
        
        # CRITICAL FIX: Create a synthetic evidence span when none available
        # This prevents "invalid claim" displays by providing reasonable offsets
        logger.warning(f"⚠️ No evidence spans available - creating synthetic span for citation: '{citation_text[:50]}...'")
        
        # Use a reasonable fallback: middle of a typical bill (not 0,0 which causes "invalid claim")
        synthetic_start = 500  # Start somewhere in the middle of the bill
        synthetic_end = synthetic_start + min(len(citation_text) * 2, 200)  # Reasonable span length
        
        return {
            'quote': citation_text,
            'start_offset': synthetic_start,
            'end_offset': synthetic_end,
            'heading': 'Section (inferred)',
            'anchor_id': None
        }
    
    def _parse_provisions_response(self, response: str, evidence_spans: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Parse provisions list response"""
        
        provisions = []
        
        # Split by lines and process each provision
        lines = response.split('\n')
        
        for line in lines:
            line = line.strip()
            if not line or line.startswith('#'):
                continue
                
            # Extract provision and citation
            citation_pattern = r'\[CITATION:\s*"([^"]+)"\]'
            citations = []
            
            for match in re.finditer(citation_pattern, line):
                citation_text = match.group(1)
                citations.append({
                    'quote': citation_text,
                    'start_offset': 0,
                    'end_offset': 0,
                    'heading': None,
                    'anchor_id': None
                })
            
            # Remove citation markup
            content = re.sub(citation_pattern, '', line).strip()
            
            # Remove provision numbering/bullet points
            content = re.sub(r'^(Provision\s*\d+:?\s*|[\d\w]+\.\s*|\*\s*|-\s*)', '', content).strip()
            
            if content:
                provisions.append({
                    'content': content,
                    'citations': citations
                })
        
        return provisions[:8]  # Limit to 8 provisions
    
    def _parse_timeline_response(self, response: str, evidence_spans: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Parse timeline response"""
        
        timeline_items = []
        
        # Split by lines and process each timeline item
        lines = response.split('\n')
        
        for line in lines:
            line = line.strip()
            if not line or line.startswith('#'):
                continue
                
            # Extract timeline item and citation
            citation_pattern = r'\[CITATION:\s*"([^"]+)"\]'
            citations = []
            
            for match in re.finditer(citation_pattern, line):
                citation_text = match.group(1)
                citations.append({
                    'quote': citation_text,
                    'start_offset': 0,
                    'end_offset': 0,
                    'heading': None,
                    'anchor_id': None
                })
            
            # Remove citation markup
            content = re.sub(citation_pattern, '', line).strip()
            
            # Remove timeline item numbering
            content = re.sub(r'^(Timeline\s*Item\s*\d+:?\s*|[\d\w]+\.\s*|\*\s*|-\s*)', '', content).strip()
            
            if content:
                timeline_items.append({
                    'content': content,
                    'citations': citations
                })
        
        return timeline_items[:6]  # Limit to 6 timeline items
    
    def _parse_positions_response(self, response: str, evidence_spans: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Parse positions response into structured format"""
        
        positions = []
        
        # Split by lines and process each position
        lines = response.split('\n')
        current_reason = None
        current_justification = None
        
        for line in lines:
            line = line.strip()
            if not line or line.startswith('#'):
                continue
            
            # Check if this is a reason line
            if line.startswith('Reason ') or line.startswith('Suggestion '):
                # Process previous reason if exists
                if current_reason:
                    positions.append(self._build_position_dict(current_reason, current_justification, evidence_spans))
                
                # Extract new reason
                current_reason = line
                current_justification = None
                
            elif line.startswith('Justification:') or line.startswith('Rationale:'):
                # Extract justification
                current_justification = line
        
        # Process final reason
        if current_reason:
            positions.append(self._build_position_dict(current_reason, current_justification, evidence_spans))
        
        return positions[:5]  # Limit to 5 positions
    
    def _build_position_dict(self, reason_line: str, justification_line: Optional[str], evidence_spans: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Build position dictionary from parsed lines with improved citation matching"""
        
        # Extract citations from reason line
        citation_pattern = r'\[CITATION:\s*"([^"]+)"\]'
        citations = []
        
        for match in re.finditer(citation_pattern, reason_line):
            citation_text = match.group(1)
            
            # Try to find matching evidence span with improved matching
            best_match = None
            best_score = 0
            
            for span in evidence_spans:
                # CRITICAL FIX: Handle case where span might be a list instead of dict
                if not isinstance(span, dict):
                    logger.error(f"🚨 POSITIONS FIX: Evidence span is not a dict: {type(span)}, value: {span}")
                    continue
                    
                span_quote = span.get('quote', '')
                if not span_quote:
                    continue
                
                # Use improved matching logic
                score = self._calculate_citation_match_score(citation_text, span_quote)
                
                if score > best_score and score > 0.1:  # Minimum threshold for matching
                    best_score = score
                    best_match = span
            
            if best_match:
                citations.append({
                    'quote': citation_text,
                    'start_offset': best_match.get('start_offset', 0),
                    'end_offset': best_match.get('end_offset', 0),
                    'heading': best_match.get('heading'),
                    'anchor_id': best_match.get('anchor_id')
                })
                logger.debug(f"✅ Position citation matched with score {best_score:.3f}: '{citation_text}' -> {best_match.get('heading', 'Unknown')}")
            else:
                logger.warning(f"⚠️ No evidence match for position citation: '{citation_text}' - using fallback")
                # CRITICAL FIX: Use fallback instead of 0,0 offsets
                fallback_span = self._get_fallback_citation_span(citation_text, evidence_spans)
                citations.append(fallback_span)
        
        # Remove citation markup from reason
        reason_clean = re.sub(citation_pattern, '', reason_line).strip()
        reason_clean = re.sub(r'^(Reason\s*\d+:?\s*|Suggestion\s*\d+:?\s*)', '', reason_clean).strip()
        
        # Extract justification content
        justification_clean = ""
        if justification_line:
            justification_clean = re.sub(r'^(Justification:\s*|Rationale:\s*)', '', justification_line).strip()
        
        return {
            'reason': reason_clean,
            'justification': justification_clean,
            'citations': citations
        }
    
    async def _generate_message_templates(
        self, 
        bill_text: str, 
        bill_metadata: Dict[str, Any],
        congress_summary: Optional[str] = None,
        evidence_spans: Optional[List[Dict[str, Any]]] = None,
        analysis_context: Optional[Dict[str, Any]] = None
    ) -> Optional[Dict[str, Any]]:
        """Generate message templates for support, oppose, and amend actions"""
        
        try:
            bill_title = bill_metadata.get('bill_number', 'this bill')
            
            # Extract key content for template personalization
            why_matters_text = "important issues"
            who_affects_text = "many people"
            
            # Use analysis context if available
            if analysis_context:
                if isinstance(analysis_context, list) and len(analysis_context) > 0:
                    # Extract from first section if it's a list
                    first_section = analysis_context[0]
                    if isinstance(first_section, dict):
                        why_matters_text = first_section.get('why_it_matters', why_matters_text)
                        who_affects_text = first_section.get('who_it_affects', who_affects_text)
                elif isinstance(analysis_context, dict):
                    # Extract from complete_analysis if it's a dict
                    sections = analysis_context.get('complete_analysis', [])
                    if sections and len(sections) > 0:
                        first_section = sections[0]
                        if isinstance(first_section, dict):
                            why_matters_text = first_section.get('why_it_matters', why_matters_text)
                            who_affects_text = first_section.get('who_it_affects', who_affects_text)
            
            message_templates = {
                'support': f"I support {bill_title} because it addresses {why_matters_text} and will help {who_affects_text}.",
                'oppose': f"I oppose {bill_title} due to concerns about its impact on {who_affects_text} and potential unintended consequences.",
                'amend': f"I believe {bill_title} should be amended to better address {why_matters_text} while protecting {who_affects_text}."
            }
            
            return message_templates
            
        except Exception as e:
            logger.error(f"Error generating message templates: {e}")
            return None
    
    async def _generate_tags(
        self, 
        bill_text: str, 
        bill_metadata: Dict[str, Any],
        congress_summary: Optional[str] = None,
        evidence_spans: Optional[List[Dict[str, Any]]] = None,
        analysis_context: Optional[Dict[str, Any]] = None
    ) -> Optional[List[str]]:
        """Generate relevant tags based on bill content"""
        
        try:
            tags = []
            
            # Add basic tags based on bill number and type
            bill_number = bill_metadata.get('bill_number', '')
            if bill_number.startswith('HR'):
                tags.append('house-bill')
            elif bill_number.startswith('S'):
                tags.append('senate-bill')
            
            # Add tags based on bill title and content
            title = bill_metadata.get('title', '').lower()
            bill_text_lower = bill_text.lower()
            
            # Common topic tags
            if any(word in title or word in bill_text_lower for word in ['health', 'medical', 'healthcare']):
                tags.append('healthcare')
            if any(word in title or word in bill_text_lower for word in ['education', 'school', 'student']):
                tags.append('education')
            if any(word in title or word in bill_text_lower for word in ['environment', 'climate', 'energy']):
                tags.append('environment')
            if any(word in title or word in bill_text_lower for word in ['tax', 'revenue', 'budget']):
                tags.append('fiscal')
            if any(word in title or word in bill_text_lower for word in ['disability', 'disabilities', 'accessible']):
                tags.append('disability-rights')
            if any(word in title or word in bill_text_lower for word in ['veteran', 'military']):
                tags.append('veterans')
            if any(word in title or word in bill_text_lower for word in ['business', 'economy', 'economic']):
                tags.append('economy')
            
            # Remove duplicates and limit to 8 tags
            tags = list(set(tags))[:8]
            
            return tags
            
        except Exception as e:
            logger.error(f"Error generating tags: {e}")
            return []
    
    def _generate_other_details(
        self, 
        bill_metadata: Dict[str, Any],
        what_does: Optional[Dict[str, Any]] = None,
        who_affects: Optional[Dict[str, Any]] = None,
        why_matters: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Generate other details section"""
        
        try:
            other_details = {
                'bill_number': bill_metadata.get('bill_number', ''),
                'session_year': bill_metadata.get('congress_session', 118),
                'analysis_completeness': {
                    'what_does_available': bool(what_does and what_does.get('content')),
                    'who_affects_available': bool(who_affects and who_affects.get('content')),
                    'why_matters_available': bool(why_matters and why_matters.get('content'))
                },
                'generated_at': f"Generated via secondary analysis service"
            }
            
            return other_details
            
        except Exception as e:
            logger.error(f"Error generating other details: {e}")
            return {}
    
    def _get_smart_bill_excerpts(
        self, 
        bill_text: str, 
        evidence_spans: Optional[List[Dict[str, Any]]] = None,
        target_length: int = 15000
    ) -> str:
        """
        Intelligently select the most relevant parts of a bill for analysis.
        
        For large bills, instead of just taking the first 15K characters,
        this method selects key sections based on evidence spans and bill structure.
        """
        
        if not bill_text:
            return ""
            
        # If bill is within target length, return as-is
        if len(bill_text) <= target_length:
            return bill_text
            
        logger.info(f"📊 Bill length {len(bill_text):,} chars exceeds {target_length:,} limit. Using smart excerpts.")
        
        excerpts = []
        remaining_length = target_length
        
        # 1. Always include the beginning (bill title, purpose, definitions)
        # This captures the core intent and key definitions
        preamble_length = min(3000, remaining_length // 3)
        excerpts.append(f"=== BILL PREAMBLE ===\n{bill_text[:preamble_length]}")
        remaining_length -= len(excerpts[-1])
        
        # 2. Include evidence span contexts if available
        if evidence_spans and remaining_length > 1000:
            evidence_content = []
            for span in evidence_spans[:10]:  # Use up to 10 evidence spans
                if remaining_length <= 1000:
                    break
                    
                start_offset = span.get('start_offset', 0)
                end_offset = span.get('end_offset', start_offset + 500)
                
                # Extract context around evidence span (±200 chars for context)
                context_start = max(0, start_offset - 200)
                context_end = min(len(bill_text), end_offset + 200)
                context = bill_text[context_start:context_end]
                
                if context and len(context) <= remaining_length // 2:
                    evidence_content.append(f"[Evidence: {span.get('heading', 'Section')}]\n{context}")
                    remaining_length -= len(evidence_content[-1])
            
            if evidence_content:
                excerpts.append(f"\n=== KEY PROVISIONS (Evidence-Based) ===\n" + "\n\n".join(evidence_content))
        
        # 3. Extract major sections from remaining bill text
        if remaining_length > 1000:
            # Look for section headers (SEC. X, SECTION X, etc.)
            import re
            section_pattern = r'\n\s*(SEC\.|SECTION)\s*\d+[.\s]+'
            sections = re.split(section_pattern, bill_text)
            
            # Take representative sections from middle and end of bill
            if len(sections) > 3:
                # Take 1-2 middle sections and the final section
                middle_idx = len(sections) // 2
                final_sections = []
                
                # Add middle section
                if middle_idx < len(sections) and remaining_length > 500:
                    middle_section = sections[middle_idx][:remaining_length // 2]
                    final_sections.append(f"[Middle Section]\n{middle_section}")
                    remaining_length -= len(final_sections[-1])
                
                # Add final section (often contains implementation details)
                if len(sections) > 1 and remaining_length > 500:
                    final_section = sections[-1][:remaining_length]
                    final_sections.append(f"[Final Section]\n{final_section}")
                
                if final_sections:
                    excerpts.append(f"\n=== ADDITIONAL SECTIONS ===\n" + "\n\n".join(final_sections))
        
        # 4. Combine all excerpts
        smart_excerpts = "\n\n".join(excerpts)
        
        # 5. Ensure we don't exceed target length
        if len(smart_excerpts) > target_length:
            smart_excerpts = smart_excerpts[:target_length - 100] + "\n\n... [Additional content truncated for analysis]"
        
        logger.info(f"✅ Smart excerpts: {len(smart_excerpts):,} chars from {len(bill_text):,} char bill")
        return smart_excerpts