#!/usr/bin/env python3

"""
Debug script to investigate HR670 bill details persistence failure
"""

import asyncio
import sys
import os
sys.path.append('.')

from app.db.database import get_db
from app.models.bill import Bill
from app.services.unified_bill_processing_service import UnifiedBillProcessingService
import logging

# Set up detailed logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def debug_hr670_processing():
    """Debug the HR670 processing issue"""
    print("🔍 DEBUGGING HR670 PROCESSING ISSUE")
    print("=" * 60)
    
    db = next(get_db())
    
    # Get HR670 bill
    bill = db.query(Bill).filter(Bill.bill_number == 'HR670').first()
    if not bill:
        print("❌ HR670 bill not found")
        return
    
    print(f"📋 Bill: {bill.title}")
    print(f"📄 Bill ID: {bill.id}")
    print(f"📄 Full text length: {len(bill.full_text) if bill.full_text else 0} characters")
    print(f"📄 AI processed at: {bill.ai_processed_at}")
    
    # Create unified service
    service = UnifiedBillProcessingService(db)
    
    print("\n🔬 RUNNING ENHANCED PROCESSING TEST")
    
    try:
        # Test the enhanced AI analysis method directly
        bill_metadata = {
            'title': bill.title,
            'summary': bill.summary or '',
            'bill_number': bill.bill_number,
            'congress_session': bill.session_year,
            'type': 'HR',
            'number': '670',
            'bill_id': str(bill.id)
        }
        
        print(f"📊 Testing _run_enhanced_ai_analysis method...")
        enhanced_ai_results, enhanced_details_payload = await service._run_enhanced_ai_analysis(
            bill.full_text, bill_metadata
        )
        
        print(f"✅ Enhanced AI results: {enhanced_ai_results is not None}")
        print(f"✅ Enhanced details payload: {enhanced_details_payload is not None}")
        
        if enhanced_details_payload:
            print(f"📋 Details payload keys: {list(enhanced_details_payload.keys())}")
            if 'hero_summary' in enhanced_details_payload:
                print(f"📝 Hero summary: {enhanced_details_payload['hero_summary'][:100]}...")
            if 'positions' in enhanced_details_payload:
                positions = enhanced_details_payload['positions']
                if positions:
                    pos_count = sum(len(v) if isinstance(v, list) else 0 for v in positions.values())
                    print(f"🎯 Positions count: {pos_count}")
                else:
                    print(f"⚠️ Positions is empty: {positions}")
        else:
            print("❌ Enhanced details payload is None - this is the problem!")
        
        # Test the details service directly if we have a payload
        if enhanced_details_payload and bill and bill.id:
            print(f"\n🔬 TESTING DETAILS SERVICE DIRECTLY")
            try:
                print(f"📊 Calling create_or_update_details...")
                service.details_service.create_or_update_details(bill, bill.full_text, enhanced_details_payload)
                print(f"✅ Details service call succeeded!")
                
                # Check if it was actually saved
                from app.models.bill_details import BillDetails
                details = db.query(BillDetails).filter(BillDetails.bill_id == bill.id).first()
                if details:
                    print(f"✅ Bill details found in database: {details.seo_slug}")
                else:
                    print(f"❌ Bill details not found in database after save attempt")
                    
            except Exception as de:
                print(f"❌ Details service failed: {de}")
                import traceback
                traceback.print_exc()
        
    except Exception as e:
        print(f"💥 ERROR: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        db.close()

if __name__ == "__main__":
    asyncio.run(debug_hr670_processing())