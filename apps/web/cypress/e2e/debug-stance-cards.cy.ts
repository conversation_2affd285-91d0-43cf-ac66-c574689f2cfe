describe('Debug Stance Cards Text Truncation', () => {
  beforeEach(() => {
    cy.visit('http://localhost:3002/bills/34e4c62c-3b2f-404a-a2c3-2f668d4358db/action')
    cy.wait(2000) // Wait for page to load
  })

  it('should display full text in stance cards', () => {
    // Check Support card
    cy.contains('Support').should('be.visible')
    cy.get('button').contains('Support').within(() => {
      cy.get('p').should('contain.text', 'I want this bill to pass as written')
      cy.get('p').invoke('text').then((text) => {
        cy.log('Support card text:', text)
        expect(text.trim()).to.equal('I want this bill to pass as written')
      })
    })

    // Check Oppose card  
    cy.contains('Oppose').should('be.visible')
    cy.get('button').contains('Oppose').within(() => {
      cy.get('p').should('contain.text', "I don't want this bill to pass")
      cy.get('p').invoke('text').then((text) => {
        cy.log('Oppose card text:', text)
        expect(text.trim()).to.equal("I don't want this bill to pass")
      })
    })

    // Check Needs Changes card
    cy.contains('Needs Changes').should('be.visible')
    cy.get('button').contains('Needs Changes').within(() => {
      cy.get('p').should('contain.text', 'I support with modifications')
      cy.get('p').invoke('text').then((text) => {
        cy.log('Needs Changes card text:', text)
        expect(text.trim()).to.equal('I support with modifications')
      })
    })
  })

  it('should inspect CSS properties causing truncation', () => {
    cy.get('button').contains('Support').within(() => {
      cy.get('p').then(($el) => {
        const computedStyle = window.getComputedStyle($el[0])
        cy.log('Support card CSS properties:', {
          width: computedStyle.width,
          height: computedStyle.height,
          overflow: computedStyle.overflow,
          textOverflow: computedStyle.textOverflow,
          whiteSpace: computedStyle.whiteSpace,
          maxWidth: computedStyle.maxWidth,
          position: computedStyle.position,
          display: computedStyle.display,
        })
      })
    })

    // Also check the button container
    cy.get('button').contains('Support').then(($el) => {
      const computedStyle = window.getComputedStyle($el[0])
      cy.log('Support button CSS properties:', {
        width: computedStyle.width,
        height: computedStyle.height,
        overflow: computedStyle.overflow,
        position: computedStyle.position,
        display: computedStyle.display,
      })
    })
  })
})