import {
  HomeIcon,
  DocumentTextIcon,
  UsersIcon,
  ChartBarIcon,
  CpuChipIcon,
  EnvelopeIcon,
  Cog6ToothIcon,
  ServerIcon,
  PresentationChartLineIcon,
  DocumentIcon,
  ClipboardDocumentIcon,
} from '@heroicons/react/24/outline';

import type { NavigationStructure } from '../types/navigation';

/**
 * Admin Navigation Configuration
 * 
 * Defines the complete navigation structure for the admin interface.
 * Items are organized into logical sections for better UX.
 */
export const navigationConfig: NavigationStructure = {
  sections: [
    // Main navigation
    {
      id: 'main',
      items: [
        {
          id: 'dashboard',
          label: 'Dashboard',
          icon: HomeIcon,
          path: '/admin/dashboard',
          description: 'Overview and key metrics',
        },
      ],
    },
    
    // Content management
    {
      id: 'content',
      title: 'Content Management',
      items: [
        {
          id: 'bills',
          label: 'Bills',
          icon: DocumentTextIcon,
          path: '/admin/bills',
          description: 'Manage legislative bills and AI processing',
          children: [
            {
              id: 'bills-browse',
              label: 'Browse Bills',
              icon: DocumentIcon,
              path: '/admin/bills',
            },
            {
              id: 'bills-processing',
              label: 'Processing Queue',
              icon: CpuChipIcon,
              path: '/admin/bills/processing',
              badge: 3, // Example: 3 items in queue
            },
            {
              id: 'bills-analytics',
              label: 'Bill Analytics',
              icon: ChartBarIcon,
              path: '/admin/bills/analytics',
            },
          ],
        },
        {
          id: 'users',
          label: 'Users',
          icon: UsersIcon,
          path: '/admin/users',
          description: 'User management and analytics',
          children: [
            {
              id: 'users-directory',
              label: 'User Directory',
              icon: UsersIcon,
              path: '/admin/users',
            },
            {
              id: 'users-analytics',
              label: 'User Analytics',
              icon: PresentationChartLineIcon,
              path: '/admin/users/analytics',
            },
            {
              id: 'users-support',
              label: 'Support Tools',
              icon: Cog6ToothIcon,
              path: '/admin/users/support',
            },
          ],
        },
      ],
    },
    
    // Operations
    {
      id: 'operations',
      title: 'Operations',
      items: [
        {
          id: 'ai-cost',
          label: 'AI & Cost Management',
          icon: CpuChipIcon,
          path: '/admin/ai-cost',
          description: 'Monitor AI usage and costs',
          featured: true,
        },
        {
          id: 'contact',
          label: 'Contact Messages',
          icon: EnvelopeIcon,
          path: '/admin/contact',
          description: 'Customer support messages',
          badge: 5, // Example: 5 unread messages
        },
        {
          id: 'analytics',
          label: 'Analytics & Reports',
          icon: ChartBarIcon,
          path: '/admin/analytics',
          description: 'Business intelligence and reporting',
          children: [
            {
              id: 'analytics-overview',
              label: 'Overview',
              icon: ChartBarIcon,
              path: '/admin/analytics',
            },
            {
              id: 'analytics-custom',
              label: 'Custom Reports',
              icon: ClipboardDocumentIcon,
              path: '/admin/analytics/reports',
            },
            {
              id: 'analytics-export',
              label: 'Data Export',
              icon: DocumentIcon,
              path: '/admin/analytics/export',
            },
          ],
        },
      ],
    },
    
    // System administration
    {
      id: 'system',
      title: 'System',
      items: [
        {
          id: 'system-admin',
          label: 'System Administration',
          icon: ServerIcon,
          path: '/admin/system',
          description: 'Infrastructure and system management',
          permissions: ['super_admin'],
          children: [
            {
              id: 'system-monitoring',
              label: 'Monitoring',
              icon: ChartBarIcon,
              path: '/admin/system/monitoring',
            },
            {
              id: 'system-config',
              label: 'Configuration',
              icon: Cog6ToothIcon,
              path: '/admin/system/config',
            },
            {
              id: 'system-logs',
              label: 'Logs',
              icon: DocumentTextIcon,
              path: '/admin/system/logs',
            },
          ],
        },
        {
          id: 'content-management',
          label: 'Content Management',
          icon: DocumentIcon,
          path: '/admin/content',
          description: 'Manage static content and templates',
          permissions: ['admin', 'content_manager'],
        },
        {
          id: 'settings',
          label: 'Settings',
          icon: Cog6ToothIcon,
          path: '/admin/settings',
          description: 'Admin preferences and configuration',
        },
      ],
    },
  ],
};

/**
 * Get navigation items that the user has permission to access
 */
export function getFilteredNavigation(
  userPermissions: string[] = [],
  isSuperAdmin: boolean = false
): NavigationStructure {
  if (isSuperAdmin) {
    return navigationConfig;
  }

  const filterItems = (items: any[]): any[] => {
    return items
      .filter(item => {
        // If no permissions required, show the item
        if (!item.permissions || item.permissions.length === 0) {
          return true;
        }
        
        // Check if user has any of the required permissions
        return item.permissions.some((permission: string) => 
          userPermissions.includes(permission)
        );
      })
      .map(item => ({
        ...item,
        children: item.children ? filterItems(item.children) : undefined,
      }));
  };

  return {
    sections: navigationConfig.sections
      .map(section => ({
        ...section,
        items: filterItems(section.items),
      }))
      .filter(section => section.items.length > 0),
  };
}