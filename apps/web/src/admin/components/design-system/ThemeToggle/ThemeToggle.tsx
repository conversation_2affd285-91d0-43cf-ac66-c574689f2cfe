import React from 'react';
import { 
  SunIcon, 
  MoonIcon, 
  ComputerDesktopIcon 
} from '@heroicons/react/24/outline';
import { Button } from '../Button';
import { cn } from '../../../utils/cn';
import { useThemeContext } from '../../providers/ThemeProvider';

/**
 * Theme toggle variant
 */
export type ThemeToggleVariant = 'button' | 'dropdown' | 'switch';

/**
 * Theme toggle props
 */
export interface ThemeToggleProps {
  /**
   * Visual variant of the toggle
   */
  variant?: ThemeToggleVariant;
  
  /**
   * Size of the toggle
   */
  size?: 'sm' | 'md' | 'lg';
  
  /**
   * Whether to show labels
   */
  showLabels?: boolean;
  
  /**
   * Whether to show the system option
   */
  showSystem?: boolean;
  
  /**
   * Custom className
   */
  className?: string;
}

/**
 * ThemeToggle Component
 * 
 * Provides UI controls for switching between light, dark, and system themes.
 * Supports multiple visual variants and sizes.
 * 
 * @example
 * ```tsx
 * // Simple toggle button
 * <ThemeToggle variant="button" />
 * 
 * // Dropdown with system option
 * <ThemeToggle variant="dropdown" showSystem />
 * 
 * // Switch with labels
 * <ThemeToggle variant="switch" showLabels />
 * ```
 */
export const ThemeToggle: React.FC<ThemeToggleProps> = ({
  variant = 'button',
  size = 'md',
  showLabels = false,
  showSystem = false,
  className,
}) => {
  const { theme, setTheme, isDark, loading } = useThemeContext();
  const [dropdownOpen, setDropdownOpen] = React.useState(false);

  if (loading) {
    return (
      <div className={cn('animate-pulse', className)}>
        <div className="h-8 w-8 bg-gray-200 rounded-lg dark:bg-gray-700" />
      </div>
    );
  }

  // Simple toggle button (cycles through light -> dark -> system if enabled)
  if (variant === 'button') {
    const handleToggle = () => {
      if (theme === 'light') {
        setTheme('dark');
      } else if (theme === 'dark') {
        setTheme(showSystem ? 'system' : 'light');
      } else {
        setTheme('light');
      }
    };

    const getCurrentIcon = () => {
      if (theme === 'system') return ComputerDesktopIcon;
      return isDark ? MoonIcon : SunIcon;
    };

    const CurrentIcon = getCurrentIcon();

    return (
      <Button
        variant="ghost"
        size={size}
        onClick={handleToggle}
        className={className}
        aria-label={`Switch to ${theme === 'light' ? 'dark' : theme === 'dark' ? (showSystem ? 'system' : 'light') : 'light'} theme`}
      >
        <CurrentIcon className="h-5 w-5" />
        {showLabels && (
          <span className="ml-2 capitalize">
            {theme === 'system' ? 'Auto' : theme}
          </span>
        )}
      </Button>
    );
  }

  // Dropdown variant
  if (variant === 'dropdown') {
    const options = [
      { value: 'light', label: 'Light', icon: SunIcon },
      { value: 'dark', label: 'Dark', icon: MoonIcon },
      ...(showSystem ? [{ value: 'system', label: 'System', icon: ComputerDesktopIcon }] : []),
    ] as const;

    const currentOption = options.find(option => option.value === theme);
    const CurrentIcon = currentOption?.icon || SunIcon;

    return (
      <div className={cn('relative', className)}>
        <Button
          variant="ghost"
          size={size}
          onClick={() => setDropdownOpen(!dropdownOpen)}
          aria-expanded={dropdownOpen}
          aria-haspopup="true"
        >
          <CurrentIcon className="h-5 w-5" />
          {showLabels && (
            <span className="ml-2">
              {currentOption?.label}
            </span>
          )}
        </Button>

        {dropdownOpen && (
          <>
            {/* Backdrop */}
            <div 
              className="fixed inset-0 z-10" 
              onClick={() => setDropdownOpen(false)}
              aria-hidden="true"
            />
            
            {/* Dropdown menu */}
            <div className="absolute right-0 z-20 mt-2 w-48 origin-top-right rounded-lg bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none dark:bg-gray-800">
              <div className="py-1">
                {options.map((option) => {
                  const OptionIcon = option.icon;
                  const isSelected = theme === option.value;
                  
                  return (
                    <button
                      key={option.value}
                      onClick={() => {
                        setTheme(option.value);
                        setDropdownOpen(false);
                      }}
                      className={cn(
                        'flex w-full items-center px-4 py-2 text-sm transition-colors',
                        {
                          'bg-primary-50 text-primary-700 dark:bg-primary-900 dark:text-primary-300': isSelected,
                          'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700': !isSelected,
                        }
                      )}
                    >
                      <OptionIcon className="mr-3 h-4 w-4" />
                      {option.label}
                      {isSelected && (
                        <span className="ml-auto text-primary-600 dark:text-primary-400">
                          ✓
                        </span>
                      )}
                    </button>
                  );
                })}
              </div>
            </div>
          </>
        )}
      </div>
    );
  }

  // Switch variant (toggle between light/dark only)
  if (variant === 'switch') {
    return (
      <div className={cn('flex items-center', className)}>
        {showLabels && (
          <span className="mr-3 text-sm font-medium text-gray-700 dark:text-gray-300">
            <SunIcon className="inline h-4 w-4 mr-1" />
            Light
          </span>
        )}
        
        <button
          onClick={() => setTheme(isDark ? 'light' : 'dark')}
          className={cn(
            'relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2',
            {
              'bg-primary-600': isDark,
              'bg-gray-200': !isDark,
            }
          )}
          aria-checked={isDark}
          role="switch"
          aria-label="Toggle dark mode"
        >
          <span
            className={cn(
              'inline-block h-4 w-4 transform rounded-full bg-white transition-transform',
              {
                'translate-x-6': isDark,
                'translate-x-1': !isDark,
              }
            )}
          />
        </button>
        
        {showLabels && (
          <span className="ml-3 text-sm font-medium text-gray-700 dark:text-gray-300">
            <MoonIcon className="inline h-4 w-4 mr-1" />
            Dark
          </span>
        )}
      </div>
    );
  }

  return null;
};

ThemeToggle.displayName = 'ThemeToggle';