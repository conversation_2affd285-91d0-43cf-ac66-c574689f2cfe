import React, { useState, useMemo } from 'react';
import {
  ChevronUpIcon,
  ChevronDownIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  ArrowDownTrayIcon,
  TrashIcon,
  PencilIcon,
  EyeIcon,
} from '@heroicons/react/24/outline';
import { Button } from '../Button';
import { Card } from '../Card';
import { cn } from '../../../utils/cn';
import { exportData, formatDataForExport, getAvailableColumns, type ExportFormat } from '../../../utils/export';

/**
 * Column Definition Interface
 */
export interface Column<T = any> {
  id: string;
  header: string;
  accessor?: keyof T | string;
  cell?: (item: T) => React.ReactNode;
  sortable?: boolean;
  filterable?: boolean;
  width?: string;
  align?: 'left' | 'center' | 'right';
  type?: 'text' | 'number' | 'date' | 'boolean' | 'badge' | 'actions';
}

/**
 * Filter Configuration
 */
export interface FilterConfig {
  id: string;
  label: string;
  type: 'text' | 'select' | 'date' | 'number' | 'boolean';
  options?: { value: string; label: string }[];
  placeholder?: string;
}

/**
 * Sort Configuration
 */
export interface SortConfig {
  column: string;
  direction: 'asc' | 'desc';
}

/**
 * DataTable Props
 */
export interface DataTableProps<T = any> {
  data: T[];
  columns: Column<T>[];
  loading?: boolean;
  searchable?: boolean;
  searchPlaceholder?: string;
  filterable?: boolean;
  filters?: FilterConfig[];
  sortable?: boolean;
  selectable?: boolean;
  pagination?: boolean;
  pageSize?: number;
  exportable?: boolean;
  exportFormats?: ('csv' | 'pdf' | 'json')[];
  actions?: {
    view?: (item: T) => void;
    edit?: (item: T) => void;
    delete?: (item: T) => void;
    custom?: Array<{
      label: string;
      icon?: React.ComponentType<{ className?: string }>;
      onClick: (item: T) => void;
      variant?: 'primary' | 'secondary' | 'danger';
    }>;
  };
  batchActions?: Array<{
    label: string;
    icon?: React.ComponentType<{ className?: string }>;
    onClick: (selectedItems: T[]) => void;
    variant?: 'primary' | 'secondary' | 'danger';
  }>;
  onSearch?: (query: string) => void;
  onFilter?: (filters: Record<string, any>) => void;
  onSort?: (sort: SortConfig) => void;
  onSelectionChange?: (selectedItems: T[]) => void;
  className?: string;
}

/**
 * DataTable Component
 * 
 * A comprehensive data table with searching, filtering, sorting, pagination,
 * batch operations, and export capabilities.
 */
export const DataTable = <T extends Record<string, any>>({
  data,
  columns,
  loading = false,
  searchable = true,
  searchPlaceholder = "Search...",
  filterable = false,
  filters = [],
  sortable = true,
  selectable = false,
  pagination = true,
  pageSize = 10,
  exportable = false,
  exportFormats = ['csv'],
  actions,
  batchActions = [],
  onSearch,
  onFilter,
  onSort,
  onSelectionChange,
  className,
}: DataTableProps<T>) => {
  // State
  const [searchQuery, setSearchQuery] = useState('');
  const [filterValues, setFilterValues] = useState<Record<string, any>>({});
  const [sortConfig, setSortConfig] = useState<SortConfig | null>(null);
  const [selectedItems, setSelectedItems] = useState<Set<number>>(new Set());
  const [currentPage, setCurrentPage] = useState(1);
  const [showFilters, setShowFilters] = useState(false);

  // Memoized filtered and sorted data
  const processedData = useMemo(() => {
    let result = [...data];

    // Apply search
    if (searchQuery && !onSearch) {
      result = result.filter(item =>
        Object.values(item).some(value =>
          String(value).toLowerCase().includes(searchQuery.toLowerCase())
        )
      );
    }

    // Apply filters
    if (Object.keys(filterValues).length > 0 && !onFilter) {
      result = result.filter(item => {
        return Object.entries(filterValues).every(([key, value]) => {
          if (!value) return true;
          const itemValue = item[key];
          
          if (typeof value === 'string') {
            return String(itemValue).toLowerCase().includes(value.toLowerCase());
          }
          
          return itemValue === value;
        });
      });
    }

    // Apply sorting
    if (sortConfig && !onSort) {
      result.sort((a, b) => {
        const aValue = a[sortConfig.column];
        const bValue = b[sortConfig.column];
        
        if (aValue < bValue) return sortConfig.direction === 'asc' ? -1 : 1;
        if (aValue > bValue) return sortConfig.direction === 'asc' ? 1 : -1;
        return 0;
      });
    }

    return result;
  }, [data, searchQuery, filterValues, sortConfig, onSearch, onFilter, onSort]);

  // Pagination
  const totalPages = Math.ceil(processedData.length / pageSize);
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const currentData = pagination ? processedData.slice(startIndex, endIndex) : processedData;

  // Handlers
  const handleSearch = (query: string) => {
    setSearchQuery(query);
    setCurrentPage(1);
    onSearch?.(query);
  };

  const handleFilter = (filterId: string, value: any) => {
    const newFilters = { ...filterValues, [filterId]: value };
    setFilterValues(newFilters);
    setCurrentPage(1);
    onFilter?.(newFilters);
  };

  const handleSort = (columnId: string) => {
    if (!sortable) return;
    
    const newSort: SortConfig = {
      column: columnId,
      direction: sortConfig?.column === columnId && sortConfig.direction === 'asc' ? 'desc' : 'asc'
    };
    
    setSortConfig(newSort);
    onSort?.(newSort);
  };

  const handleSelectItem = (index: number) => {
    const newSelection = new Set(selectedItems);
    if (newSelection.has(index)) {
      newSelection.delete(index);
    } else {
      newSelection.add(index);
    }
    setSelectedItems(newSelection);
    
    const selectedData = Array.from(newSelection).map(i => currentData[i]);
    onSelectionChange?.(selectedData);
  };

  const handleSelectAll = () => {
    if (selectedItems.size === currentData.length) {
      setSelectedItems(new Set());
      onSelectionChange?.([]);
    } else {
      const allIndices = new Set(currentData.map((_, index) => index));
      setSelectedItems(allIndices);
      onSelectionChange?.(currentData);
    }
  };

  const getCellValue = (item: T, column: Column<T>) => {
    if (column.cell) {
      return column.cell(item);
    }
    
    if (column.accessor) {
      return item[column.accessor as keyof T];
    }
    
    return '';
  };

  const renderCell = (item: T, column: Column<T>) => {
    const value = getCellValue(item, column);
    
    switch (column.type) {
      case 'badge':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
            {value}
          </span>
        );
      case 'boolean':
        return (
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
            value 
              ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
              : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
          }`}>
            {value ? 'Yes' : 'No'}
          </span>
        );
      case 'actions':
        return (
          <div className="flex items-center space-x-2">
            {actions?.view && (
              <Button
                variant="ghost"
                size="sm"
                iconLeft={EyeIcon}
                onClick={() => actions.view!(item)}
                aria-label="View"
              />
            )}
            {actions?.edit && (
              <Button
                variant="ghost"
                size="sm"
                iconLeft={PencilIcon}
                onClick={() => actions.edit!(item)}
                aria-label="Edit"
              />
            )}
            {actions?.delete && (
              <Button
                variant="ghost"
                size="sm"
                iconLeft={TrashIcon}
                onClick={() => actions.delete!(item)}
                aria-label="Delete"
              />
            )}
            {actions?.custom?.map((action, index) => (
              <Button
                key={index}
                variant="ghost"
                size="sm"
                iconLeft={action.icon}
                onClick={() => action.onClick(item)}
                aria-label={action.label}
              />
            ))}
          </div>
        );
      default:
        return value;
    }
  };

  return (
    <Card className={cn('space-y-4', className)}>
      {/* Header Controls */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="flex items-center space-x-4">
          {/* Search */}
          {searchable && (
            <div className="relative">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                type="text"
                placeholder={searchPlaceholder}
                value={searchQuery}
                onChange={(e) => handleSearch(e.target.value)}
                className="pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              />
            </div>
          )}

          {/* Filter Toggle */}
          {filterable && filters.length > 0 && (
            <Button
              variant="outline"
              size="sm"
              iconLeft={FunnelIcon}
              onClick={() => setShowFilters(!showFilters)}
            >
              Filters
            </Button>
          )}
        </div>

        <div className="flex items-center space-x-2">
          {/* Batch Actions */}
          {selectable && selectedItems.size > 0 && batchActions.length > 0 && (
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-600 dark:text-gray-400">
                {selectedItems.size} selected
              </span>
              {batchActions.map((action, index) => (
                <Button
                  key={index}
                  variant={action.variant || 'secondary'}
                  size="sm"
                  iconLeft={action.icon}
                  onClick={() => {
                    const selectedData = Array.from(selectedItems).map(i => currentData[i]);
                    action.onClick(selectedData);
                  }}
                >
                  {action.label}
                </Button>
              ))}
            </div>
          )}

          {/* Export */}
          {exportable && (
            <div className="relative">
              <Button
                variant="outline"
                size="sm"
                iconLeft={ArrowDownTrayIcon}
                onClick={() => {
                  // Default to CSV export
                  const formattedData = formatDataForExport(processedData);
                  const exportColumns = columns
                    .filter(col => col.id !== 'actions')
                    .map(col => col.id);
                  
                  exportData(formattedData, 'csv', {
                    filename: 'data_export',
                    columns: exportColumns,
                  });
                }}
              >
                Export CSV
              </Button>
              {exportFormats.length > 1 && (
                <div className="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg ring-1 ring-black ring-opacity-5 z-10 hidden group-hover:block">
                  {exportFormats.map((format) => (
                    <button
                      key={format}
                      className="block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                      onClick={() => {
                        const formattedData = formatDataForExport(processedData);
                        const exportColumns = columns
                          .filter(col => col.id !== 'actions')
                          .map(col => col.id);
                        
                        exportData(formattedData, format, {
                          filename: 'data_export',
                          columns: exportColumns,
                        });
                      }}
                    >
                      Export {format.toUpperCase()}
                    </button>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Filters Panel */}
      {showFilters && filterable && (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
          {filters.map((filter) => (
            <div key={filter.id}>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                {filter.label}
              </label>
              {filter.type === 'select' ? (
                <select
                  value={filterValues[filter.id] || ''}
                  onChange={(e) => handleFilter(filter.id, e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                >
                  <option value="">All</option>
                  {filter.options?.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              ) : (
                <input
                  type={filter.type}
                  placeholder={filter.placeholder}
                  value={filterValues[filter.id] || ''}
                  onChange={(e) => handleFilter(filter.id, e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
                />
              )}
            </div>
          ))}
        </div>
      )}

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead className="bg-gray-50 dark:bg-gray-800">
            <tr>
              {selectable && (
                <th className="px-6 py-3 text-left">
                  <input
                    type="checkbox"
                    checked={selectedItems.size === currentData.length && currentData.length > 0}
                    onChange={handleSelectAll}
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  />
                </th>
              )}
              {columns.map((column) => (
                <th
                  key={column.id}
                  className={cn(
                    'px-6 py-3 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider',
                    column.align === 'center' && 'text-center',
                    column.align === 'right' && 'text-right',
                    column.sortable && 'cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700'
                  )}
                  style={{ width: column.width }}
                  onClick={() => column.sortable && handleSort(column.id)}
                >
                  <div className="flex items-center space-x-1">
                    <span>{column.header}</span>
                    {column.sortable && (
                      <div className="flex flex-col">
                        <ChevronUpIcon
                          className={cn(
                            'h-3 w-3',
                            sortConfig?.column === column.id && sortConfig.direction === 'asc'
                              ? 'text-primary-600'
                              : 'text-gray-400'
                          )}
                        />
                        <ChevronDownIcon
                          className={cn(
                            'h-3 w-3 -mt-1',
                            sortConfig?.column === column.id && sortConfig.direction === 'desc'
                              ? 'text-primary-600'
                              : 'text-gray-400'
                          )}
                        />
                      </div>
                    )}
                  </div>
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
            {loading ? (
              <tr>
                <td colSpan={columns.length + (selectable ? 1 : 0)} className="px-6 py-12 text-center">
                  <div className="flex justify-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
                  </div>
                  <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">Loading...</p>
                </td>
              </tr>
            ) : currentData.length === 0 ? (
              <tr>
                <td colSpan={columns.length + (selectable ? 1 : 0)} className="px-6 py-12 text-center text-gray-500 dark:text-gray-400">
                  No data found
                </td>
              </tr>
            ) : (
              currentData.map((item, index) => (
                <tr
                  key={index}
                  className={cn(
                    'hover:bg-gray-50 dark:hover:bg-gray-800',
                    selectedItems.has(index) && 'bg-primary-50 dark:bg-primary-900/20'
                  )}
                >
                  {selectable && (
                    <td className="px-6 py-4">
                      <input
                        type="checkbox"
                        checked={selectedItems.has(index)}
                        onChange={() => handleSelectItem(index)}
                        className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                      />
                    </td>
                  )}
                  {columns.map((column) => (
                    <td
                      key={column.id}
                      className={cn(
                        'px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white',
                        column.align === 'center' && 'text-center',
                        column.align === 'right' && 'text-right'
                      )}
                    >
                      {renderCell(item, column)}
                    </td>
                  ))}
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {pagination && totalPages > 1 && (
        <div className="flex items-center justify-between px-6 py-3 bg-gray-50 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
          <div className="flex-1 flex justify-between sm:hidden">
            <Button
              variant="outline"
              size="sm"
              disabled={currentPage === 1}
              onClick={() => setCurrentPage(currentPage - 1)}
            >
              Previous
            </Button>
            <Button
              variant="outline"
              size="sm"
              disabled={currentPage === totalPages}
              onClick={() => setCurrentPage(currentPage + 1)}
            >
              Next
            </Button>
          </div>
          
          <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p className="text-sm text-gray-700 dark:text-gray-300">
                Showing{' '}
                <span className="font-medium">{startIndex + 1}</span>
                {' '}to{' '}
                <span className="font-medium">
                  {Math.min(endIndex, processedData.length)}
                </span>
                {' '}of{' '}
                <span className="font-medium">{processedData.length}</span>
                {' '}results
              </p>
            </div>
            
            <div>
              <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                <button
                  onClick={() => setCurrentPage(currentPage - 1)}
                  disabled={currentPage === 1}
                  className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <ChevronLeftIcon className="h-5 w-5" />
                </button>
                
                {/* Page numbers */}
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  const pageNum = i + Math.max(1, currentPage - 2);
                  if (pageNum > totalPages) return null;
                  
                  return (
                    <button
                      key={pageNum}
                      onClick={() => setCurrentPage(pageNum)}
                      className={cn(
                        'relative inline-flex items-center px-4 py-2 border text-sm font-medium',
                        pageNum === currentPage
                          ? 'z-10 bg-primary-50 dark:bg-primary-900 border-primary-500 text-primary-600 dark:text-primary-400'
                          : 'bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600'
                      )}
                    >
                      {pageNum}
                    </button>
                  );
                })}
                
                <button
                  onClick={() => setCurrentPage(currentPage + 1)}
                  disabled={currentPage === totalPages}
                  className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <ChevronRightIcon className="h-5 w-5" />
                </button>
              </nav>
            </div>
          </div>
        </div>
      )}
    </Card>
  );
};

DataTable.displayName = 'DataTable';