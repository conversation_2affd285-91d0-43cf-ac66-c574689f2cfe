import React, { forwardRef } from 'react';
import { cn } from '../../../utils/cn';

/**
 * Button component variants
 */
export type ButtonVariant = 
  | 'primary'     // Filled primary color
  | 'secondary'   // Filled secondary color  
  | 'outline'     // Outlined with border
  | 'ghost'       // No background, minimal styling
  | 'danger';     // Destructive actions

/**
 * Button component sizes
 */
export type ButtonSize = 'sm' | 'md' | 'lg';

/**
 * Button component props
 */
export interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  /**
   * Visual style variant
   */
  variant?: ButtonVariant;
  
  /**
   * Size of the button
   */
  size?: ButtonSize;
  
  /**
   * Whether the button is in a loading state
   */
  loading?: boolean;
  
  /**
   * Icon to display before the text
   */
  iconLeft?: React.ComponentType<{ className?: string }>;
  
  /**
   * Icon to display after the text
   */
  iconRight?: React.ComponentType<{ className?: string }>;
  
  /**
   * Whether the button should take full width
   */
  fullWidth?: boolean;
  
  /**
   * Button content
   */
  children?: React.ReactNode;
}

/**
 * Button Component
 * 
 * A flexible button component with multiple variants, sizes, and states.
 * Supports icons, loading states, and full accessibility.
 * 
 * @example
 * ```tsx
 * // Primary button
 * <Button variant="primary" size="md">
 *   Save Changes
 * </Button>
 * 
 * // Button with icon
 * <Button variant="outline" iconLeft={PlusIcon}>
 *   Add Item
 * </Button>
 * 
 * // Loading state
 * <Button loading disabled>
 *   Processing...
 * </Button>
 * ```
 */
export const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      variant = 'primary',
      size = 'md',
      loading = false,
      iconLeft: IconLeft,
      iconRight: IconRight,
      fullWidth = false,
      className,
      children,
      disabled,
      ...props
    },
    ref
  ) => {
    const isDisabled = disabled || loading;

    return (
      <button
        ref={ref}
        className={cn(
          // Base styles
          'inline-flex items-center justify-center',
          'font-medium rounded-lg',
          'transition-all duration-200',
          'focus:outline-none focus:ring-2 focus:ring-offset-2',
          'disabled:opacity-50 disabled:cursor-not-allowed',
          'active:scale-[0.98]',
          
          // Size variants
          {
            // Small
            'h-8 px-3 text-sm gap-1.5': size === 'sm',
            // Medium  
            'h-10 px-4 text-base gap-2': size === 'md',
            // Large
            'h-12 px-6 text-lg gap-2.5': size === 'lg',
          },
          
          // Color variants
          {
            // Primary
            'bg-primary-600 text-white shadow-sm hover:bg-primary-700 focus:ring-primary-500': 
              variant === 'primary',
            
            // Secondary  
            'bg-gray-100 text-gray-900 shadow-sm hover:bg-gray-200 focus:ring-gray-500 dark:bg-gray-700 dark:text-gray-100 dark:hover:bg-gray-600': 
              variant === 'secondary',
            
            // Outline
            'border border-gray-300 bg-white text-gray-700 shadow-sm hover:bg-gray-50 focus:ring-primary-500 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700': 
              variant === 'outline',
            
            // Ghost
            'text-gray-700 hover:bg-gray-100 focus:ring-gray-500 dark:text-gray-300 dark:hover:bg-gray-800': 
              variant === 'ghost',
            
            // Danger
            'bg-error-600 text-white shadow-sm hover:bg-error-700 focus:ring-error-500': 
              variant === 'danger',
          },
          
          // Full width
          {
            'w-full': fullWidth,
          },
          
          className
        )}
        disabled={isDisabled}
        {...props}
      >
        {/* Left icon */}
        {IconLeft && !loading && (
          <IconLeft 
            className={cn(
              'flex-shrink-0',
              {
                'w-3.5 h-3.5': size === 'sm',
                'w-4 h-4': size === 'md', 
                'w-5 h-5': size === 'lg',
              }
            )} 
          />
        )}
        
        {/* Loading spinner */}
        {loading && (
          <div
            className={cn(
              'animate-spin rounded-full border-2 border-current border-t-transparent',
              {
                'w-3.5 h-3.5': size === 'sm',
                'w-4 h-4': size === 'md',
                'w-5 h-5': size === 'lg',
              }
            )}
            aria-hidden="true"
          />
        )}
        
        {/* Button text */}
        {children && (
          <span className={loading ? 'opacity-70' : ''}>
            {children}
          </span>
        )}
        
        {/* Right icon */}
        {IconRight && !loading && (
          <IconRight 
            className={cn(
              'flex-shrink-0',
              {
                'w-3.5 h-3.5': size === 'sm',
                'w-4 h-4': size === 'md',
                'w-5 h-5': size === 'lg', 
              }
            )} 
          />
        )}
      </button>
    );
  }
);

Button.displayName = 'Button';