import React, { forwardRef } from 'react';
import { cn } from '../../../utils/cn';

/**
 * Card component padding variants
 */
export type CardPadding = 'none' | 'sm' | 'md' | 'lg';

/**
 * Card component shadow variants  
 */
export type CardShadow = 'none' | 'sm' | 'md' | 'lg';

/**
 * Card component props
 */
export interface CardProps extends React.HTMLAttributes<HTMLDivElement> {
  /**
   * Optional title displayed in the card header
   */
  title?: string;
  
  /**
   * Optional subtitle displayed below the title
   */
  subtitle?: string;
  
  /**
   * Actions to display in the card header (buttons, menu, etc.)
   */
  headerActions?: React.ReactNode;
  
  /**
   * Padding variant for the card content
   */
  padding?: CardPadding;
  
  /**
   * Whether to show a border around the card
   */
  border?: boolean;
  
  /**
   * Shadow variant for the card
   */
  shadow?: CardShadow;
  
  /**
   * Card content
   */
  children: React.ReactNode;
}

/**
 * Card Component
 * 
 * A flexible container component for grouping related content with optional
 * headers, actions, and consistent styling.
 * 
 * @example
 * ```tsx
 * // Basic card
 * <Card>
 *   <p>Card content</p>
 * </Card>
 * 
 * // Card with header
 * <Card 
 *   title="User Statistics" 
 *   subtitle="Last updated 5 minutes ago"
 *   headerActions={<Button variant="ghost" size="sm">Refresh</Button>}
 * >
 *   <UserStats />
 * </Card>
 * 
 * // Custom styling
 * <Card padding="lg" shadow="lg" border>
 *   <ImportantContent />
 * </Card>
 * ```
 */
export const Card = forwardRef<HTMLDivElement, CardProps>(
  (
    {
      title,
      subtitle,
      headerActions,
      padding = 'md',
      border = true,
      shadow = 'sm',
      className,
      children,
      ...props
    },
    ref
  ) => {
    const hasHeader = title || subtitle || headerActions;

    return (
      <div
        ref={ref}
        className={cn(
          // Base styles
          'bg-white dark:bg-gray-800 rounded-lg overflow-hidden',
          
          // Border
          {
            'border border-gray-200 dark:border-gray-700': border,
          },
          
          // Shadow variants
          {
            'shadow-none': shadow === 'none',
            'shadow-sm': shadow === 'sm',
            'shadow-md': shadow === 'md', 
            'shadow-lg': shadow === 'lg',
          },
          
          className
        )}
        {...props}
      >
        {/* Card Header */}
        {hasHeader && (
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-start justify-between">
              <div className="min-w-0 flex-1">
                {title && (
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                    {title}
                  </h3>
                )}
                {subtitle && (
                  <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                    {subtitle}
                  </p>
                )}
              </div>
              
              {headerActions && (
                <div className="ml-4 flex-shrink-0">
                  {headerActions}
                </div>
              )}
            </div>
          </div>
        )}
        
        {/* Card Content */}
        <div
          className={cn({
            // Padding variants
            'p-0': padding === 'none',
            'p-4': padding === 'sm',
            'p-6': padding === 'md',
            'p-8': padding === 'lg',
          })}
        >
          {children}
        </div>
      </div>
    );
  }
);

Card.displayName = 'Card';