import React, { useState } from 'react';
import { cn } from '../../../utils/cn';

/**
 * Admin layout props
 */
export interface AdminLayoutProps {
  /**
   * Sidebar content
   */
  sidebar: React.ReactNode;
  
  /**
   * Header content
   */
  header: React.ReactNode;
  
  /**
   * Main content
   */
  children: React.ReactNode;
  
  /**
   * Whether the sidebar should be collapsed by default on mobile
   */
  defaultCollapsed?: boolean;
}

/**
 * AdminLayout Component
 * 
 * Main layout component for the admin interface. Provides a responsive
 * layout with sidebar navigation, header, and main content area.
 * 
 * Features:
 * - Responsive design (sidebar collapses on mobile)
 * - Smooth transitions
 * - Keyboard navigation support
 * - ARIA landmarks for accessibility
 * 
 * @example
 * ```tsx
 * <AdminLayout
 *   sidebar={<AdminSidebar />}
 *   header={<AdminHeader />}
 * >
 *   <DashboardContent />
 * </AdminLayout>
 * ```
 */
export const AdminLayout: React.FC<AdminLayoutProps> = ({
  sidebar,
  header,
  children,
  defaultCollapsed = false,
}) => {
  const [sidebarOpen, setSidebarOpen] = useState(!defaultCollapsed);

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Mobile sidebar overlay */}
      {sidebarOpen && (
        <div 
          className="fixed inset-0 z-40 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        >
          <div className="absolute inset-0 bg-gray-600 bg-opacity-75" />
        </div>
      )}

      {/* Sidebar */}
      <aside
        className={cn(
          // Base styles
          'fixed inset-y-0 left-0 z-50 w-64 transform transition-transform duration-300 ease-in-out',
          'bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700',
          
          // Mobile behavior
          'lg:translate-x-0 lg:static lg:inset-0',
          {
            'translate-x-0': sidebarOpen,
            '-translate-x-full': !sidebarOpen,
          }
        )}
        aria-label="Sidebar navigation"
      >
        {sidebar}
      </aside>

      {/* Main content area */}
      <div className="lg:ml-64">
        {/* Header */}
        <header 
          className="sticky top-0 z-30 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700"
          role="banner"
        >
          {React.cloneElement(header as React.ReactElement, {
            onMenuClick: () => setSidebarOpen(!sidebarOpen),
            sidebarOpen,
          })}
        </header>

        {/* Main content */}
        <main 
          className="flex-1 focus:outline-none"
          role="main"
          tabIndex={-1}
        >
          <div className="py-6">
            <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
              {children}
            </div>
          </div>
        </main>
      </div>
    </div>
  );
};

AdminLayout.displayName = 'AdminLayout';