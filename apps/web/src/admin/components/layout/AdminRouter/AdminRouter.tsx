import React, { useState } from 'react';
import { Dashboard } from '../../../pages/Dashboard';
import { Bills } from '../../../pages/Bills';

/**
 * Page Component Type
 */
export type PageComponent = React.ComponentType;

/**
 * Route Configuration
 */
export interface Route {
  path: string;
  component: PageComponent;
  title: string;
}

/**
 * Available Routes
 */
export const routes: Route[] = [
  {
    path: '/admin/dashboard',
    component: Dashboard,
    title: 'Dashboard',
  },
  {
    path: '/admin/bills',
    component: Bills,
    title: 'Bills Management',
  },
  // Add more routes as we build them
];

/**
 * Admin Router Props
 */
export interface AdminRouterProps {
  currentPath: string;
  onNavigate: (path: string) => void;
}

/**
 * Admin Router Component
 * 
 * Simple client-side router for the admin interface.
 * This is a temporary solution until we integrate with Next.js routing.
 */
export const AdminRouter: React.FC<AdminRouterProps> = ({
  currentPath,
  onNavigate,
}) => {
  // Find the current route
  const currentRoute = routes.find(route => route.path === currentPath) || routes[0];
  const Component = currentRoute.component;

  return (
    <div className="min-h-full">
      <Component />
    </div>
  );
};

AdminRouter.displayName = 'AdminRouter';