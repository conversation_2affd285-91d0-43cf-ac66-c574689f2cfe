import React from 'react';
import { 
  Bars3Icon, 
  BellIcon, 
  MagnifyingGlassIcon,
  ChevronDownIcon,
  UserCircleIcon,
  Cog6ToothIcon,
  ArrowRightOnRectangleIcon
} from '@heroicons/react/24/outline';
import { Button } from '../../design-system';
import { cn } from '../../../utils/cn';

/**
 * Admin header props
 */
export interface AdminHeaderProps {
  /**
   * Function to handle menu button click (for mobile sidebar toggle)
   */
  onMenuClick?: () => void;
  
  /**
   * Whether the sidebar is currently open
   */
  sidebarOpen?: boolean;
  
  /**
   * Current user information
   */
  user?: {
    name: string;
    email: string;
    avatar?: string;
  };
  
  /**
   * Number of unread notifications
   */
  notificationCount?: number;
  
  /**
   * Whether to show the search bar
   */
  showSearch?: boolean;
}

/**
 * AdminHeader Component
 * 
 * Top navigation header for the admin interface. Includes mobile menu toggle,
 * search, notifications, and user menu.
 * 
 * Features:
 * - Mobile-responsive design
 * - Notification badge
 * - User dropdown menu
 * - Global search
 * - Keyboard navigation
 * 
 * @example
 * ```tsx
 * <AdminHeader
 *   onMenuClick={() => toggleSidebar()}
 *   user={{ name: '<PERSON> Do<PERSON>', email: '<EMAIL>' }}
 *   notificationCount={3}
 *   showSearch
 * />
 * ```
 */
export const AdminHeader: React.FC<AdminHeaderProps> = ({
  onMenuClick,
  sidebarOpen = false,
  user,
  notificationCount = 0,
  showSearch = true,
}) => {
  const [userMenuOpen, setUserMenuOpen] = React.useState(false);
  const [searchOpen, setSearchOpen] = React.useState(false);

  return (
    <div className="flex h-16 items-center justify-between px-4 sm:px-6 lg:px-8">
      {/* Left side - Mobile menu + Search */}
      <div className="flex items-center gap-4">
        {/* Mobile menu button */}
        <Button
          variant="ghost"
          size="sm"
          onClick={onMenuClick}
          className="lg:hidden"
          aria-label={sidebarOpen ? 'Close sidebar' : 'Open sidebar'}
        >
          <Bars3Icon className="h-5 w-5" />
        </Button>

        {/* Search */}
        {showSearch && (
          <div className="relative hidden sm:block">
            <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
              <MagnifyingGlassIcon className="h-4 w-4 text-gray-400" aria-hidden="true" />
            </div>
            <input
              type="text"
              placeholder="Search..."
              className={cn(
                'block w-full rounded-lg border border-gray-300 bg-white py-2 pl-10 pr-3 text-sm',
                'placeholder:text-gray-400',
                'focus:border-primary-500 focus:outline-none focus:ring-1 focus:ring-primary-500',
                'dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder:text-gray-400',
                'dark:focus:border-primary-400 dark:focus:ring-primary-400'
              )}
              onFocus={() => setSearchOpen(true)}
              onBlur={() => setSearchOpen(false)}
            />
          </div>
        )}

        {/* Mobile search button */}
        {showSearch && (
          <Button
            variant="ghost"
            size="sm"
            className="sm:hidden"
            aria-label="Search"
          >
            <MagnifyingGlassIcon className="h-5 w-5" />
          </Button>
        )}
      </div>

      {/* Right side - Notifications + User menu */}
      <div className="flex items-center gap-2">
        {/* Notifications */}
        <div className="relative">
          <Button
            variant="ghost"
            size="sm"
            aria-label={`Notifications${notificationCount > 0 ? ` (${notificationCount} unread)` : ''}`}
          >
            <BellIcon className="h-5 w-5" />
            {notificationCount > 0 && (
              <span className="absolute -top-1 -right-1 flex h-4 w-4 items-center justify-center rounded-full bg-error-500 text-xs font-medium text-white">
                {notificationCount > 9 ? '9+' : notificationCount}
              </span>
            )}
          </Button>
        </div>

        {/* User menu */}
        <div className="relative">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setUserMenuOpen(!userMenuOpen)}
            className="flex items-center gap-2"
            aria-expanded={userMenuOpen}
            aria-haspopup="true"
          >
            {user?.avatar ? (
              <img
                src={user.avatar}
                alt={user.name}
                className="h-6 w-6 rounded-full"
              />
            ) : (
              <UserCircleIcon className="h-6 w-6" />
            )}
            
            <span className="hidden sm:block text-sm font-medium text-gray-700 dark:text-gray-300">
              {user?.name || 'Admin'}
            </span>
            
            <ChevronDownIcon 
              className={cn(
                'h-4 w-4 transition-transform duration-200',
                userMenuOpen && 'rotate-180'
              )} 
            />
          </Button>

          {/* User dropdown menu */}
          {userMenuOpen && (
            <div className="absolute right-0 mt-2 w-48 origin-top-right rounded-lg bg-white dark:bg-gray-800 py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
              <div className="px-4 py-2 border-b border-gray-200 dark:border-gray-700">
                <p className="text-sm font-medium text-gray-900 dark:text-white">
                  {user?.name || 'Admin User'}
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  {user?.email || '<EMAIL>'}
                </p>
              </div>

              <a
                href="#"
                className="flex items-center gap-2 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700"
                onClick={(e) => e.preventDefault()}
              >
                <Cog6ToothIcon className="h-4 w-4" />
                Settings
              </a>

              <button
                className="flex w-full items-center gap-2 px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700"
                onClick={() => {
                  // Handle logout
                  console.log('Logout clicked');
                }}
              >
                <ArrowRightOnRectangleIcon className="h-4 w-4" />
                Sign out
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Click outside to close user menu */}
      {userMenuOpen && (
        <div 
          className="fixed inset-0 z-10" 
          onClick={() => setUserMenuOpen(false)}
          aria-hidden="true"
        />
      )}
    </div>
  );
};

AdminHeader.displayName = 'AdminHeader';