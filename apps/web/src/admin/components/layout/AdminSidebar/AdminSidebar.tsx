import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { 
  ChevronDownIcon,
  ChevronRightIcon,
} from '@heroicons/react/24/outline';
import { cn } from '../../../utils/cn';
import { navigationConfig } from '../../../config/navigation';
import type { NavigationItem } from '../../../types/navigation';

/**
 * Admin sidebar props
 */
export interface AdminSidebarProps {
  /**
   * Current active path for highlighting
   */
  currentPath?: string;
  
  /**
   * User permissions for filtering navigation
   */
  userPermissions?: string[];
  
  /**
   * Whether user is super admin
   */
  isSuperAdmin?: boolean;
  
  /**
   * Callback when navigation item is clicked
   */
  onNavigate?: (path: string) => void;
}

/**
 * Navigation Item Component
 */
interface NavigationItemComponentProps {
  item: NavigationItem;
  currentPath: string;
  level: number;
  onNavigate: (path: string) => void;
}

const NavigationItemComponent: React.FC<NavigationItemComponentProps> = ({
  item,
  currentPath,
  level,
  onNavigate,
}) => {
  const [isExpanded, setIsExpanded] = useState(
    item.children?.some(child => currentPath.startsWith(child.path)) || false
  );
  
  const hasChildren = item.children && item.children.length > 0;
  const isActive = currentPath === item.path;
  const isChildActive = item.children?.some(child => currentPath.startsWith(child.path));

  const handleClick = () => {
    if (hasChildren) {
      setIsExpanded(!isExpanded);
    } else {
      onNavigate(item.path);
    }
  };

  const handleNavigation = (path: string) => {
    onNavigate(path);
  };

  return (
    <li>
      {/* Main navigation item */}
      <button
        onClick={handleClick}
        disabled={item.disabled}
        className={cn(
          // Base styles
          'group flex w-full items-center rounded-lg px-3 py-2 text-left text-sm font-medium transition-colors',
          'focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2',
          
          // Indentation based on level
          {
            'pl-3': level === 0,
            'pl-8': level === 1,
            'pl-12': level === 2,
          },
          
          // Active state
          {
            'bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300': 
              isActive || (hasChildren && isChildActive),
            'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700': 
              !isActive && !(hasChildren && isChildActive),
          },
          
          // Disabled state
          {
            'opacity-50 cursor-not-allowed': item.disabled,
          },
          
          // Featured items
          {
            'ring-1 ring-primary-200 dark:ring-primary-800': item.featured && !isActive,
          }
        )}
        aria-expanded={hasChildren ? isExpanded : undefined}
        title={item.description}
      >
        {/* Icon */}
        <item.icon 
          className={cn(
            'mr-3 h-5 w-5 flex-shrink-0',
            {
              'text-primary-500': isActive || (hasChildren && isChildActive),
              'text-gray-400 group-hover:text-gray-500 dark:text-gray-500 dark:group-hover:text-gray-400': 
                !isActive && !(hasChildren && isChildActive),
            }
          )} 
        />
        
        {/* Label */}
        <span className="flex-1 truncate">
          {item.label}
        </span>
        
        {/* Badge */}
        {item.badge && (
          <span className={cn(
            'ml-2 inline-flex items-center rounded-full px-2 py-0.5 text-xs font-medium',
            {
              'bg-primary-100 text-primary-600 dark:bg-primary-900 dark:text-primary-300': 
                isActive || (hasChildren && isChildActive),
              'bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-300': 
                !isActive && !(hasChildren && isChildActive),
            }
          )}>
            {item.badge}
          </span>
        )}
        
        {/* Expand/collapse icon for items with children */}
        {hasChildren && (
          <div className="ml-2">
            {isExpanded ? (
              <ChevronDownIcon className="h-4 w-4" />
            ) : (
              <ChevronRightIcon className="h-4 w-4" />
            )}
          </div>
        )}
      </button>
      
      {/* Child items */}
      {hasChildren && isExpanded && (
        <ul className="mt-1 space-y-1">
          {item.children!.map((child) => (
            <NavigationItemComponent
              key={child.id}
              item={child}
              currentPath={currentPath}
              level={level + 1}
              onNavigate={handleNavigation}
            />
          ))}
        </ul>
      )}
    </li>
  );
};

/**
 * AdminSidebar Component
 * 
 * Main navigation sidebar for the admin interface. Provides hierarchical
 * navigation with support for nested items, badges, and permission-based filtering.
 * 
 * Features:
 * - Hierarchical navigation with expand/collapse
 * - Active state management
 * - Badge support for notifications
 * - Permission-based filtering
 * - Keyboard navigation support
 * - Mobile responsive
 * 
 * @example
 * ```tsx
 * <AdminSidebar
 *   currentPath="/admin/bills"
 *   userPermissions={['admin', 'bill_manager']}
 *   onNavigate={(path) => router.push(path)}
 * />
 * ```
 */
export const AdminSidebar: React.FC<AdminSidebarProps> = ({
  currentPath = '',
  userPermissions = [],
  isSuperAdmin = false,
  onNavigate,
}) => {
  const router = useRouter();
  
  const handleNavigate = (path: string) => {
    if (onNavigate) {
      onNavigate(path);
    } else {
      router.push(path);
    }
  };

  // Filter navigation based on permissions
  const filteredSections = navigationConfig.sections.map(section => ({
    ...section,
    items: section.items.filter(item => {
      if (isSuperAdmin) return true;
      if (!item.permissions || item.permissions.length === 0) return true;
      return item.permissions.some(permission => userPermissions.includes(permission));
    }),
  })).filter(section => section.items.length > 0);

  return (
    <div className="flex h-full flex-col">
      {/* Logo/Brand */}
      <div className="flex h-16 shrink-0 items-center border-b border-gray-200 px-6 dark:border-gray-700">
        <div className="flex items-center">
          <div className="h-8 w-8 rounded-lg bg-primary-600 flex items-center justify-center">
            <span className="text-white font-bold text-sm">MA</span>
          </div>
          <span className="ml-3 text-lg font-semibold text-gray-900 dark:text-white">
            Modern Action
          </span>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 space-y-6 overflow-y-auto px-3 py-6" aria-label="Main navigation">
        {filteredSections.map((section) => (
          <div key={section.id}>
            {/* Section title */}
            {section.title && (
              <h3 className="px-3 text-xs font-semibold uppercase tracking-wider text-gray-500 dark:text-gray-400">
                {section.title}
              </h3>
            )}
            
            {/* Section items */}
            <ul className={cn('space-y-1', section.title && 'mt-2')}>
              {section.items.map((item) => (
                <NavigationItemComponent
                  key={item.id}
                  item={item}
                  currentPath={currentPath}
                  level={0}
                  onNavigate={handleNavigate}
                />
              ))}
            </ul>
          </div>
        ))}
      </nav>

      {/* Footer */}
      <div className="border-t border-gray-200 p-4 dark:border-gray-700">
        <div className="text-xs text-gray-500 dark:text-gray-400">
          <p>Admin Dashboard v2.0</p>
          <p className="mt-1">Modern Action Platform</p>
        </div>
      </div>
    </div>
  );
};

AdminSidebar.displayName = 'AdminSidebar';