import React, { createContext, useContext, useEffect } from 'react';
import { useTheme, type UseThemeReturn } from '../../hooks/useTheme';

/**
 * Theme context type
 */
type ThemeContextType = UseThemeReturn | undefined;

/**
 * Theme context
 */
const ThemeContext = createContext<ThemeContextType>(undefined);

/**
 * Theme provider props
 */
export interface ThemeProviderProps {
  /**
   * Child components
   */
  children: React.ReactNode;
  
  /**
   * Default theme (used for SSR)
   */
  defaultTheme?: 'light' | 'dark' | 'system';
  
  /**
   * Whether to force a specific theme (useful for testing)
   */
  forcedTheme?: 'light' | 'dark';
}

/**
 * ThemeProvider Component
 * 
 * Provides theme context to child components and manages theme state
 * across the application. Handles SSR hydration and theme persistence.
 * 
 * @example
 * ```tsx
 * function App() {
 *   return (
 *     <ThemeProvider defaultTheme="system">
 *       <AdminLayout>
 *         <Dashboard />
 *       </AdminLayout>
 *     </ThemeProvider>
 *   );
 * }
 * ```
 */
export const ThemeProvider: React.FC<ThemeProviderProps> = ({
  children,
  defaultTheme = 'system',
  forcedTheme,
}) => {
  const themeValue = useTheme();

  // Apply forced theme if specified
  useEffect(() => {
    if (forcedTheme && themeValue.theme !== forcedTheme) {
      themeValue.setTheme(forcedTheme);
    }
  }, [forcedTheme, themeValue]);

  // Prevent hydration mismatch by not rendering until theme is loaded
  if (themeValue.loading && typeof window !== 'undefined') {
    return (
      <div className="min-h-screen bg-white dark:bg-gray-900">
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
        </div>
      </div>
    );
  }

  return (
    <ThemeContext.Provider value={themeValue}>
      {children}
    </ThemeContext.Provider>
  );
};

/**
 * Hook to use theme context
 * 
 * @example
 * ```tsx
 * function ThemeToggle() {
 *   const { theme, setTheme, isDark } = useThemeContext();
 *   
 *   return (
 *     <button onClick={() => setTheme(isDark ? 'light' : 'dark')}>
 *       Toggle Theme
 *     </button>
 *   );
 * }
 * ```
 */
export function useThemeContext(): UseThemeReturn {
  const context = useContext(ThemeContext);
  
  if (context === undefined) {
    throw new Error('useThemeContext must be used within a ThemeProvider');
  }
  
  return context;
}

ThemeProvider.displayName = 'ThemeProvider';