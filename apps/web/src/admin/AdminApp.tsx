import React, { useState } from 'react';
import { UserProvider } from '@auth0/nextjs-auth0/client';
import { 
  AdminLayout,
  AdminHeader, 
  AdminSidebar,
  AdminRouter,
} from './components/layout';
import { ThemeProvider } from './components/providers/ThemeProvider';
import { useAuth, useNotifications } from './hooks';
import { Button } from './components/design-system';

/**
 * Notification Toast Component
 */
const NotificationToast: React.FC = () => {
  const { notifications, removeNotification, markNotificationRead } = useNotifications();

  if (notifications.length === 0) return null;

  return (
    <div className="fixed top-4 right-4 z-50 space-y-2">
      {notifications.slice(0, 5).map((notification) => (
        <div
          key={notification.id}
          className={`max-w-sm w-full bg-white dark:bg-gray-800 shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden ${
            notification.read ? 'opacity-75' : ''
          }`}
        >
          <div className="p-4">
            <div className="flex items-start">
              <div className="flex-shrink-0">
                <div className={`w-6 h-6 rounded-full flex items-center justify-center text-white text-xs ${
                  notification.type === 'success' ? 'bg-green-500' :
                  notification.type === 'warning' ? 'bg-yellow-500' :
                  notification.type === 'error' ? 'bg-red-500' :
                  'bg-blue-500'
                }`}>
                  {notification.type === 'success' ? '✓' :
                   notification.type === 'warning' ? '⚠' :
                   notification.type === 'error' ? '✕' :
                   'ℹ'}
                </div>
              </div>
              <div className="ml-3 w-0 flex-1 pt-0.5">
                <p className="text-sm font-medium text-gray-900 dark:text-white">
                  {notification.title}
                </p>
                <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                  {notification.message}
                </p>
              </div>
              <div className="ml-4 flex-shrink-0 flex">
                <button
                  className="bg-white dark:bg-gray-800 rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                  onClick={() => removeNotification(notification.id)}
                >
                  <span className="sr-only">Close</span>
                  <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

/**
 * Access Denied Component
 */
const AccessDenied: React.FC = () => (
  <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
    <div className="sm:mx-auto sm:w-full sm:max-w-md">
      <div className="bg-white dark:bg-gray-800 py-8 px-4 shadow sm:rounded-lg sm:px-10">
        <div className="text-center">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white">
            Access Denied
          </h2>
          <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
            You don't have permission to access the admin interface.
          </p>
          <div className="mt-6">
            <Button
              variant="primary"
              onClick={() => window.location.href = '/api/auth/logout'}
            >
              Sign Out
            </Button>
          </div>
        </div>
      </div>
    </div>
  </div>
);

/**
 * Loading Component
 */
const Loading: React.FC = () => (
  <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
    <div className="text-center">
      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
      <p className="mt-4 text-sm text-gray-600 dark:text-gray-400">
        Loading admin interface...
      </p>
    </div>
  </div>
);

/**
 * Admin Content Component
 */
const AdminContent: React.FC = () => {
  const [currentPath, setCurrentPath] = useState('/admin/dashboard');
  const { user, isAuthenticated, isAdmin, loading } = useAuth();
  const { unreadCount } = useNotifications();

  if (loading) {
    return <Loading />;
  }

  if (!isAuthenticated || !isAdmin) {
    return <AccessDenied />;
  }

  const handleNavigation = (path: string) => {
    setCurrentPath(path);
  };

  return (
    <>
      <AdminLayout
        sidebar={
          <AdminSidebar
            currentPath={currentPath}
            userPermissions={user?.permissions || []}
            isSuperAdmin={user?.role === 'super_admin'}
            onNavigate={handleNavigation}
          />
        }
        header={
          <AdminHeader
            user={user}
            notificationCount={unreadCount}
            showSearch
          />
        }
      >
        <AdminRouter
          currentPath={currentPath}
          onNavigate={handleNavigation}
        />
      </AdminLayout>
      
      <NotificationToast />
    </>
  );
};

/**
 * AdminApp Component
 * 
 * Main entry point for the admin interface. Handles authentication,
 * theming, and provides the complete admin experience.
 * 
 * Features:
 * - Auth0 integration
 * - Theme management
 * - Notification system
 * - Responsive layout
 * - Access control
 * 
 * @example
 * ```tsx
 * // In your Next.js page
 * export default function AdminPage() {
 *   return <AdminApp />;
 * }
 * ```
 */
export const AdminApp: React.FC = () => {
  return (
    <UserProvider>
      <ThemeProvider defaultTheme="system">
        <AdminContent />
      </ThemeProvider>
    </UserProvider>
  );
};

AdminApp.displayName = 'AdminApp';