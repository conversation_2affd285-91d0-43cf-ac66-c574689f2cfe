/**
 * Design Tokens
 * 
 * Centralized design values for consistent styling across the admin interface.
 * Based on Tailwind CSS but abstracted for easier maintenance and theming.
 */

export const tokens = {
  colors: {
    // Primary brand colors
    primary: {
      50: '#eff6ff',
      100: '#dbeafe',
      200: '#bfdbfe',
      300: '#93c5fd',
      400: '#60a5fa',
      500: '#3b82f6',
      600: '#2563eb',
      700: '#1d4ed8',
      800: '#1e40af',
      900: '#1e3a8a',
      950: '#172554',
    },
    
    // Semantic colors
    success: {
      50: '#ecfdf5',
      100: '#d1fae5',
      500: '#10b981',
      600: '#059669',
      700: '#047857',
    },
    
    warning: {
      50: '#fffbeb',
      100: '#fef3c7',
      500: '#f59e0b',
      600: '#d97706',
      700: '#b45309',
    },
    
    error: {
      50: '#fef2f2',
      100: '#fee2e2',
      500: '#ef4444',
      600: '#dc2626',
      700: '#b91c1c',
    },
    
    info: {
      50: '#f0f9ff',
      100: '#e0f2fe',
      500: '#06b6d4',
      600: '#0891b2',
      700: '#0e7490',
    },
    
    // Neutral grays
    gray: {
      50: '#f9fafb',
      100: '#f3f4f6',
      200: '#e5e7eb',
      300: '#d1d5db',
      400: '#9ca3af',
      500: '#6b7280',
      600: '#4b5563',
      700: '#374151',
      800: '#1f2937',
      900: '#111827',
      950: '#030712',
    },
  },
  
  spacing: {
    px: '1px',
    0: '0',
    0.5: '0.125rem',  // 2px
    1: '0.25rem',     // 4px
    1.5: '0.375rem',  // 6px
    2: '0.5rem',      // 8px
    2.5: '0.625rem',  // 10px
    3: '0.75rem',     // 12px
    3.5: '0.875rem',  // 14px
    4: '1rem',        // 16px
    5: '1.25rem',     // 20px
    6: '1.5rem',      // 24px
    7: '1.75rem',     // 28px
    8: '2rem',        // 32px
    9: '2.25rem',     // 36px
    10: '2.5rem',     // 40px
    11: '2.75rem',    // 44px
    12: '3rem',       // 48px
    14: '3.5rem',     // 56px
    16: '4rem',       // 64px
    20: '5rem',       // 80px
    24: '6rem',       // 96px
    28: '7rem',       // 112px
    32: '8rem',       // 128px
    36: '9rem',       // 144px
    40: '10rem',      // 160px
    44: '11rem',      // 176px
    48: '12rem',      // 192px
    52: '13rem',      // 208px
    56: '14rem',      // 224px
    60: '15rem',      // 240px
    64: '16rem',      // 256px
    72: '18rem',      // 288px
    80: '20rem',      // 320px
    96: '24rem',      // 384px
  },
  
  fontSize: {
    xs: ['0.75rem', { lineHeight: '1rem' }],      // 12px
    sm: ['0.875rem', { lineHeight: '1.25rem' }],  // 14px
    base: ['1rem', { lineHeight: '1.5rem' }],     // 16px
    lg: ['1.125rem', { lineHeight: '1.75rem' }],  // 18px
    xl: ['1.25rem', { lineHeight: '1.75rem' }],   // 20px
    '2xl': ['1.5rem', { lineHeight: '2rem' }],    // 24px
    '3xl': ['1.875rem', { lineHeight: '2.25rem' }], // 30px
    '4xl': ['2.25rem', { lineHeight: '2.5rem' }],   // 36px
    '5xl': ['3rem', { lineHeight: '1' }],           // 48px
    '6xl': ['3.75rem', { lineHeight: '1' }],        // 60px
  },
  
  fontWeight: {
    thin: '100',
    extralight: '200',
    light: '300',
    normal: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
    extrabold: '800',
    black: '900',
  },
  
  fontFamily: {
    sans: [
      'Inter',
      '-apple-system',
      'BlinkMacSystemFont',
      '"Segoe UI"',
      'Roboto',
      '"Helvetica Neue"',
      'Arial',
      'sans-serif',
    ],
    mono: [
      '"JetBrains Mono"',
      '"Fira Code"',
      'Monaco',
      'Consolas',
      '"Liberation Mono"',
      '"Courier New"',
      'monospace',
    ],
  },
  
  borderRadius: {
    none: '0',
    sm: '0.125rem',   // 2px
    base: '0.25rem',  // 4px
    md: '0.375rem',   // 6px
    lg: '0.5rem',     // 8px
    xl: '0.75rem',    // 12px
    '2xl': '1rem',    // 16px
    '3xl': '1.5rem',  // 24px
    full: '9999px',
  },
  
  boxShadow: {
    sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
    base: '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)',
    md: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
    lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
    xl: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
    '2xl': '0 25px 50px -12px rgb(0 0 0 / 0.25)',
    inner: 'inset 0 2px 4px 0 rgb(0 0 0 / 0.05)',
    none: '0 0 #0000',
  },
  
  breakpoints: {
    sm: '640px',
    md: '768px',
    lg: '1024px',
    xl: '1280px',
    '2xl': '1536px',
  },
  
  zIndex: {
    auto: 'auto',
    0: '0',
    10: '10',
    20: '20',
    30: '30',
    40: '40',
    50: '50',
    modal: '1000',
    popover: '1010',
    tooltip: '1020',
    notification: '1030',
  },
} as const;

/**
 * Dark mode color overrides
 * These colors are used when dark mode is active
 */
export const darkModeColors = {
  background: {
    primary: tokens.colors.gray[900],
    secondary: tokens.colors.gray[800],
    tertiary: tokens.colors.gray[700],
  },
  
  surface: {
    primary: tokens.colors.gray[800],
    secondary: tokens.colors.gray[700],
    tertiary: tokens.colors.gray[600],
  },
  
  border: {
    primary: tokens.colors.gray[700],
    secondary: tokens.colors.gray[600],
    tertiary: tokens.colors.gray[500],
  },
  
  text: {
    primary: tokens.colors.gray[50],
    secondary: tokens.colors.gray[300],
    tertiary: tokens.colors.gray[400],
    muted: tokens.colors.gray[500],
  },
} as const;

/**
 * Component-specific design tokens
 */
export const components = {
  button: {
    height: {
      sm: tokens.spacing[8],   // 32px
      md: tokens.spacing[10],  // 40px
      lg: tokens.spacing[12],  // 48px
    },
    padding: {
      sm: `${tokens.spacing[2]} ${tokens.spacing[3]}`,  // 8px 12px
      md: `${tokens.spacing[2.5]} ${tokens.spacing[4]}`, // 10px 16px
      lg: `${tokens.spacing[3]} ${tokens.spacing[6]}`,  // 12px 24px
    },
    fontSize: {
      sm: tokens.fontSize.sm,
      md: tokens.fontSize.base,
      lg: tokens.fontSize.lg,
    },
  },
  
  input: {
    height: {
      sm: tokens.spacing[8],   // 32px
      md: tokens.spacing[10],  // 40px
      lg: tokens.spacing[12],  // 48px
    },
    padding: {
      sm: `${tokens.spacing[1.5]} ${tokens.spacing[2.5]}`, // 6px 10px
      md: `${tokens.spacing[2]} ${tokens.spacing[3]}`,     // 8px 12px
      lg: `${tokens.spacing[2.5]} ${tokens.spacing[4]}`,   // 10px 16px
    },
  },
  
  card: {
    padding: {
      sm: tokens.spacing[4],   // 16px
      md: tokens.spacing[6],   // 24px
      lg: tokens.spacing[8],   // 32px
    },
    borderRadius: tokens.borderRadius.lg,
    shadow: tokens.boxShadow.sm,
  },
} as const;

export type Tokens = typeof tokens;
export type DarkModeColors = typeof darkModeColors;
export type Components = typeof components;