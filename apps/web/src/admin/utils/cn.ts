import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

/**
 * Utility function to merge class names conditionally
 * 
 * Combines clsx for conditional classes with tailwind-merge for deduplication
 * 
 * @example
 * ```tsx
 * cn('base-class', condition && 'conditional-class', {
 *   'active-class': isActive,
 *   'disabled-class': isDisabled
 * })
 * ```
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}