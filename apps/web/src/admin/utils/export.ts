/**
 * Export Utilities
 * 
 * Utilities for exporting data in various formats (CSV, JSON, PDF)
 */

/**
 * Export Format Types
 */
export type ExportFormat = 'csv' | 'json' | 'pdf';

/**
 * Export Options Interface
 */
export interface ExportOptions {
  filename?: string;
  columns?: string[];
  dateFormat?: string;
  includeHeaders?: boolean;
}

/**
 * Convert data to CSV format
 */
export const convertToCSV = (data: any[], options: ExportOptions = {}): string => {
  if (data.length === 0) return '';

  const { columns, includeHeaders = true } = options;
  const headers = columns || Object.keys(data[0]);
  
  const csvRows: string[] = [];
  
  // Add headers if requested
  if (includeHeaders) {
    csvRows.push(headers.map(header => `"${header}"`).join(','));
  }
  
  // Add data rows
  for (const row of data) {
    const values = headers.map(header => {
      const value = row[header];
      
      // Handle different data types
      if (value == null) return '""';
      if (typeof value === 'string') return `"${value.replace(/"/g, '""')}"`;
      if (typeof value === 'object') return `"${JSON.stringify(value).replace(/"/g, '""')}"`;
      
      return `"${value}"`;
    });
    csvRows.push(values.join(','));
  }
  
  return csvRows.join('\n');
};

/**
 * Convert data to JSON format
 */
export const convertToJSON = (data: any[], options: ExportOptions = {}): string => {
  const { columns } = options;
  
  if (columns) {
    // Filter data to only include specified columns
    const filteredData = data.map(row => {
      const filteredRow: any = {};
      columns.forEach(col => {
        filteredRow[col] = row[col];
      });
      return filteredRow;
    });
    return JSON.stringify(filteredData, null, 2);
  }
  
  return JSON.stringify(data, null, 2);
};

/**
 * Generate PDF content (basic HTML structure)
 * Note: In a real app, you'd use a proper PDF library like jsPDF or Puppeteer
 */
export const generatePDFHTML = (data: any[], options: ExportOptions = {}): string => {
  const { columns } = options;
  const headers = columns || (data.length > 0 ? Object.keys(data[0]) : []);
  
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <title>Export Data</title>
      <style>
        body { 
          font-family: Arial, sans-serif; 
          margin: 20px;
        }
        table { 
          border-collapse: collapse; 
          width: 100%; 
          margin-top: 20px;
        }
        th, td { 
          border: 1px solid #ddd; 
          padding: 8px; 
          text-align: left; 
        }
        th { 
          background-color: #f2f2f2; 
          font-weight: bold;
        }
        tr:nth-child(even) { 
          background-color: #f9f9f9; 
        }
        .header {
          margin-bottom: 20px;
        }
        .timestamp {
          color: #666;
          font-size: 12px;
        }
      </style>
    </head>
    <body>
      <div class="header">
        <h1>Data Export</h1>
        <p class="timestamp">Generated on: ${new Date().toLocaleString()}</p>
        <p>Total records: ${data.length}</p>
      </div>
      
      <table>
        <thead>
          <tr>
            ${headers.map(header => `<th>${header}</th>`).join('')}
          </tr>
        </thead>
        <tbody>
          ${data.map(row => `
            <tr>
              ${headers.map(header => {
                const value = row[header];
                if (value == null) return '<td></td>';
                if (typeof value === 'object') return `<td>${JSON.stringify(value)}</td>`;
                return `<td>${value}</td>`;
              }).join('')}
            </tr>
          `).join('')}
        </tbody>
      </table>
    </body>
    </html>
  `;
  
  return html;
};

/**
 * Download file with given content and filename
 */
export const downloadFile = (content: string, filename: string, mimeType: string) => {
  const blob = new Blob([content], { type: mimeType });
  const url = URL.createObjectURL(blob);
  
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  
  // Cleanup
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
};

/**
 * Export data in specified format
 */
export const exportData = (
  data: any[],
  format: ExportFormat,
  options: ExportOptions = {}
) => {
  const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
  const defaultFilename = `export_${timestamp}`;
  const filename = options.filename || defaultFilename;
  
  switch (format) {
    case 'csv': {
      const csvContent = convertToCSV(data, options);
      downloadFile(csvContent, `${filename}.csv`, 'text/csv;charset=utf-8;');
      break;
    }
    
    case 'json': {
      const jsonContent = convertToJSON(data, options);
      downloadFile(jsonContent, `${filename}.json`, 'application/json;charset=utf-8;');
      break;
    }
    
    case 'pdf': {
      // For now, we'll export as HTML that can be printed to PDF
      // In a real app, you'd use a proper PDF library
      const htmlContent = generatePDFHTML(data, options);
      downloadFile(htmlContent, `${filename}.html`, 'text/html;charset=utf-8;');
      break;
    }
    
    default:
      throw new Error(`Unsupported export format: ${format}`);
  }
};

/**
 * Get export filename with timestamp
 */
export const getExportFilename = (baseName: string, format: ExportFormat): string => {
  const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
  return `${baseName}_${timestamp}.${format}`;
};

/**
 * Validate export data
 */
export const validateExportData = (data: any[]): boolean => {
  if (!Array.isArray(data)) return false;
  if (data.length === 0) return false;
  
  // Check if all items are objects
  return data.every(item => typeof item === 'object' && item !== null);
};

/**
 * Get available columns from data
 */
export const getAvailableColumns = (data: any[]): string[] => {
  if (data.length === 0) return [];
  
  const allColumns = new Set<string>();
  data.forEach(item => {
    Object.keys(item).forEach(key => allColumns.add(key));
  });
  
  return Array.from(allColumns).sort();
};

/**
 * Format data for export (clean up complex objects, dates, etc.)
 */
export const formatDataForExport = (data: any[], options: ExportOptions = {}): any[] => {
  const { dateFormat = 'YYYY-MM-DD HH:mm:ss' } = options;
  
  return data.map(item => {
    const formatted: any = {};
    
    Object.entries(item).forEach(([key, value]) => {
      if (value == null) {
        formatted[key] = '';
      } else if (value instanceof Date) {
        formatted[key] = value.toISOString().slice(0, 19).replace('T', ' ');
      } else if (Array.isArray(value)) {
        formatted[key] = value.join(', ');
      } else if (typeof value === 'object') {
        formatted[key] = JSON.stringify(value);
      } else {
        formatted[key] = value;
      }
    });
    
    return formatted;
  });
};