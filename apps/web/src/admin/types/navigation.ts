/**
 * Navigation Types
 * 
 * Type definitions for the admin navigation system
 */

/**
 * Navigation item interface
 */
export interface NavigationItem {
  /**
   * Unique identifier for the navigation item
   */
  id: string;
  
  /**
   * Display label for the navigation item
   */
  label: string;
  
  /**
   * Icon component to display (from Heroicons)
   */
  icon: React.ComponentType<{ className?: string }>;
  
  /**
   * Path/URL for the navigation item
   */
  path: string;
  
  /**
   * Optional badge to display (number or string)
   */
  badge?: number | string;
  
  /**
   * Child navigation items (for nested navigation)
   */
  children?: NavigationItem[];
  
  /**
   * Required permissions to access this item
   */
  permissions?: string[];
  
  /**
   * Whether this item should be highlighted/featured
   */
  featured?: boolean;
  
  /**
   * Whether this item is disabled
   */
  disabled?: boolean;
  
  /**
   * Optional description for tooltips
   */
  description?: string;
}

/**
 * Navigation section for grouping related items
 */
export interface NavigationSection {
  /**
   * Section identifier
   */
  id: string;
  
  /**
   * Section title (optional)
   */
  title?: string;
  
  /**
   * Navigation items in this section
   */
  items: NavigationItem[];
}

/**
 * Complete navigation structure
 */
export interface NavigationStructure {
  /**
   * Navigation sections
   */
  sections: NavigationSection[];
}