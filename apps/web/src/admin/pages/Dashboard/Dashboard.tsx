import React from 'react';
import {
  CpuChipIcon,
  UsersIcon,
  DocumentTextIcon,
  CurrencyDollarIcon,
  ChartBarIcon,
  ServerIcon,
} from '@heroicons/react/24/outline';
import { Card, Button, ThemeToggle } from '../../components/design-system';
import { useAuth, useNotifications } from '../../hooks';

/**
 * KPI Card Component
 */
interface KPICardProps {
  title: string;
  value: string | number;
  change?: {
    value: number;
    direction: 'up' | 'down';
    timeframe: string;
  };
  icon: React.ComponentType<{ className?: string }>;
  color: 'blue' | 'green' | 'red' | 'yellow' | 'purple';
}

const KPICard: React.FC<KPICardProps> = ({
  title,
  value,
  change,
  icon: Icon,
  color,
}) => {
  const colorClasses = {
    blue: 'bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-300',
    green: 'bg-green-100 text-green-600 dark:bg-green-900 dark:text-green-300',
    red: 'bg-red-100 text-red-600 dark:bg-red-900 dark:text-red-300',
    yellow: 'bg-yellow-100 text-yellow-600 dark:bg-yellow-900 dark:text-yellow-300',
    purple: 'bg-purple-100 text-purple-600 dark:bg-purple-900 dark:text-purple-300',
  };

  return (
    <Card padding="md" shadow="sm">
      <div className="flex items-center justify-between">
        <div className="min-w-0 flex-1">
          <p className="text-sm font-medium text-gray-600 dark:text-gray-400 truncate">
            {title}
          </p>
          <p className="text-2xl font-bold text-gray-900 dark:text-white">
            {value}
          </p>
          {change && (
            <div className={`flex items-center text-sm ${
              change.direction === 'up' ? 'text-green-600' : 'text-red-600'
            }`}>
              <span className="mr-1">
                {change.direction === 'up' ? '↗' : '↘'}
              </span>
              {change.value}% from {change.timeframe}
            </div>
          )}
        </div>
        <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${colorClasses[color]}`}>
          <Icon className="w-6 h-6" />
        </div>
      </div>
    </Card>
  );
};

/**
 * Activity Item Component
 */
interface ActivityItemProps {
  title: string;
  description: string;
  timestamp: string;
  type: 'bill' | 'user' | 'system' | 'ai';
}

const ActivityItem: React.FC<ActivityItemProps> = ({
  title,
  description,
  timestamp,
  type,
}) => {
  const typeColors = {
    bill: 'bg-blue-100 text-blue-600',
    user: 'bg-green-100 text-green-600',
    system: 'bg-red-100 text-red-600',
    ai: 'bg-purple-100 text-purple-600',
  };

  const typeIcons = {
    bill: DocumentTextIcon,
    user: UsersIcon,
    system: ServerIcon,
    ai: CpuChipIcon,
  };

  const Icon = typeIcons[type];

  return (
    <div className="flex items-start space-x-3">
      <div className={`flex-shrink-0 w-8 h-8 rounded-full ${typeColors[type]} flex items-center justify-center`}>
        <Icon className="w-4 h-4" />
      </div>
      <div className="flex-1 min-w-0">
        <p className="text-sm font-medium text-gray-900 dark:text-white">
          {title}
        </p>
        <p className="text-sm text-gray-500 dark:text-gray-400">
          {description}
        </p>
        <p className="text-xs text-gray-400 dark:text-gray-500 mt-1">
          {timestamp}
        </p>
      </div>
    </div>
  );
};

/**
 * Dashboard Component
 * 
 * Main dashboard page showing key metrics, recent activity, and quick actions.
 * 
 * Features:
 * - KPI cards with metrics
 * - Recent activity feed
 * - Quick action buttons
 * - Theme toggle
 * - Notification testing
 */
export const Dashboard: React.FC = () => {
  const { user } = useAuth();
  const { addNotification } = useNotifications();

  // Sample data - in real app this would come from API
  const kpis = [
    {
      title: 'Bills Processed Today',
      value: 23,
      change: { value: 12, direction: 'up' as const, timeframe: 'yesterday' },
      icon: DocumentTextIcon,
      color: 'blue' as const,
    },
    {
      title: 'Active Users',
      value: '1,234',
      change: { value: 5, direction: 'up' as const, timeframe: 'last week' },
      icon: UsersIcon,
      color: 'green' as const,
    },
    {
      title: 'AI Cost Today',
      value: '$45.67',
      change: { value: 8, direction: 'down' as const, timeframe: 'yesterday' },
      icon: CurrencyDollarIcon,
      color: 'yellow' as const,
    },
    {
      title: 'System Uptime',
      value: '99.9%',
      icon: ServerIcon,
      color: 'green' as const,
    },
  ];

  const activities = [
    {
      title: 'HR-1234 processed successfully',
      description: 'AI analysis completed with 95% confidence',
      timestamp: '2 minutes ago',
      type: 'bill' as const,
    },
    {
      title: 'New user registered',
      description: '<EMAIL> joined the platform',
      timestamp: '5 minutes ago',
      type: 'user' as const,
    },
    {
      title: 'AI processing queue cleared',
      description: 'All pending bills have been processed',
      timestamp: '10 minutes ago',
      type: 'ai' as const,
    },
    {
      title: 'Database backup completed',
      description: 'Scheduled backup finished successfully',
      timestamp: '1 hour ago',
      type: 'system' as const,
    },
  ];

  const handleTestNotification = (type: 'success' | 'warning' | 'error' | 'info') => {
    const messages = {
      success: { title: 'Success!', message: 'Operation completed successfully' },
      warning: { title: 'Warning', message: 'This is a warning message' },
      error: { title: 'Error', message: 'Something went wrong' },
      info: { title: 'Info', message: 'This is an informational message' },
    };

    addNotification({
      type,
      ...messages[type],
    });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Dashboard
          </h1>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Welcome back, {user?.name || 'Admin'}! Here's what's happening today.
          </p>
        </div>
        
        <div className="flex items-center gap-3">
          <ThemeToggle variant="dropdown" showSystem showLabels />
          <Button
            variant="primary"
            onClick={() => window.location.reload()}
          >
            Refresh Data
          </Button>
        </div>
      </div>

      {/* KPI Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        {kpis.map((kpi, index) => (
          <KPICard key={index} {...kpi} />
        ))}
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Activity */}
        <Card
          title="Recent Activity"
          subtitle="Latest system events and updates"
          padding="md"
        >
          <div className="space-y-4">
            {activities.map((activity, index) => (
              <ActivityItem key={index} {...activity} />
            ))}
          </div>
          <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
            <Button variant="ghost" size="sm" fullWidth>
              View All Activity
            </Button>
          </div>
        </Card>

        {/* Quick Actions */}
        <Card
          title="Quick Actions"
          subtitle="Common administrative tasks"
          padding="md"
        >
          <div className="grid grid-cols-2 gap-3">
            <Button
              variant="outline"
              size="md"
              iconLeft={DocumentTextIcon}
              onClick={() => handleTestNotification('success')}
            >
              Process Bills
            </Button>
            <Button
              variant="outline"
              size="md"
              iconLeft={UsersIcon}
              onClick={() => handleTestNotification('info')}
            >
              Manage Users
            </Button>
            <Button
              variant="outline"
              size="md"
              iconLeft={ChartBarIcon}
              onClick={() => handleTestNotification('warning')}
            >
              View Analytics
            </Button>
            <Button
              variant="outline"
              size="md"
              iconLeft={CpuChipIcon}
              onClick={() => handleTestNotification('error')}
            >
              AI Settings
            </Button>
          </div>
          
          <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
            <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
              Test Notifications
            </h4>
            <div className="grid grid-cols-2 gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleTestNotification('success')}
              >
                Success
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleTestNotification('warning')}
              >
                Warning
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleTestNotification('error')}
              >
                Error
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleTestNotification('info')}
              >
                Info
              </Button>
            </div>
          </div>
        </Card>
      </div>

      {/* System Status */}
      <Card
        title="System Status"
        subtitle="Current system health and performance"
        padding="md"
      >
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center">
            <div className="w-16 h-16 mx-auto rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center mb-2">
              <ServerIcon className="w-8 h-8 text-green-600 dark:text-green-300" />
            </div>
            <h4 className="font-medium text-gray-900 dark:text-white">API Server</h4>
            <p className="text-sm text-green-600 dark:text-green-400">Healthy</p>
          </div>
          
          <div className="text-center">
            <div className="w-16 h-16 mx-auto rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center mb-2">
              <CpuChipIcon className="w-8 h-8 text-green-600 dark:text-green-300" />
            </div>
            <h4 className="font-medium text-gray-900 dark:text-white">AI Processing</h4>
            <p className="text-sm text-green-600 dark:text-green-400">Online</p>
          </div>
          
          <div className="text-center">
            <div className="w-16 h-16 mx-auto rounded-full bg-yellow-100 dark:bg-yellow-900 flex items-center justify-center mb-2">
              <ChartBarIcon className="w-8 h-8 text-yellow-600 dark:text-yellow-300" />
            </div>
            <h4 className="font-medium text-gray-900 dark:text-white">Analytics</h4>
            <p className="text-sm text-yellow-600 dark:text-yellow-400">Degraded</p>
          </div>
        </div>
      </Card>
    </div>
  );
};

Dashboard.displayName = 'Dashboard';