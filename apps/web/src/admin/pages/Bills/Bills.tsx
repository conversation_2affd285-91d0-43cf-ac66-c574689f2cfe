import React, { useState, useEffect } from 'react';
import {
  DocumentTextIcon,
  PlusIcon,
  ArrowPathIcon,
  CheckCircleIcon,
  ExclamationCircleIcon,
  ClockIcon,
  XCircleIcon,
  CpuChipIcon,
  DocumentArrowUpIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
} from '@heroicons/react/24/outline';
import { DataTable, Column, FilterConfig } from '../../components/design-system/DataTable';
import { <PERSON><PERSON>, Card } from '../../components/design-system';
import { useAdminStore } from '../../stores/adminStore';
import { useNotifications } from '../../hooks';

/**
 * Bill Interface
 */
interface Bill {
  id: string;
  billNumber: string;
  title: string;
  congress: number;
  sessionYear: number;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'imported';
  aiProcessed: boolean;
  aiProcessedAt?: string;
  sponsor: string;
  introducedDate: string;
  lastUpdated: string;
  chamber: 'house' | 'senate';
  subjects: string[];
  summary?: string;
  fullTextUrl?: string;
  policyArea?: string;
  cosponsors: number;
}

/**
 * Mock data - in real app this would come from API
 */
const mockBills: Bill[] = [
  {
    id: '1',
    billNumber: 'HR1234',
    title: 'Climate Action and Infrastructure Investment Act',
    congress: 118,
    sessionYear: 2023,
    status: 'completed',
    aiProcessed: true,
    aiProcessedAt: '2024-01-15T10:30:00Z',
    sponsor: 'Rep. Smith (D-CA)',
    introducedDate: '2023-03-15',
    lastUpdated: '2024-01-15T10:30:00Z',
    chamber: 'house',
    subjects: ['Environment', 'Infrastructure', 'Energy'],
    summary: 'A comprehensive bill to address climate change through infrastructure investment.',
    policyArea: 'Environmental Protection',
    cosponsors: 45,
  },
  {
    id: '2',
    billNumber: 'S567',
    title: 'Healthcare Accessibility Enhancement Act',
    congress: 118,
    sessionYear: 2023,
    status: 'processing',
    aiProcessed: false,
    sponsor: 'Sen. Johnson (R-TX)',
    introducedDate: '2023-04-22',
    lastUpdated: '2024-01-14T15:45:00Z',
    chamber: 'senate',
    subjects: ['Health', 'Social Welfare'],
    policyArea: 'Health',
    cosponsors: 23,
  },
  {
    id: '3',
    billNumber: 'HR2345',
    title: 'Small Business Innovation and Growth Act',
    congress: 118,
    sessionYear: 2023,
    status: 'failed',
    aiProcessed: false,
    sponsor: 'Rep. Davis (D-NY)',
    introducedDate: '2023-05-10',
    lastUpdated: '2024-01-13T09:20:00Z',
    chamber: 'house',
    subjects: ['Commerce', 'Economics', 'Small Business'],
    policyArea: 'Commerce',
    cosponsors: 12,
  },
  {
    id: '4',
    billNumber: 'S890',
    title: 'Education Modernization and Technology Act',
    congress: 118,
    sessionYear: 2023,
    status: 'pending',
    aiProcessed: false,
    sponsor: 'Sen. Wilson (D-WA)',
    introducedDate: '2023-06-01',
    lastUpdated: '2024-01-12T14:10:00Z',
    chamber: 'senate',
    subjects: ['Education', 'Technology', 'Science'],
    policyArea: 'Education',
    cosponsors: 31,
  },
  {
    id: '5',
    billNumber: 'HR3456',
    title: 'Veterans Healthcare Improvement Act',
    congress: 118,
    sessionYear: 2023,
    status: 'completed',
    aiProcessed: true,
    aiProcessedAt: '2024-01-10T16:25:00Z',
    sponsor: 'Rep. Brown (R-FL)',
    introducedDate: '2023-07-08',
    lastUpdated: '2024-01-10T16:25:00Z',
    chamber: 'house',
    subjects: ['Armed Forces', 'Health', 'Veterans'],
    summary: 'Legislation to improve healthcare services for veterans.',
    policyArea: 'Armed Forces and National Security',
    cosponsors: 67,
  },
];

/**
 * Status Badge Component
 */
const StatusBadge: React.FC<{ status: Bill['status'] }> = ({ status }) => {
  const configs = {
    pending: { color: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200', icon: ClockIcon, label: 'Pending' },
    processing: { color: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200', icon: ArrowPathIcon, label: 'Processing' },
    completed: { color: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200', icon: CheckCircleIcon, label: 'Completed' },
    failed: { color: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200', icon: XCircleIcon, label: 'Failed' },
    imported: { color: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200', icon: DocumentArrowUpIcon, label: 'Imported' },
  };
  
  const config = configs[status];
  const Icon = config.icon;
  
  return (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
      <Icon className="w-3 h-3 mr-1" />
      {config.label}
    </span>
  );
};

/**
 * AI Processing Badge Component
 */
const AIProcessingBadge: React.FC<{ processed: boolean; processedAt?: string }> = ({ processed, processedAt }) => {
  if (!processed) {
    return (
      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200">
        <ExclamationCircleIcon className="w-3 h-3 mr-1" />
        Not Processed
      </span>
    );
  }
  
  return (
    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
      <CpuChipIcon className="w-3 h-3 mr-1" />
      AI Processed
    </span>
  );
};

/**
 * Bills Management Page
 */
export const Bills: React.FC = () => {
  const [bills, setBills] = useState<Bill[]>(mockBills);
  const [loading, setLoading] = useState(false);
  const [selectedBills, setSelectedBills] = useState<Bill[]>([]);
  const { addNotification } = useNotifications();

  // Column definitions
  const columns: Column<Bill>[] = [
    {
      id: 'billNumber',
      header: 'Bill Number',
      accessor: 'billNumber',
      sortable: true,
      filterable: true,
      width: '120px',
      cell: (bill) => (
        <div className="font-medium text-primary-600 dark:text-primary-400">
          {bill.billNumber}
        </div>
      ),
    },
    {
      id: 'title',
      header: 'Title',
      accessor: 'title',
      sortable: true,
      filterable: true,
      cell: (bill) => (
        <div>
          <div className="font-medium text-gray-900 dark:text-white">
            {bill.title}
          </div>
          <div className="text-sm text-gray-500 dark:text-gray-400">
            {bill.sponsor}
          </div>
        </div>
      ),
    },
    {
      id: 'chamber',
      header: 'Chamber',
      accessor: 'chamber',
      sortable: true,
      width: '100px',
      cell: (bill) => (
        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
          bill.chamber === 'house' 
            ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
            : 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'
        }`}>
          {bill.chamber === 'house' ? 'House' : 'Senate'}
        </span>
      ),
    },
    {
      id: 'status',
      header: 'Status',
      accessor: 'status',
      sortable: true,
      width: '120px',
      cell: (bill) => <StatusBadge status={bill.status} />,
    },
    {
      id: 'aiProcessed',
      header: 'AI Processing',
      accessor: 'aiProcessed',
      sortable: true,
      width: '140px',
      cell: (bill) => <AIProcessingBadge processed={bill.aiProcessed} processedAt={bill.aiProcessedAt} />,
    },
    {
      id: 'subjects',
      header: 'Subjects',
      accessor: 'subjects',
      cell: (bill) => (
        <div className="flex flex-wrap gap-1">
          {bill.subjects.slice(0, 2).map((subject, index) => (
            <span
              key={index}
              className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200"
            >
              {subject}
            </span>
          ))}
          {bill.subjects.length > 2 && (
            <span className="text-xs text-gray-500 dark:text-gray-400">
              +{bill.subjects.length - 2} more
            </span>
          )}
        </div>
      ),
    },
    {
      id: 'introducedDate',
      header: 'Introduced',
      accessor: 'introducedDate',
      sortable: true,
      type: 'date',
      width: '110px',
      cell: (bill) => new Date(bill.introducedDate).toLocaleDateString(),
    },
    {
      id: 'cosponsors',
      header: 'Cosponsors',
      accessor: 'cosponsors',
      sortable: true,
      type: 'number',
      width: '100px',
      align: 'center',
    },
    {
      id: 'actions',
      header: 'Actions',
      type: 'actions',
      width: '120px',
      align: 'center',
    },
  ];

  // Filter configurations
  const filters: FilterConfig[] = [
    {
      id: 'status',
      label: 'Status',
      type: 'select',
      options: [
        { value: 'pending', label: 'Pending' },
        { value: 'processing', label: 'Processing' },
        { value: 'completed', label: 'Completed' },
        { value: 'failed', label: 'Failed' },
        { value: 'imported', label: 'Imported' },
      ],
    },
    {
      id: 'chamber',
      label: 'Chamber',
      type: 'select',
      options: [
        { value: 'house', label: 'House' },
        { value: 'senate', label: 'Senate' },
      ],
    },
    {
      id: 'aiProcessed',
      label: 'AI Processing',
      type: 'select',
      options: [
        { value: 'true', label: 'Processed' },
        { value: 'false', label: 'Not Processed' },
      ],
    },
    {
      id: 'congress',
      label: 'Congress',
      type: 'number',
      placeholder: 'e.g., 118',
    },
  ];

  // Batch actions
  const batchActions = [
    {
      label: 'Process with AI',
      icon: CpuChipIcon,
      variant: 'primary' as const,
      onClick: (selectedBills: Bill[]) => {
        setLoading(true);
        addNotification({
          type: 'info',
          title: 'Processing Bills',
          message: `Starting AI processing for ${selectedBills.length} bills...`,
        });
        
        // Simulate AI processing
        setTimeout(() => {
          setBills(prev => prev.map(bill => 
            selectedBills.find(selected => selected.id === bill.id)
              ? { ...bill, status: 'processing' as const }
              : bill
          ));
          setLoading(false);
          addNotification({
            type: 'success',
            title: 'Processing Started',
            message: `AI processing initiated for ${selectedBills.length} bills.`,
          });
        }, 2000);
      },
    },
    {
      label: 'Mark as Failed',
      icon: XCircleIcon,
      variant: 'secondary' as const,
      onClick: (selectedBills: Bill[]) => {
        setBills(prev => prev.map(bill => 
          selectedBills.find(selected => selected.id === bill.id)
            ? { ...bill, status: 'failed' as const }
            : bill
        ));
        addNotification({
          type: 'warning',
          title: 'Bills Marked as Failed',
          message: `${selectedBills.length} bills marked as failed.`,
        });
      },
    },
    {
      label: 'Delete Selected',
      icon: TrashIcon,
      variant: 'danger' as const,
      onClick: (selectedBills: Bill[]) => {
        setBills(prev => prev.filter(bill => 
          !selectedBills.find(selected => selected.id === bill.id)
        ));
        addNotification({
          type: 'success',
          title: 'Bills Deleted',
          message: `${selectedBills.length} bills deleted successfully.`,
        });
      },
    },
  ];

  // Action handlers
  const handleViewBill = (bill: Bill) => {
    addNotification({
      type: 'info',
      title: 'View Bill',
      message: `Opening details for ${bill.billNumber}`,
    });
  };

  const handleEditBill = (bill: Bill) => {
    addNotification({
      type: 'info',
      title: 'Edit Bill',
      message: `Opening editor for ${bill.billNumber}`,
    });
  };

  const handleDeleteBill = (bill: Bill) => {
    setBills(prev => prev.filter(b => b.id !== bill.id));
    addNotification({
      type: 'success',
      title: 'Bill Deleted',
      message: `${bill.billNumber} deleted successfully.`,
    });
  };

  const handleRefreshData = () => {
    setLoading(true);
    addNotification({
      type: 'info',
      title: 'Refreshing Data',
      message: 'Fetching latest bill information...',
    });
    
    setTimeout(() => {
      setLoading(false);
      addNotification({
        type: 'success',
        title: 'Data Refreshed',
        message: 'Bill data updated successfully.',
      });
    }, 2000);
  };

  const handleImportBills = () => {
    addNotification({
      type: 'info',
      title: 'Import Bills',
      message: 'Opening bill import wizard...',
    });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Bills Management
          </h1>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Manage legislative bills, AI processing, and data imports
          </p>
        </div>
        
        <div className="flex items-center gap-3">
          <Button
            variant="outline"
            iconLeft={ArrowPathIcon}
            onClick={handleRefreshData}
            loading={loading}
          >
            Refresh
          </Button>
          <Button
            variant="outline"
            iconLeft={DocumentArrowUpIcon}
            onClick={handleImportBills}
          >
            Import Bills
          </Button>
          <Button
            variant="primary"
            iconLeft={PlusIcon}
            onClick={() => addNotification({
              type: 'info',
              title: 'Add Bill',
              message: 'Opening bill creation form...',
            })}
          >
            Add Bill
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card padding="md">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <DocumentTextIcon className="h-8 w-8 text-blue-600 dark:text-blue-400" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Total Bills</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{bills.length}</p>
            </div>
          </div>
        </Card>
        
        <Card padding="md">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <CpuChipIcon className="h-8 w-8 text-green-600 dark:text-green-400" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">AI Processed</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {bills.filter(b => b.aiProcessed).length}
              </p>
            </div>
          </div>
        </Card>
        
        <Card padding="md">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <ArrowPathIcon className="h-8 w-8 text-yellow-600 dark:text-yellow-400" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Processing</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {bills.filter(b => b.status === 'processing').length}
              </p>
            </div>
          </div>
        </Card>
        
        <Card padding="md">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <ClockIcon className="h-8 w-8 text-purple-600 dark:text-purple-400" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Pending</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {bills.filter(b => b.status === 'pending').length}
              </p>
            </div>
          </div>
        </Card>
      </div>

      {/* Bills Table */}
      <DataTable
        data={bills}
        columns={columns}
        loading={loading}
        searchable
        searchPlaceholder="Search bills by number, title, or sponsor..."
        filterable
        filters={filters}
        sortable
        selectable
        pagination
        pageSize={10}
        exportable
        exportFormats={['csv', 'pdf', 'json']}
        actions={{
          view: handleViewBill,
          edit: handleEditBill,
          delete: handleDeleteBill,
        }}
        batchActions={batchActions}
        onSelectionChange={setSelectedBills}
        className="min-h-[600px]"
      />
    </div>
  );
};

Bills.displayName = 'Bills';