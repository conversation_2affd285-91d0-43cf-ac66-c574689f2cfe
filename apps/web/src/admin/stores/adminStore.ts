import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';

/**
 * Admin user interface
 */
export interface AdminUser {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  role: string;
  permissions: string[];
  lastLogin?: Date;
}

/**
 * Notification interface
 */
export interface Notification {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error';
  title: string;
  message: string;
  timestamp: Date;
  read: boolean;
  actions?: Array<{
    label: string;
    handler: () => void;
  }>;
}

/**
 * Admin store state
 */
export interface AdminState {
  // User state
  user: AdminUser | null;
  isAuthenticated: boolean;
  
  // UI state
  sidebarCollapsed: boolean;
  
  // Notifications
  notifications: Notification[];
  unreadCount: number;
  
  // Loading states
  loading: {
    auth: boolean;
    navigation: boolean;
  };
  
  // Error state
  error: string | null;
}

/**
 * Admin store actions
 */
export interface AdminActions {
  // User actions
  setUser: (user: AdminUser | null) => void;
  updateUser: (updates: Partial<AdminUser>) => void;
  logout: () => void;
  
  // UI actions
  toggleSidebar: () => void;
  setSidebarCollapsed: (collapsed: boolean) => void;
  
  // Notification actions
  addNotification: (notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) => void;
  removeNotification: (id: string) => void;
  markNotificationRead: (id: string) => void;
  markAllNotificationsRead: () => void;
  clearNotifications: () => void;
  
  // Loading actions
  setLoading: (key: keyof AdminState['loading'], loading: boolean) => void;
  
  // Error actions
  setError: (error: string | null) => void;
  clearError: () => void;
}

/**
 * Combined admin store type
 */
export type AdminStore = AdminState & AdminActions;

/**
 * Initial state
 */
const initialState: AdminState = {
  user: null,
  isAuthenticated: false,
  sidebarCollapsed: false,
  notifications: [],
  unreadCount: 0,
  loading: {
    auth: false,
    navigation: false,
  },
  error: null,
};

/**
 * Admin Store
 * 
 * Central state management for the admin interface using Zustand.
 * Persists user preferences and handles authentication state.
 * 
 * Features:
 * - User authentication state
 * - UI preferences (sidebar state)
 * - Notification management
 * - Loading states
 * - Error handling
 * - Persistence for preferences
 * 
 * @example
 * ```tsx
 * function AdminComponent() {
 *   const { user, notifications, addNotification } = useAdminStore();
 *   
 *   const handleAction = () => {
 *     addNotification({
 *       type: 'success',
 *       title: 'Success',
 *       message: 'Action completed successfully'
 *     });
 *   };
 *   
 *   return <div>{user?.name}</div>;
 * }
 * ```
 */
export const useAdminStore = create<AdminStore>()(
  devtools(
    persist(
      (set, get) => ({
        ...initialState,

        // User actions
        setUser: (user) => {
          set(
            {
              user,
              isAuthenticated: !!user,
              error: null,
            },
            false,
            'setUser'
          );
        },

        updateUser: (updates) => {
          const currentUser = get().user;
          if (currentUser) {
            set(
              {
                user: { ...currentUser, ...updates },
              },
              false,
              'updateUser'
            );
          }
        },

        logout: () => {
          set(
            {
              user: null,
              isAuthenticated: false,
              notifications: [],
              unreadCount: 0,
              error: null,
            },
            false,
            'logout'
          );
        },

        // UI actions
        toggleSidebar: () => {
          set(
            (state) => ({
              sidebarCollapsed: !state.sidebarCollapsed,
            }),
            false,
            'toggleSidebar'
          );
        },

        setSidebarCollapsed: (collapsed) => {
          set(
            {
              sidebarCollapsed: collapsed,
            },
            false,
            'setSidebarCollapsed'
          );
        },

        // Notification actions
        addNotification: (notification) => {
          const id = Math.random().toString(36).substr(2, 9);
          const newNotification: Notification = {
            ...notification,
            id,
            timestamp: new Date(),
            read: false,
          };

          set(
            (state) => ({
              notifications: [newNotification, ...state.notifications],
              unreadCount: state.unreadCount + 1,
            }),
            false,
            'addNotification'
          );

          // Auto-remove non-error notifications after 5 seconds
          if (notification.type !== 'error') {
            setTimeout(() => {
              get().removeNotification(id);
            }, 5000);
          }
        },

        removeNotification: (id) => {
          set(
            (state) => {
              const notification = state.notifications.find((n) => n.id === id);
              const wasUnread = notification && !notification.read;

              return {
                notifications: state.notifications.filter((n) => n.id !== id),
                unreadCount: wasUnread ? state.unreadCount - 1 : state.unreadCount,
              };
            },
            false,
            'removeNotification'
          );
        },

        markNotificationRead: (id) => {
          set(
            (state) => {
              const notifications = state.notifications.map((n) =>
                n.id === id ? { ...n, read: true } : n
              );
              const unreadCount = notifications.filter((n) => !n.read).length;

              return {
                notifications,
                unreadCount,
              };
            },
            false,
            'markNotificationRead'
          );
        },

        markAllNotificationsRead: () => {
          set(
            (state) => ({
              notifications: state.notifications.map((n) => ({ ...n, read: true })),
              unreadCount: 0,
            }),
            false,
            'markAllNotificationsRead'
          );
        },

        clearNotifications: () => {
          set(
            {
              notifications: [],
              unreadCount: 0,
            },
            false,
            'clearNotifications'
          );
        },

        // Loading actions
        setLoading: (key, loading) => {
          set(
            (state) => ({
              loading: {
                ...state.loading,
                [key]: loading,
              },
            }),
            false,
            `setLoading:${key}`
          );
        },

        // Error actions
        setError: (error) => {
          set(
            {
              error,
            },
            false,
            'setError'
          );
        },

        clearError: () => {
          set(
            {
              error: null,
            },
            false,
            'clearError'
          );
        },
      }),
      {
        name: 'admin-store',
        // Only persist UI preferences, not sensitive data
        partialize: (state) => ({
          sidebarCollapsed: state.sidebarCollapsed,
        }),
      }
    ),
    {
      name: 'admin-store',
    }
  )
);

/**
 * Selector hooks for specific state slices
 */
export const useAdminUser = () => useAdminStore((state) => state.user);
export const useIsAuthenticated = () => useAdminStore((state) => state.isAuthenticated);
export const useNotifications = () => useAdminStore((state) => ({
  notifications: state.notifications,
  unreadCount: state.unreadCount,
  addNotification: state.addNotification,
  removeNotification: state.removeNotification,
  markNotificationRead: state.markNotificationRead,
  markAllNotificationsRead: state.markAllNotificationsRead,
  clearNotifications: state.clearNotifications,
}));
export const useSidebar = () => useAdminStore((state) => ({
  collapsed: state.sidebarCollapsed,
  toggle: state.toggleSidebar,
  setCollapsed: state.setSidebarCollapsed,
}));
export const useAdminLoading = () => useAdminStore((state) => state.loading);
export const useAdminError = () => useAdminStore((state) => ({
  error: state.error,
  setError: state.setError,
  clearError: state.clearError,
}));