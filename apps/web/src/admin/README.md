# Modern Action Admin Dashboard

## Overview

This is the redesigned admin dashboard for Modern Action, built with modern React patterns, TypeScript, and a focus on accessibility and performance.

## Architecture

### Design System
- **Design Tokens**: Centralized color, typography, and spacing values
- **Component Library**: Reusable UI components with consistent styling
- **Theme Support**: Light/dark mode with system preference detection
- **Accessibility**: WCAG 2.1 AA compliant components

### Layout Structure
```
admin/
├── components/          # Shared admin components
│   ├── design-system/   # Base design system components
│   ├── layout/          # Layout components (Sidebar, Header)
│   ├── charts/          # Data visualization components
│   └── forms/           # Form components
├── hooks/               # Custom React hooks
├── stores/              # Zustand state management
├── utils/               # Utility functions
├── types/               # TypeScript type definitions
└── pages/               # Page components
    ├── dashboard/       # Main dashboard
    ├── bills/           # Bill management
    ├── users/           # User management
    ├── analytics/       # Analytics & reporting
    ├── contact/         # Contact messages
    ├── system/          # System administration
    └── settings/        # Admin settings
```

## Getting Started

1. The new admin interface is being built in `/src/admin/`
2. Existing admin functionality remains in `/src/app/admin/` during transition
3. Components are built mobile-first and fully responsive
4. Dark mode is supported throughout

## Design Principles

1. **Progressive Disclosure**: Show most important information first
2. **Task-Oriented**: Design around common admin workflows
3. **Accessibility First**: Full keyboard navigation and screen reader support
4. **Performance**: Lazy loading and optimized rendering
5. **Consistency**: Unified design language and patterns

## Technology Stack

- **React 18** with TypeScript
- **Zustand** for state management
- **React Query** for server state
- **Tailwind CSS** for styling
- **Heroicons** for icons
- **React Router** for navigation
- **React Hook Form** for forms

## Component Documentation

Each component includes:
- TypeScript interfaces for props
- Usage examples
- Accessibility features
- Responsive behavior notes

## Development Notes

- All new components use the design system tokens
- Components are tested with Jest and React Testing Library
- Accessibility is validated with jest-axe
- Performance is monitored with bundle analysis