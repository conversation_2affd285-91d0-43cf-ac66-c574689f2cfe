import { useState, useEffect, useCallback } from 'react';
import { useUser } from '@auth0/nextjs-auth0/client';

/**
 * API Response Interface
 */
export interface ApiResponse<T = any> {
  data?: T;
  error?: string;
  loading: boolean;
  refetch: () => Promise<void>;
}

/**
 * API Request Options
 */
export interface ApiRequestOptions {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  body?: any;
  headers?: Record<string, string>;
  requireAuth?: boolean;
}

/**
 * Bills API Response Types
 */
export interface BillsResponse {
  bills: Array<{
    id: string;
    billNumber: string;
    title: string;
    congress: number;
    sessionYear: number;
    status: string;
    aiProcessed: boolean;
    aiProcessedAt?: string;
    sponsor: string;
    introducedDate: string;
    lastUpdated: string;
    chamber: string;
    subjects: string[];
    summary?: string;
    policyArea?: string;
    cosponsors: number;
  }>;
  total: number;
  page: number;
  pageSize: number;
}

/**
 * Users API Response Types
 */
export interface UsersResponse {
  users: Array<{
    id: string;
    email: string;
    name: string;
    role: string;
    isActive: boolean;
    createdAt: string;
    lastLogin?: string;
  }>;
  total: number;
  page: number;
  pageSize: number;
}

/**
 * AI Usage Stats Response
 */
export interface AIUsageResponse {
  totalCost: number;
  costToday: number;
  costThisMonth: number;
  requestsToday: number;
  requestsThisMonth: number;
  averageResponseTime: number;
  successRate: number;
  dailyStats: Array<{
    date: string;
    cost: number;
    requests: number;
  }>;
}

/**
 * System Health Response
 */
export interface SystemHealthResponse {
  status: 'healthy' | 'degraded' | 'down';
  services: Array<{
    name: string;
    status: 'healthy' | 'degraded' | 'down';
    responseTime?: number;
    lastCheck: string;
  }>;
  uptime: number;
  version: string;
}

/**
 * Custom hook for making API requests
 */
export const useApi = <T = any>(
  endpoint: string,
  options: ApiRequestOptions = {}
): ApiResponse<T> => {
  const [data, setData] = useState<T | undefined>(undefined);
  const [error, setError] = useState<string | undefined>(undefined);
  const [loading, setLoading] = useState(true);
  const { user, isLoading: userLoading } = useUser();

  const { 
    method = 'GET', 
    body, 
    headers = {}, 
    requireAuth = true 
  } = options;

  const makeRequest = useCallback(async () => {
    if (requireAuth && userLoading) {
      return; // Wait for user to load
    }

    if (requireAuth && !user) {
      setError('Authentication required');
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(undefined);

      const requestHeaders: Record<string, string> = {
        'Content-Type': 'application/json',
        ...headers,
      };

      // Add auth header if user is authenticated
      if (user && requireAuth) {
        // In a real app, you'd get the token from Auth0
        // requestHeaders.Authorization = `Bearer ${token}`;
      }

      const requestInit: RequestInit = {
        method,
        headers: requestHeaders,
      };

      if (body && method !== 'GET') {
        requestInit.body = JSON.stringify(body);
      }

      const response = await fetch(`/api/v1${endpoint}`, requestInit);
      
      if (!response.ok) {
        throw new Error(`API Error: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      setData(result);
    } catch (err) {
      console.error('API request failed:', err);
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
    } finally {
      setLoading(false);
    }
  }, [endpoint, method, body, headers, requireAuth, user, userLoading]);

  useEffect(() => {
    makeRequest();
  }, [makeRequest]);

  return {
    data,
    error,
    loading,
    refetch: makeRequest,
  };
};

/**
 * Hook for fetching bills with pagination and filtering
 */
export const useBills = (params: {
  page?: number;
  pageSize?: number;
  status?: string;
  chamber?: string;
  search?: string;
} = {}) => {
  const queryParams = new URLSearchParams();
  
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined) {
      queryParams.append(key, String(value));
    }
  });

  const endpoint = `/bills?${queryParams.toString()}`;
  
  return useApi<BillsResponse>(endpoint, { requireAuth: true });
};

/**
 * Hook for fetching users with pagination and filtering
 */
export const useUsers = (params: {
  page?: number;
  pageSize?: number;
  role?: string;
  search?: string;
} = {}) => {
  const queryParams = new URLSearchParams();
  
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined) {
      queryParams.append(key, String(value));
    }
  });

  const endpoint = `/users?${queryParams.toString()}`;
  
  return useApi<UsersResponse>(endpoint, { requireAuth: true });
};

/**
 * Hook for fetching AI usage statistics
 */
export const useAIUsage = () => {
  return useApi<AIUsageResponse>('/admin/ai-usage', { requireAuth: true });
};

/**
 * Hook for fetching system health
 */
export const useSystemHealth = () => {
  return useApi<SystemHealthResponse>('/admin/health', { requireAuth: true });
};

/**
 * Hook for making authenticated POST requests
 */
export const useApiMutation = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | undefined>(undefined);
  const { user } = useUser();

  const mutate = useCallback(async <T = any>(
    endpoint: string,
    options: ApiRequestOptions = {}
  ): Promise<T | null> => {
    try {
      setLoading(true);
      setError(undefined);

      const { method = 'POST', body, headers = {} } = options;

      const requestHeaders: Record<string, string> = {
        'Content-Type': 'application/json',
        ...headers,
      };

      // Add auth header if user is authenticated
      if (user) {
        // In a real app, you'd get the token from Auth0
        // requestHeaders.Authorization = `Bearer ${token}`;
      }

      const requestInit: RequestInit = {
        method,
        headers: requestHeaders,
      };

      if (body) {
        requestInit.body = JSON.stringify(body);
      }

      const response = await fetch(`/api/v1${endpoint}`, requestInit);
      
      if (!response.ok) {
        throw new Error(`API Error: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      return result;
    } catch (err) {
      console.error('API mutation failed:', err);
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [user]);

  return {
    mutate,
    loading,
    error,
  };
};

/**
 * Helper functions for common API operations
 */
export const billsApi = {
  /**
   * Process bills with AI
   */
  processBills: (billIds: string[]) => {
    const { mutate } = useApiMutation();
    return mutate('/bills/process', {
      method: 'POST',
      body: { billIds },
    });
  },

  /**
   * Update bill status
   */
  updateBillStatus: (billId: string, status: string) => {
    const { mutate } = useApiMutation();
    return mutate(`/bills/${billId}/status`, {
      method: 'PATCH',
      body: { status },
    });
  },

  /**
   * Delete bills
   */
  deleteBills: (billIds: string[]) => {
    const { mutate } = useApiMutation();
    return mutate('/bills/bulk-delete', {
      method: 'DELETE',
      body: { billIds },
    });
  },

  /**
   * Import bills
   */
  importBills: (source: string, options: any = {}) => {
    const { mutate } = useApiMutation();
    return mutate('/bills/import', {
      method: 'POST',
      body: { source, options },
    });
  },
};

/**
 * User management API helpers
 */
export const usersApi = {
  /**
   * Update user role
   */
  updateUserRole: (userId: string, role: string) => {
    const { mutate } = useApiMutation();
    return mutate(`/users/${userId}/role`, {
      method: 'PATCH',
      body: { role },
    });
  },

  /**
   * Deactivate user
   */
  deactivateUser: (userId: string) => {
    const { mutate } = useApiMutation();
    return mutate(`/users/${userId}/deactivate`, {
      method: 'PATCH',
    });
  },

  /**
   * Delete user
   */
  deleteUser: (userId: string) => {
    const { mutate } = useApiMutation();
    return mutate(`/users/${userId}`, {
      method: 'DELETE',
    });
  },
};