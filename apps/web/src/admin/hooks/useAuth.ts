import { useEffect } from 'react';
import { useUser } from '@auth0/nextjs-auth0/client';
import { useAdminStore, type AdminUser } from '../stores/adminStore';

/**
 * Admin role mapping
 */
const ADMIN_ROLES = ['admin', 'super_admin', 'system_admin'];

/**
 * Permission mapping based on roles
 */
const ROLE_PERMISSIONS: Record<string, string[]> = {
  super_admin: ['*'], // Full access
  admin: [
    'bills:read',
    'bills:write',
    'users:read',
    'users:write',
    'analytics:read',
    'contact:read',
    'contact:write',
    'ai:read',
    'ai:write',
  ],
  bill_manager: [
    'bills:read',
    'bills:write',
    'analytics:read',
    'ai:read',
    'ai:write',
  ],
  support_agent: [
    'users:read',
    'contact:read',
    'contact:write',
  ],
  content_manager: [
    'bills:read',
    'content:read',
    'content:write',
  ],
};

/**
 * Check if user has admin privileges
 */
function isAdminUser(user: any): boolean {
  if (!user) return false;
  
  // Check for admin role in Auth0 user metadata
  const roles = user['https://modernaction.org/roles'] || [];
  return ADMIN_ROLES.some(role => roles.includes(role));
}

/**
 * Get user permissions based on roles
 */
function getUserPermissions(roles: string[]): string[] {
  const permissions = new Set<string>();
  
  roles.forEach(role => {
    const rolePermissions = ROLE_PERMISSIONS[role] || [];
    rolePermissions.forEach(permission => permissions.add(permission));
  });
  
  return Array.from(permissions);
}

/**
 * Convert Auth0 user to AdminUser
 */
function mapAuth0UserToAdminUser(auth0User: any): AdminUser {
  const roles = auth0User['https://modernaction.org/roles'] || [];
  const primaryRole = roles[0] || 'user';
  const permissions = getUserPermissions(roles);
  
  return {
    id: auth0User.sub,
    email: auth0User.email,
    name: auth0User.name || auth0User.email,
    avatar: auth0User.picture,
    role: primaryRole,
    permissions,
    lastLogin: new Date(),
  };
}

/**
 * Auth hook return type
 */
export interface UseAuthReturn {
  /** Current authenticated user */
  user: AdminUser | null;
  /** Whether user is authenticated */
  isAuthenticated: boolean;
  /** Whether user has admin privileges */
  isAdmin: boolean;
  /** Whether auth is loading */
  loading: boolean;
  /** Auth error if any */
  error: string | null;
  /** Login function */
  login: () => void;
  /** Logout function */
  logout: () => void;
  /** Check if user has specific permission */
  hasPermission: (permission: string) => boolean;
  /** Check if user has any of the specified permissions */
  hasAnyPermission: (permissions: string[]) => boolean;
}

/**
 * Authentication Hook
 * 
 * Manages authentication state by integrating with Auth0 and the admin store.
 * Handles role-based access control and permission checking.
 * 
 * Features:
 * - Auth0 integration
 * - Role-based access control
 * - Permission checking
 * - Admin user validation
 * - Automatic state synchronization
 * 
 * @example
 * ```tsx
 * function ProtectedComponent() {
 *   const { isAuthenticated, isAdmin, hasPermission } = useAuth();
 *   
 *   if (!isAuthenticated || !isAdmin) {
 *     return <AccessDenied />;
 *   }
 *   
 *   if (!hasPermission('bills:write')) {
 *     return <ReadOnlyView />;
 *   }
 *   
 *   return <FullAccessView />;
 * }
 * ```
 */
export function useAuth(): UseAuthReturn {
  const { user: auth0User, isLoading, error: auth0Error } = useUser();
  const {
    user: adminUser,
    isAuthenticated,
    setUser,
    logout: logoutStore,
    setError,
    clearError,
  } = useAdminStore();

  // Sync Auth0 user with admin store
  useEffect(() => {
    if (isLoading) return;

    if (auth0User) {
      // Check if user has admin privileges
      if (isAdminUser(auth0User)) {
        const mappedUser = mapAuth0UserToAdminUser(auth0User);
        setUser(mappedUser);
        clearError();
      } else {
        setError('Access denied: Admin privileges required');
        setUser(null);
      }
    } else {
      setUser(null);
    }
  }, [auth0User, isLoading, setUser, setError, clearError]);

  // Handle Auth0 errors
  useEffect(() => {
    if (auth0Error) {
      setError(auth0Error.message);
    }
  }, [auth0Error, setError]);

  const login = () => {
    window.location.href = '/api/auth/login';
  };

  const logout = () => {
    logoutStore();
    window.location.href = '/api/auth/logout';
  };

  const hasPermission = (permission: string): boolean => {
    if (!adminUser) return false;
    
    // Super admin has all permissions
    if (adminUser.permissions.includes('*')) return true;
    
    // Check specific permission
    return adminUser.permissions.includes(permission);
  };

  const hasAnyPermission = (permissions: string[]): boolean => {
    return permissions.some(permission => hasPermission(permission));
  };

  return {
    user: adminUser,
    isAuthenticated: isAuthenticated && !!adminUser,
    isAdmin: !!adminUser,
    loading: isLoading,
    error: useAdminStore((state) => state.error),
    login,
    logout,
    hasPermission,
    hasAnyPermission,
  };
}