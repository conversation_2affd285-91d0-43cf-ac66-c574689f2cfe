'use client';

import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import { ArrowLeftIcon, CheckIcon, SparklesIcon, DocumentTextIcon } from '@heroicons/react/24/outline';
import { useUser } from '@auth0/nextjs-auth0/client';
import { useForm } from 'react-hook-form';
import toast from 'react-hot-toast';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';

import { Bill } from '../../../../types';
import { billApi, billActionApi, BillActionData, BillActionSubmitRequest, MessagePreviewRequest, userApi, UserProfile } from '../../../../services/apiClient';
import LoginButton from '../../../../components/auth/LoginButton';
import { BillActionSocialFlow } from '../../../../components/bills/BillActionSocialFlow';

interface BillActionFormData {
  stance: 'support' | 'oppose' | 'amend';
  selected_reasons: string[];
  custom_reasons: string[];
  personal_stories: string;
  custom_message: string;
  first_name: string;
  last_name: string;
  zip_code: string;
  email: string;
  address: string;
  city: string;
  state: string;
  save_address_for_future_actions: boolean;
}

interface MessagePreviewData {
  representatives: unknown[];
  personalized_messages: unknown[];
  stance: string;
  selected_reasons: string[];
  custom_reasons: string[];
}

export default function BillActionPage() {
  const params = useParams();
  const router = useRouter();
  const { user, isLoading: isAuthLoading } = useUser();
  
  const [bill, setBill] = useState<Bill | null>(null);
  const [billActionData, setBillActionData] = useState<BillActionData | null>(null);
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [isLoadingData, setIsLoadingData] = useState(true);
  const [isLoadingProfile, setIsLoadingProfile] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [currentStep, setCurrentStep] = useState<'stance' | 'reasons' | 'contact' | 'edit_and_send' | 'amplify'>('stance');
  
  // Debug step changes
  useEffect(() => {
    console.log('🔧 DEBUG - Step changed to:', currentStep);
  }, [currentStep]);
  const [messagePreview, setMessagePreview] = useState<MessagePreviewData | null>(null);
  const [isLoadingPreview, setIsLoadingPreview] = useState(false);
  const [aiProgress, setAiProgress] = useState(0);
  const [aiProgressMessage, setAiProgressMessage] = useState('');
  const [customReasonInput, setCustomReasonInput] = useState('');
  const [isRegeneratingMessage, setIsRegeneratingMessage] = useState(false);
  const [rewriteFeedback, setRewriteFeedback] = useState('');
  const [actionNetworkEmbed, setActionNetworkEmbed] = useState<any>(null);
  const [isProcessingActionNetwork, setIsProcessingActionNetwork] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    setValue,
    getValues
  } = useForm<BillActionFormData>({
    mode: 'onChange',
    defaultValues: {
      stance: undefined,
      selected_reasons: [],
      custom_reasons: [],
      personal_stories: '',
      custom_message: '',
      first_name: '',
      last_name: '',
      zip_code: '',
      email: '',
      address: '',
      city: '',
      state: '',
      save_address_for_future_actions: false
    }
  });

  const watchedStance = watch('stance');
  const watchedReasons = watch('selected_reasons');
  const watchedCustomReasons = watch('custom_reasons');
  const watchedZipCode = watch('zip_code');
  const watchedEmail = watch('email');

  // Generate storage key based on bill slug for persistence
  const storageKey = `billAction_${params.slug}`;

  // Enhanced utility function to determine AND filter target officials based on bill stage and chamber
  const getTargetOfficialsInfo = (bill: Bill, allOfficials?: any[]) => {
    if (!bill) return { 
      type: 'both', 
      description: 'your representatives', 
      chamber: 'both',
      filteredOfficials: allOfficials || [] 
    };

    // Determine current legislative stage and appropriate contacts
    const chamber = bill.chamber;
    const status = bill.status;
    
    // House bills in House: Contact House Representatives
    if (chamber === 'house' && ['introduced', 'committee', 'floor'].includes(status)) {
      const filteredOfficials = allOfficials ? 
        allOfficials.filter(official => 
          official.chamber === 'house' || 
          official.title?.toLowerCase().includes('representative') ||
          official.role === 'representative'
        ) : [];
      
      return {
        type: 'representatives',
        description: 'your House Representative',
        chamber: 'house',
        explanation: 'This bill is currently in the House of Representatives',
        actionNetworkChamber: 'house',
        filteredOfficials
      };
    }
    
    // Senate bills in Senate: Contact Senators  
    if (chamber === 'senate' && ['introduced', 'committee', 'floor'].includes(status)) {
      const filteredOfficials = allOfficials ? 
        allOfficials.filter(official => 
          official.chamber === 'senate' || 
          official.title?.toLowerCase().includes('senator') ||
          official.role === 'senator'
        ) : [];
      
      return {
        type: 'senators',
        description: 'your Senators',
        chamber: 'senate',
        explanation: 'This bill is currently in the Senate',
        actionNetworkChamber: 'senate',
        filteredOfficials
      };
    }
    
    // Bills that have passed one chamber: Contact the other chamber
    if (status === 'passed') {
      if (chamber === 'house') {
        const filteredOfficials = allOfficials ? 
          allOfficials.filter(official => 
            official.chamber === 'senate' || 
            official.title?.toLowerCase().includes('senator') ||
            official.role === 'senator'
          ) : [];
        
        return {
          type: 'senators',
          description: 'your Senators',
          chamber: 'senate',
          explanation: 'This bill has passed the House and is now in the Senate',
          actionNetworkChamber: 'senate',
          filteredOfficials
        };
      } else if (chamber === 'senate') {
        const filteredOfficials = allOfficials ? 
          allOfficials.filter(official => 
            official.chamber === 'house' || 
            official.title?.toLowerCase().includes('representative') ||
            official.role === 'representative'
          ) : [];
        
        return {
          type: 'representatives', 
          description: 'your House Representative',
          chamber: 'house',
          explanation: 'This bill has passed the Senate and is now in the House',
          actionNetworkChamber: 'house',
          filteredOfficials
        };
      }
    }
    
    // For signed/vetoed bills or unclear stage: Contact both
    return {
      type: 'both',
      description: 'your representatives',
      chamber: 'both',
      explanation: 'Contact both your Representative and Senators about this bill',
      actionNetworkChamber: 'both',
      filteredOfficials: allOfficials || [] // For 'both', include all officials
    };
  };

  // Get the current target officials info with filtering
  const targetOfficialsInfo = getTargetOfficialsInfo(bill, messagePreview?.representatives);
  
  // Helper function to generate chamber-aware greeting
  const getChamberAwareGreeting = (targetInfo: any, messageCount: number) => {
    if (messageCount <= 1) {
      // Single message, keep specific greeting
      return null;
    }
    
    // Multiple messages, use chamber-specific generic greeting
    if (targetInfo.chamber === 'house') {
      return 'Dear Representative,';
    } else if (targetInfo.chamber === 'senate') {
      return 'Dear Senators,';
    } else {
      return 'Dear Representatives,';
    }
  };

  // Helper function to get chamber-aware message count
  const getChamberAwareMessageCount = () => {
    const filteredCount = targetOfficialsInfo.filteredOfficials?.length;
    const totalCount = messagePreview?.personalized_messages?.length;
    
    // If we have filtered officials, use that count, otherwise fall back to total
    return filteredCount || totalCount || 0;
  };

  // Hidden Action Network submission function
  const submitToActionNetworkHidden = async (actionNetworkData: any, formData: BillActionFormData): Promise<void> => {
    return new Promise((resolve, reject) => {
      console.log('🔧 DEBUG - Submitting to Action Network hidden:', actionNetworkData);
      
      // Create a hidden iframe to submit to Action Network
      const iframe = document.createElement('iframe');
      iframe.style.display = 'none';
      iframe.style.position = 'absolute';
      iframe.style.left = '-9999px';
      iframe.style.top = '-9999px';
      iframe.style.width = '1px';
      iframe.style.height = '1px';
      
      // Set up message listener to detect when Action Network submission completes
      const messageHandler = (event: MessageEvent) => {
        // Only listen to messages from Action Network domain
        if (event.origin.includes('actionnetwork.org') || event.data?.type === 'action-network-success') {
          console.log('🔧 DEBUG - Action Network submission success detected');
          window.removeEventListener('message', messageHandler);
          document.body.removeChild(iframe);
          resolve();
        } else if (event.data?.type === 'action-network-error') {
          console.error('🔧 DEBUG - Action Network submission error detected');
          window.removeEventListener('message', messageHandler);
          document.body.removeChild(iframe);
          reject(new Error('Action Network submission failed'));
        }
      };
      
      window.addEventListener('message', messageHandler);
      
      // Set up iframe onload to submit the form programmatically
      iframe.onload = () => {
        try {
          // Wait a bit for the iframe to fully load, then try to auto-submit
          setTimeout(() => {
            try {
              const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;
              if (iframeDoc) {
                // Try to find and auto-submit the Action Network form
                const form = iframeDoc.querySelector('form');
                if (form) {
                  // Fill in the form fields with our data
                  const emailField = iframeDoc.querySelector('input[name*="email"], input[type="email"]') as HTMLInputElement;
                  const firstNameField = iframeDoc.querySelector('input[name*="first"], input[name*="given"]') as HTMLInputElement;
                  const lastNameField = iframeDoc.querySelector('input[name*="last"], input[name*="family"]') as HTMLInputElement;
                  const zipField = iframeDoc.querySelector('input[name*="zip"], input[name*="postal"]') as HTMLInputElement;
                  const messageField = iframeDoc.querySelector('textarea[name*="message"], textarea[name*="comment"]') as HTMLTextAreaElement;
                  
                  if (emailField) emailField.value = formData.email;
                  if (firstNameField) firstNameField.value = formData.first_name;
                  if (lastNameField) lastNameField.value = formData.last_name;
                  if (zipField) zipField.value = formData.zip_code;
                  if (messageField) messageField.value = formData.custom_message || '';
                  
                  // Submit the form
                  form.submit();
                  
                  // Since we can't always detect submission success reliably,
                  // assume success after a reasonable delay
                  setTimeout(() => {
                    console.log('🔧 DEBUG - Assuming Action Network submission success after delay');
                    window.removeEventListener('message', messageHandler);
                    document.body.removeChild(iframe);
                    resolve();
                  }, 3000);
                } else {
                  // No form found, assume direct submission worked
                  console.log('🔧 DEBUG - No form found in iframe, assuming direct submission');
                  resolve();
                }
              } else {
                // Can't access iframe content due to CORS, assume success
                console.log('🔧 DEBUG - Cannot access iframe content (CORS), assuming success');
                setTimeout(() => {
                  window.removeEventListener('message', messageHandler);
                  document.body.removeChild(iframe);
                  resolve();
                }, 2000);
              }
            } catch (error) {
              console.log('🔧 DEBUG - Error accessing iframe content, assuming success:', error);
              // CORS or other error accessing iframe - assume success
              setTimeout(() => {
                window.removeEventListener('message', messageHandler);
                document.body.removeChild(iframe);
                resolve();
              }, 2000);
            }
          }, 1000);
        } catch (error) {
          console.error('🔧 DEBUG - Error in iframe onload:', error);
          reject(error);
        }
      };
      
      // Set up error handling
      iframe.onerror = (error) => {
        console.error('🔧 DEBUG - Iframe error:', error);
        window.removeEventListener('message', messageHandler);
        document.body.removeChild(iframe);
        reject(error);
      };
      
      // Add iframe to DOM and load Action Network URL
      document.body.appendChild(iframe);
      iframe.src = actionNetworkData.iframe_url;
      
      // Fallback timeout in case nothing else works
      setTimeout(() => {
        if (document.body.contains(iframe)) {
          console.log('🔧 DEBUG - Action Network submission timeout, assuming success');
          window.removeEventListener('message', messageHandler);
          document.body.removeChild(iframe);
          resolve();
        }
      }, 10000); // 10 second fallback
    });
  };
  
  // Save form state to sessionStorage whenever form values change
  useEffect(() => {
    if (params.slug && typeof window !== 'undefined') {
      const formData = getValues();
      const stateToSave = {
        formData,
        currentStep,
        customReasonInput,
        timestamp: Date.now()
      };
      
      // Only save if we have meaningful data (not initial empty state)
      if (formData.stance || formData.selected_reasons.length > 0 || currentStep !== 'stance') {
        sessionStorage.setItem(storageKey, JSON.stringify(stateToSave));
      }
    }
  }, [watchedStance, watchedReasons, watchedCustomReasons, watchedEmail, currentStep, customReasonInput, storageKey, getValues, params.slug]);

  // Restore form state from sessionStorage on component mount
  useEffect(() => {
    if (params.slug && typeof window !== 'undefined') {
      try {
        const savedState = sessionStorage.getItem(storageKey);
        if (savedState) {
          const parsedState = JSON.parse(savedState);
          
          // Check if saved state is not too old (expire after 1 hour)
          const isExpired = Date.now() - parsedState.timestamp > 60 * 60 * 1000;
          
          if (!isExpired && parsedState.formData) {
            // Restore form values
            Object.keys(parsedState.formData).forEach((key) => {
              setValue(key as keyof BillActionFormData, parsedState.formData[key]);
            });
            
            // Restore UI state
            if (parsedState.currentStep) {
              setCurrentStep(parsedState.currentStep);
            }
            if (parsedState.customReasonInput) {
              setCustomReasonInput(parsedState.customReasonInput);
            }
          } else {
            // Clean up expired data
            sessionStorage.removeItem(storageKey);
          }
        }
      } catch (error) {
        console.warn('Failed to restore form state:', error);
        sessionStorage.removeItem(storageKey);
      }
    }
  }, [params.slug, setValue, storageKey]);

  // Clear saved state when component unmounts or form is submitted successfully
  const clearSavedState = () => {
    if (typeof window !== 'undefined') {
      sessionStorage.removeItem(storageKey);
    }
  };

  // Cleanup on component unmount
  useEffect(() => {
    return () => {
      // Don't auto-clear on unmount since user might be going through auth flow
      // Only clear on explicit success or manual clear
    };
  }, []);

  // Load bill and bill action data
  useEffect(() => {
    const loadData = async () => {
      if (params.slug) {
        try {
          setIsLoadingData(true);

          // Use the slug directly as bill ID (since slug is actually the UUID)
          const billId = params.slug as string;

          // Get the full bill data and action data directly
          const [billData, actionData] = await Promise.all([
            billApi.getBillById(billId),
            billActionApi.getBillActionData(billId)
          ]);

          setBill(billData);
          setBillActionData(actionData);
        } catch (error) {
          console.error('Failed to load data:', error);
          toast.error('Failed to load bill information. Please try again.');
        } finally {
          setIsLoadingData(false);
        }
      }
    };

    loadData();
  }, [params.slug]);

  // Load user profile when authenticated
  useEffect(() => {
    const loadUserProfile = async () => {
      console.log('🔧 DEBUG - loadUserProfile triggered:', { user: !!user, isAuthLoading, userEmail: user?.email });
      
      if (user && !isAuthLoading) {
        try {
          setIsLoadingProfile(true);
          console.log('🔧 DEBUG - Calling userApi.getProfile()...');
          const profile = await userApi.getProfile();
          console.log('🔧 DEBUG - Profile loaded:', profile);
          setUserProfile(profile);
          
          // Auto-populate form with saved data
          if (profile.first_name) setValue('first_name', profile.first_name);
          if (profile.last_name) setValue('last_name', profile.last_name);
          // Always set email for authenticated users (from profile or Auth0)
          const emailToSet = profile.email || user?.email || '';
          console.log('🔧 DEBUG - Setting email:', emailToSet);
          setValue('email', emailToSet);
          
          // Debug: Check if setValue worked by watching the form values
          setTimeout(() => {
            const currentFormValues = getValues();
            console.log('🔧 DEBUG - Form values after setValue:', currentFormValues.email);
          }, 100);
          if (profile.zip_code) setValue('zip_code', profile.zip_code);
          if (profile.address) setValue('address', profile.address);
          if (profile.city) setValue('city', profile.city);
          if (profile.state) setValue('state', profile.state);
          // Set save preference based on whether user has saved address data
          setValue('save_address_for_future_actions', profile.save_address_for_future_actions || false);
        } catch (error) {
          console.error('🔧 DEBUG - Failed to load user profile:', error);
          // Fallback: try to use Auth0 user data directly
          if (user?.email) {
            console.log('🔧 DEBUG - Using Auth0 fallback email:', user.email);
            setValue('email', user.email);
            
            // Debug: Check if fallback setValue worked  
            setTimeout(() => {
              const currentFormValues = getValues();
              console.log('🔧 DEBUG - Fallback form values after setValue:', currentFormValues.email);
            }, 100);
          }
        } finally {
          setIsLoadingProfile(false);
        }
      }
    };

    loadUserProfile();
  }, [user, isAuthLoading, setValue]);

  const handleStanceSelect = (stance: 'support' | 'oppose' | 'amend') => {
    setValue('stance', stance);
    setValue('selected_reasons', []); // Reset reasons when stance changes
    setCurrentStep('reasons');
    
    // Force save state immediately after stance selection
    setTimeout(() => {
      const formData = getValues();
      const stateToSave = {
        formData,
        currentStep: 'reasons',
        customReasonInput,
        timestamp: Date.now()
      };
      if (typeof window !== 'undefined') {
        sessionStorage.setItem(storageKey, JSON.stringify(stateToSave));
      }
    }, 0);
  };

  const handleNextStep = () => {
    console.log('🔧 DEBUG - handleNextStep called from step:', currentStep);
    switch (currentStep) {
      case 'stance':
        setCurrentStep('reasons');
        break;
      case 'reasons':
        setCurrentStep('contact');
        break;
      case 'contact':
        console.log('🔧 DEBUG - Moving directly from contact to edit_and_send (AI will load there)');
        setCurrentStep('edit_and_send');
        break;
      case 'edit_and_send':
        console.log('🔧 DEBUG - Moving from edit_and_send to amplify');
        setCurrentStep('amplify');
        break;
    }
  };

  const handlePrevStep = () => {
    switch (currentStep) {
      case 'reasons':
        setCurrentStep('stance');
        break;
      case 'contact':
        setCurrentStep('reasons');
        break;
      case 'edit_and_send':
        setCurrentStep('contact');
        break;
      case 'amplify':
        setCurrentStep('edit_and_send');
        break;
    }
  };

  const handleReasonToggle = (reason: string) => {
    const currentReasons = getValues('selected_reasons');
    const newReasons = currentReasons.includes(reason)
      ? currentReasons.filter(r => r !== reason)
      : [...currentReasons, reason];
    setValue('selected_reasons', newReasons);
  };

  const handleAddCustomReason = () => {
    if (!customReasonInput.trim()) return;
    const currentCustomReasons = getValues('custom_reasons');
    const newCustomReasons = [...currentCustomReasons, customReasonInput.trim()];
    setValue('custom_reasons', newCustomReasons);
    setCustomReasonInput('');
  };

  const handleRewriteMessage = async () => {
    if (!rewriteFeedback.trim() || !bill || !messagePreview) {
      toast.error('Please provide feedback for the message rewrite');
      return;
    }

    setIsRegeneratingMessage(true);

    try {
      const formData = getValues();
      
      // Create rewrite request with user feedback
      const rewriteData: MessagePreviewRequest = {
        bill_id: bill.id,
        stance: formData.stance,
        selected_reasons: formData.selected_reasons,
        custom_reasons: [...(formData.custom_reasons || []), `Rewrite feedback: ${rewriteFeedback}`],
        personal_stories: formData.personal_stories,
        first_name: formData.first_name || user?.given_name || '',
        last_name: formData.last_name || user?.family_name || '',
        zip_code: formData.zip_code
      };

      const result = await billActionApi.previewMessage(rewriteData);
      
      // Update the message with the rewritten version
      if (result.personalized_messages?.[0]?.body) {
        let rewrittenMessage = result.personalized_messages[0].body;
        
        // Handle multiple messages with chamber-aware greeting
        if (result.personalized_messages?.length > 1) {
          const chamberGreeting = getChamberAwareGreeting(targetOfficialsInfo, result.personalized_messages?.length);
          if (chamberGreeting) {
            rewrittenMessage = rewrittenMessage
              .replace(/Dear (Senator|Representative) [^,]+,/, chamberGreeting)
              .replace(/Dear (Sen\.|Rep\.) [^,]+,/, chamberGreeting);
          }
        }
        
        setValue('custom_message', rewrittenMessage);
        setRewriteFeedback('');
        toast.success('Message rewritten based on your feedback!');
      } else {
        toast.error('Failed to rewrite message. Please try again.');
      }

    } catch (error) {
      console.error('Failed to rewrite message:', error);
      toast.error('Failed to rewrite message. Please try again.');
    } finally {
      setIsRegeneratingMessage(false);
    }
  };

  const handleGenerateNewVersion = async () => {
    if (!bill) {
      toast.error('Bill information not loaded');
      return;
    }

    setIsRegeneratingMessage(true);

    try {
      const formData = getValues();
      
      // Generate completely new version with additional context
      const newVersionData: MessagePreviewRequest = {
        bill_id: bill.id,
        stance: formData.stance,
        selected_reasons: formData.selected_reasons,
        custom_reasons: [...(formData.custom_reasons || []), 'Generate alternative version with different tone and approach'],
        personal_stories: formData.personal_stories,
        first_name: formData.first_name || user?.given_name || '',
        last_name: formData.last_name || user?.family_name || '',
        zip_code: formData.zip_code
      };

      const result = await billActionApi.previewMessage(newVersionData);
      
      if (result.personalized_messages?.[0]?.body) {
        let newMessage = result.personalized_messages[0].body;
        
        if (result.personalized_messages?.length > 1) {
          const chamberGreeting = getChamberAwareGreeting(targetOfficialsInfo, result.personalized_messages?.length);
          if (chamberGreeting) {
            newMessage = newMessage
              .replace(/Dear (Senator|Representative) [^,]+,/, chamberGreeting)
              .replace(/Dear (Sen\.|Rep\.) [^,]+,/, chamberGreeting);
          }
        }
        
        setValue('custom_message', newMessage);
        toast.success('Generated a new version of your message!');
      } else {
        toast.error('Failed to generate new message version. Please try again.');
      }

    } catch (error) {
      console.error('Failed to generate new message version:', error);
      toast.error('Failed to generate new message version. Please try again.');
    } finally {
      setIsRegeneratingMessage(false);
    }
  };

  // Auto-trigger AI generation when we reach ai_generation step
  useEffect(() => {
    if (currentStep === 'edit_and_send' && !isLoadingPreview && !messagePreview && bill) {
      // For signed-in users, trigger immediately using their saved address
      if (user && user.user_metadata?.zip_code) {
        handlePreviewMessage();
      }
      // For guest users, trigger when they have entered valid contact info
      else {
        const formData = getValues();
        if (formData.zip_code && /^\d{5}(-\d{4})?$/.test(formData.zip_code) && 
            formData.email && formData.address && formData.city && formData.state &&
            formData.first_name && formData.last_name) {
          handlePreviewMessage();
        }
      }
    }
  }, [currentStep, isLoadingPreview, messagePreview, user, bill]);

  // Pre-populate AI message when messagePreview is available
  useEffect(() => {
    if (messagePreview && messagePreview.personalized_messages?.[0]?.body) {
      const currentMessage = getValues('custom_message');
      // Only set if the field is empty (initial load)
      if (!currentMessage) {
        let messageToUse = messagePreview.personalized_messages[0].body;
        
        // If multiple messages, create a chamber-aware generic greeting version
        if (messagePreview.personalized_messages?.length > 1) {
          const chamberGreeting = getChamberAwareGreeting(targetOfficialsInfo, messagePreview.personalized_messages?.length);
          if (chamberGreeting) {
            // Replace specific "Dear Senator/Representative [Name]" with chamber-aware greeting
            messageToUse = messageToUse
              .replace(/Dear (Senator|Representative) [^,]+,/, chamberGreeting)
              .replace(/Dear (Sen\.|Rep\.) [^,]+,/, chamberGreeting);
          }
        }
        
        setValue('custom_message', messageToUse);
      }
    }
  }, [messagePreview, setValue, getValues]);

  const handlePreviewMessage = async (customZipCode?: string) => {
    if (!bill) {
      toast.error('Bill information not loaded');
      return;
    }

    setIsLoadingPreview(true);
    setAiProgress(0);
    setAiProgressMessage('Looking up your representatives...');

    // Simulate progress updates to keep user engaged
    const progressInterval = setInterval(() => {
      setAiProgress(prev => {
        if (prev < 90) {
          const increment = Math.random() * 15 + 5; // Random increment between 5-20%
          const newProgress = Math.min(prev + increment, 90);

          // Update progress message based on progress
          if (newProgress < 30) {
            setAiProgressMessage('Looking up your representatives...');
          } else if (newProgress < 60) {
            setAiProgressMessage('Analyzing bill content...');
          } else if (newProgress < 85) {
            setAiProgressMessage('Crafting personalized messages...');
          } else {
            setAiProgressMessage('Finalizing your message...');
          }

          return newProgress;
        }
        return prev;
      });
    }, 300); // Update every 300ms for smooth progress

    try {
      const formData = getValues();

      // Use custom zip code if provided, otherwise try user's saved address, fallback to form data
      let zipCode = customZipCode || formData.zip_code;
      if (!zipCode && user?.user_metadata?.zip_code) {
        zipCode = user.user_metadata.zip_code;
        setValue('zip_code', zipCode); // Update form with user's saved zip
      }

      if (!zipCode) {
        throw new Error('ZIP code is required to find your representatives');
      }

      const previewData: MessagePreviewRequest = {
        bill_id: bill.id,
        stance: formData.stance,
        selected_reasons: formData.selected_reasons,
        custom_reasons: formData.custom_reasons,
        personal_stories: formData.personal_stories,
        first_name: formData.first_name || user?.given_name || '',
        last_name: formData.last_name || user?.family_name || '',
        zip_code: zipCode
      };

      // Debug logging to verify request data
      console.log('Sending preview request:', previewData);
      console.log('ZIP code being sent:', zipCode);
      console.log('Form data:', formData);

      // First, test direct officials lookup to see if we get real representatives
      try {
        const officialsTest = await billActionApi.lookupOfficials(zipCode);
        console.log('Direct officials lookup result:', officialsTest);
      } catch (officialsError) {
        console.log('Direct officials lookup failed:', officialsError);
      }

      const result = await billActionApi.previewMessage(previewData);

      // Debug the actual result structure
      console.log('Preview message result:', result);
      console.log('Representatives in result:', result.representatives);
      console.log('Personalized messages:', result.personalized_messages);

      // Complete the progress and set result immediately
      clearInterval(progressInterval);
      setAiProgress(100);
      setAiProgressMessage('Message ready!');

      // Brief delay to show completion before showing the edit interface
      setTimeout(() => {
        setMessagePreview(result);
        setAiProgress(0);
        setAiProgressMessage('');
        setIsLoadingPreview(false);
        // User is already on edit_and_send step, just show the loaded content
      }, 800);

    } catch (error) {
      console.error('Failed to preview message:', error);

      // Clear interval and reset progress
      clearInterval(progressInterval);
      setAiProgress(0);
      setAiProgressMessage('');
      setIsLoadingPreview(false);

      if (error.message?.includes('timeout')) {
        toast.error('AI personalization is taking longer than expected. Please try again.');
      } else {
        toast.error('Failed to generate message preview. Please try again.');
      }
    }
  };

  const onFormSubmit = async (data: BillActionFormData) => {
    console.log('🔧 DEBUG - onFormSubmit called from step:', currentStep);
    console.log('🔧 DEBUG - Form data:', data);
    
    if (!bill) {
      toast.error('Bill information not loaded');
      return;
    }

    try {
      setIsSubmitting(true);

      const submitData: BillActionSubmitRequest = {
        bill_id: bill.id,
        stance: data.stance,
        selected_reasons: data.selected_reasons,
        custom_reasons: data.custom_reasons || [],
        personal_stories: data.personal_stories || undefined,
        custom_message: data.custom_message || undefined,
        first_name: data.first_name,
        last_name: data.last_name,
        zip_code: data.zip_code,
        email: data.email,
        address: data.address || undefined,
        city: data.city || undefined,
        state: data.state || undefined,
        // Pass target chamber info to backend for proper Action Network form routing
        target_chamber: targetOfficialsInfo.actionNetworkChamber
      };

      const result = await billActionApi.submitBillAction(submitData);

      if (result.success) {
        console.log('🔧 DEBUG - Form submission result:', result);
        
        // Save user profile if they're authenticated and consented
        if (user && data.save_address_for_future_actions) {
          try {
            await userApi.updateProfile({
              first_name: data.first_name,
              last_name: data.last_name,
              zip_code: data.zip_code,
              address: data.address,
              city: data.city,
              state: data.state,
              save_address_for_future_actions: data.save_address_for_future_actions
            });
          } catch (error) {
            console.warn('Failed to save user profile:', error);
            // Don't fail the action submission if profile save fails
          }
        }
        
        // Clear saved form state on successful submission
        clearSavedState();
        
        // Handle Action Network integration completely behind the scenes
        if (result.action_network_embed?.iframe_url && result.delivery_summary?.requires_user_completion) {
          console.log('🔧 DEBUG - Action Network integration required, submitting behind the scenes');
          setIsProcessingActionNetwork(true);
          
          try {
            // Submit to Action Network programmatically using a hidden iframe
            await submitToActionNetworkHidden(result.action_network_embed, data);
            
            // Show success message only after Action Network submission is complete
            const totalMessages = result.officials_contacted || result.delivery_summary?.total_targets || 0;
            toast.success(`Successfully sent your message to ${totalMessages} representative${totalMessages > 1 ? 's' : ''}!`);
            setCurrentStep('amplify');
          } catch (error) {
            console.error('Action Network submission failed:', error);
            toast.error('Message delivery failed. Please try again.');
          } finally {
            setIsProcessingActionNetwork(false);
          }
        } else {
          // Direct delivery - show success message and go to amplify
          const totalMessages = result.officials_contacted || result.delivery_summary?.total_targets || 0;
          toast.success(`Successfully sent your message to ${totalMessages} representative${totalMessages > 1 ? 's' : ''}!`);
          setCurrentStep('amplify');
        }
      } else {
        toast.error('Failed to send your message. Please try again.');
      }

    } catch (error) {
      console.error('Failed to submit bill action:', error);
      toast.error('Failed to send your message. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const getAvailableReasons = (): string[] => {
    if (!bill) return [];

    switch (watchedStance) {
      case 'support':
        return bill.support_reasons || [];
      case 'oppose':
        return bill.oppose_reasons || [];
      case 'amend':
        return bill.amend_reasons || [];
      default:
        return [];
    }
  };

  if (isLoadingData) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="flex items-center gap-3">
          <svg className="animate-spin h-8 w-8 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <span className="text-lg text-gray-600">Loading bill information...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50/30 overflow-x-hidden">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-md border-b border-gray-100 sticky top-0 z-40">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-20">
            <div className="flex items-center gap-6">
              <button
                onClick={() => router.back()}
                className="group p-3 rounded-xl text-gray-400 hover:text-gray-700 hover:bg-gray-50 transition-all duration-200 hover:scale-105 active:scale-95"
              >
                <ArrowLeftIcon className="h-5 w-5 group-hover:-translate-x-0.5 transition-transform duration-200" />
              </button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900 tracking-tight">Take Action</h1>
                <p className="text-sm text-gray-500 font-medium">Make your voice heard on this legislation</p>
              </div>
            </div>
            
            {/* Progress Indicator */}
            <div className="hidden lg:flex items-center">
              <div className="flex items-center bg-white/60 backdrop-blur-md rounded-full px-6 py-3 border border-gray-200/50 shadow-lg">
                {[
                  { key: 'stance', label: 'Position', icon: '🎯' },
                  { key: 'reasons', label: 'Reasons', icon: '💭' },
                  { key: 'contact', label: 'Contact', icon: '📍' },
                  { key: 'edit_and_send', label: 'Send', icon: '📤' },
                  { key: 'amplify', label: 'Amplify', icon: '📢' }
                ].map((step, index) => {
                  const stepIndex = ['stance', 'reasons', 'contact', 'edit_and_send', 'amplify'].indexOf(currentStep);
                  const isCompleted = index < stepIndex;
                  const isCurrent = currentStep === step.key;
                  
                  return (
                    <div key={step.key} className="flex items-center">
                      <div className="flex items-center gap-2">
                        <div className={`relative flex items-center justify-center w-8 h-8 rounded-full text-xs font-bold transition-all duration-300 ${
                          isCurrent
                            ? 'bg-blue-600 text-white shadow-lg shadow-blue-600/25 scale-110 ring-2 ring-blue-600/30'
                            : isCompleted
                            ? 'bg-emerald-500 text-white shadow-md shadow-emerald-500/25'
                            : 'bg-gray-200 text-gray-500'
                        }`}>
                          {isCompleted ? (
                            <CheckIcon className="w-4 h-4" />
                          ) : (
                            <span>{step.icon}</span>
                          )}
                        </div>
                        <span className={`text-xs font-medium transition-colors duration-200 ${
                          isCurrent ? 'text-blue-700' : isCompleted ? 'text-emerald-700' : 'text-gray-500'
                        }`}>
                          {step.label}
                        </span>
                      </div>
                      {index < 5 && (
                        <div className={`w-8 h-px mx-3 transition-all duration-300 ${
                          isCompleted ? 'bg-emerald-400' : 'bg-gray-300'
                        }`} />
                      )}
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-12 lg:gap-16">
          {/* Left Column - Bill Information (Sticky) */}
          {currentStep === 'stance' && (
            <div className="lg:col-span-5 w-full">
              <div className="sticky top-28 space-y-6">
                <BillInfoCard bill={bill} />
              </div>
            </div>
          )}

          {/* Right Column - Action Flow */}
          <div className={`w-full ${currentStep === 'stance' ? 'lg:col-span-7' : 'lg:col-span-12'}`}>
            <Card className="overflow-hidden border-0 shadow-2xl">
              <form>
                {/* Step Content */}
                <CardContent className="p-10">
                {currentStep === 'stance' && (
                  <StanceSelectionStep
                    watchedStance={watchedStance}
                    onStanceSelect={handleStanceSelect}
                  />
                )}

                {currentStep === 'reasons' && (
                  <ReasonsStep
                    watchedStance={watchedStance}
                    watchedReasons={watchedReasons}
                    watchedCustomReasons={watchedCustomReasons}
                    customReasonInput={customReasonInput}
                    setCustomReasonInput={setCustomReasonInput}
                    availableReasons={getAvailableReasons()}
                    onReasonToggle={handleReasonToggle}
                    onAddCustomReason={handleAddCustomReason}
                    register={register}
                  />
                )}

                {currentStep === 'contact' && (
                  <ContactStep
                    user={user}
                    register={register}
                    errors={errors}
                    userProfile={userProfile}
                    isLoadingProfile={isLoadingProfile}
                    watchedEmail={watchedEmail}
                  />
                )}

                {currentStep === 'edit_and_send' && (
                  <EditAndSendStep
                    bill={bill}
                    messagePreview={messagePreview}
                    targetOfficialsInfo={targetOfficialsInfo}
                    register={register}
                    errors={errors}
                    user={user}
                    isSubmitting={isSubmitting || isProcessingActionNetwork}
                    isProcessingActionNetwork={isProcessingActionNetwork}
                    watchedStance={watchedStance}
                    watchedReasons={watchedReasons}
                    watchedCustomReasons={watchedCustomReasons}
                    getValues={getValues}
                    setValue={setValue}
                    rewriteFeedback={rewriteFeedback}
                    setRewriteFeedback={setRewriteFeedback}
                    isRegeneratingMessage={isRegeneratingMessage}
                    handleRewriteMessage={handleRewriteMessage}
                    handleGenerateNewVersion={handleGenerateNewVersion}
                    isLoadingPreview={isLoadingPreview}
                    aiProgress={aiProgress}
                    aiProgressMessage={aiProgressMessage}
                  />
                )}

                {currentStep === 'amplify' && bill && (
                  <div className="space-y-6">
                    <BillActionSocialFlow
                      bill={bill}
                      stance={watchedStance as 'support' | 'oppose' | 'amend'}
                      userLocation={{
                        zip_code: getValues('zip_code'),
                        city: getValues('city'),
                        state: getValues('state')
                      }}
                      representatives={targetOfficialsInfo.filteredOfficials || messagePreview?.representatives || []}
                      customReasons={[...watchedReasons, ...watchedCustomReasons]}
                      personalStory={getValues('personal_stories')}
                      onComplete={() => {
                        // When amplify flow is complete, redirect to bills page
                        toast.success('Thank you for amplifying your voice! Every action counts.');
                        setTimeout(() => {
                          router.push('/bills');
                        }, 2000);
                      }}
                    />
                  </div>
                )}

                {/* Navigation Footer */}
                {currentStep !== 'amplify' && (
                <div className="border-t border-gray-100/50 bg-gradient-to-r from-gray-50/80 to-gray-100/40 px-10 py-8">
                  <div className="flex items-center justify-between">
                    <button
                      onClick={handlePrevStep}
                      disabled={currentStep === 'stance'}
                      className={`group flex items-center gap-2 px-6 py-3 rounded-2xl font-semibold transition-all duration-200 ${
                        currentStep === 'stance' 
                          ? 'opacity-40 cursor-not-allowed bg-gray-100 text-gray-400' 
                          : 'bg-gray-100 text-gray-700 hover:bg-gray-200 hover:text-gray-900 hover:scale-105 active:scale-95'
                      }`}
                    >
                      <span className={`transition-transform duration-200 ${
                        currentStep !== 'stance' ? 'group-hover:-translate-x-1' : ''
                      }`}>←</span>
                      Previous
                    </button>

                    {(() => {
                      console.log('🔧 DEBUG - Button render - currentStep:', currentStep, 'isSubmitting:', isSubmitting);
                      return currentStep === 'edit_and_send' ? (
                        <button
                          type="submit"
                          disabled={isSubmitting || isProcessingActionNetwork}
                          className={`group flex items-center gap-3 px-8 py-4 rounded-2xl font-bold text-lg transition-all duration-200 transform ${
                            isSubmitting 
                              ? 'bg-gray-400 cursor-not-allowed text-white' 
                              : 'bg-gradient-to-r from-emerald-500 to-emerald-600 hover:from-emerald-600 hover:to-emerald-700 text-white shadow-lg hover:shadow-xl hover:scale-105 active:scale-95'
                          }`}
                        >
                        {isSubmitting ? (
                          <>
                            <svg className="animate-spin h-5 w-5" fill="none" viewBox="0 0 24 24">
                              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            {isProcessingActionNetwork ? 'Delivering messages...' : 'Sending...'}
                          </>
                        ) : (
                          <>
                            <span className="text-xl">📤</span>
                            Send Messages
                            <span className="group-hover:translate-x-1 transition-transform duration-200">→</span>
                          </>
                        )}
                        </button>
                      ) : (
                        <button
                          type="button"
                          onClick={handleNextStep}
                          disabled={currentStep === 'stance' && !watchedStance}
                          className={`group flex items-center gap-3 px-8 py-4 rounded-2xl font-bold text-lg transition-all duration-200 transform ${
                            currentStep === 'stance' && !watchedStance 
                              ? 'opacity-40 cursor-not-allowed bg-gray-100 text-gray-400' 
                              : 'bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white shadow-lg hover:shadow-xl hover:scale-105 active:scale-95'
                          }`}
                        >
                          Continue
                          <span className={`transition-transform duration-200 ${
                            !(currentStep === 'stance' && !watchedStance) ? 'group-hover:translate-x-1' : ''
                          }`}>→</span>
                        </button>
                      );
                    })()}
                  </div>
                </div>
              )}
                </CardContent>
              </form>
            </Card>
          </div>
        </div>
      </main>
    </div>
  );
}

// Bill Information Card Component
function BillInfoCard({ bill }: { bill: Bill | null }) {
  if (!bill) {
    return (
      <div className="bg-white rounded-2xl shadow-sm border border-gray-200 overflow-hidden">
        <div className="animate-pulse">
          <div className="h-24 bg-gradient-to-r from-gray-300 to-gray-400"></div>
          <div className="p-6 space-y-4">
            <div className="h-6 bg-gray-200 rounded w-3/4"></div>
            <div className="space-y-2">
              <div className="h-4 bg-gray-200 rounded"></div>
              <div className="h-4 bg-gray-200 rounded w-5/6"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full space-y-6">
      {/* Header Card */}
      <div className="w-full bg-white/60 backdrop-blur-sm rounded-2xl border-2 border-gray-200/50 overflow-hidden shadow-lg">
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 px-6 py-5 border-b border-blue-100/50">
          <div className="flex items-start justify-between">
            <div>
              <div className="inline-flex items-center gap-2 mb-2">
                <span className="text-xs font-semibold px-3 py-1 bg-blue-100 text-blue-700 rounded-full uppercase tracking-wide">
                  {bill.chamber} • {bill.session_year}
                </span>
              </div>
              <h2 className="text-xl font-bold text-gray-900 tracking-tight">{bill.bill_number}</h2>
              <p className="text-gray-600 mt-1 text-sm leading-relaxed font-medium">{bill.title}</p>
            </div>
          </div>
        </div>
        
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-700 capitalize">
              {bill.status}
            </span>
            <span className="text-sm text-gray-500 uppercase tracking-wide font-medium">
              {bill.state}
            </span>
          </div>
          
          {/* Timeline */}
          <div className="space-y-4">
            <h4 className="text-sm font-semibold text-gray-700 mb-3">Legislative Progress</h4>
            <BillTimeline bill={bill} />
          </div>
        </div>
      </div>

      {/* Quick Summary */}
      {bill.simple_summary && (
        <BillDetailCard
          title="Quick Summary"
          icon="📋"
          variant="blue"
        >
          <p className="leading-relaxed">{bill.simple_summary}</p>
        </BillDetailCard>
      )}

      {/* What This Bill Does */}
      {bill.summary_what_does && (
        <BillDetailCard
          title={bill.summary_what_does.title || "What This Bill Does"}
          icon="⚖️"
          variant="purple"
        >
          <p className="leading-relaxed mb-3">{bill.summary_what_does.content}</p>
          {bill.summary_what_does.key_points && bill.summary_what_does.key_points.length > 0 && (
            <div className="space-y-2">
              {bill.summary_what_does.key_points.map((point, index) => (
                <div key={index} className="flex items-start gap-2">
                  <div className="w-1.5 h-1.5 rounded-full bg-indigo-400 mt-2 flex-shrink-0"></div>
                  <p className="text-sm">{point}</p>
                </div>
              ))}
            </div>
          )}
        </BillDetailCard>
      )}

      {/* Who This Affects */}
      {bill.summary_who_affects && (
        <BillDetailCard
          title={bill.summary_who_affects.title || "Who This Affects"}
          icon="👥"
          bgColor="bg-green-50"
          borderColor="border-green-200"
          iconBg="bg-green-100"
          titleColor="text-green-900"
          textColor="text-green-800"
        >
          <p className="leading-relaxed mb-4">{bill.summary_who_affects.content}</p>
          {bill.summary_who_affects.affected_groups && bill.summary_who_affects.affected_groups.length > 0 && (
            <div className="flex flex-wrap gap-2">
              {bill.summary_who_affects.affected_groups.map((group, index) => (
                <span key={index} className="bg-green-200 text-green-800 px-3 py-1 rounded-full text-xs font-medium">
                  {group}
                </span>
              ))}
            </div>
          )}
        </BillDetailCard>
      )}

      {/* Why It Matters */}
      {bill.summary_why_matters && (
        <BillDetailCard
          title={bill.summary_why_matters.title || "Why It Matters"}
          icon="💡"
          bgColor="bg-purple-50"
          borderColor="border-purple-200"
          iconBg="bg-purple-100"
          titleColor="text-purple-900"
          textColor="text-purple-800"
        >
          <p className="leading-relaxed mb-4">{bill.summary_why_matters.content}</p>
          
          {bill.summary_why_matters.benefits && bill.summary_why_matters.benefits.length > 0 && (
            <div className="mb-4">
              <h5 className="font-semibold text-green-700 mb-2 text-sm">✅ Potential Benefits:</h5>
              <div className="space-y-1">
                {bill.summary_why_matters.benefits.map((benefit, index) => (
                  <div key={index} className="flex items-start gap-2">
                    <div className="w-1.5 h-1.5 rounded-full bg-green-400 mt-2 flex-shrink-0"></div>
                    <p className="text-sm text-green-700">{benefit}</p>
                  </div>
                ))}
              </div>
            </div>
          )}

          {bill.summary_why_matters.concerns && bill.summary_why_matters.concerns.length > 0 && (
            <div>
              <h5 className="font-semibold text-red-700 mb-2 text-sm">⚠️ Potential Concerns:</h5>
              <div className="space-y-1">
                {bill.summary_why_matters.concerns.map((concern, index) => (
                  <div key={index} className="flex items-start gap-2">
                    <div className="w-1.5 h-1.5 rounded-full bg-red-400 mt-2 flex-shrink-0"></div>
                    <p className="text-sm text-red-700">{concern}</p>
                  </div>
                ))}
              </div>
            </div>
          )}
        </BillDetailCard>
      )}
    </div>
  );
}

// Reusable Bill Detail Card Component
function BillDetailCard({ 
  title, 
  icon, 
  variant = 'blue',
  children 
}: {
  title: string;
  icon: string;
  variant?: 'blue' | 'green' | 'purple' | 'amber';
  children: React.ReactNode;
}) {
  const variantStyles = {
    blue: {
      gradient: 'from-blue-500 to-indigo-600',
      bg: 'from-blue-50 to-indigo-50',
      text: 'text-blue-900',
      badge: 'bg-blue-100 text-blue-800 border-blue-200'
    },
    green: {
      gradient: 'from-emerald-500 to-green-600', 
      bg: 'from-emerald-50 to-green-50',
      text: 'text-emerald-900',
      badge: 'bg-emerald-100 text-emerald-800 border-emerald-200'
    },
    purple: {
      gradient: 'from-purple-500 to-violet-600',
      bg: 'from-purple-50 to-violet-50', 
      text: 'text-purple-900',
      badge: 'bg-purple-100 text-purple-800 border-purple-200'
    },
    amber: {
      gradient: 'from-amber-500 to-orange-600',
      bg: 'from-amber-50 to-orange-50',
      text: 'text-amber-900', 
      badge: 'bg-amber-100 text-amber-800 border-amber-200'
    }
  };
  
  const styles = variantStyles[variant];
  
  return (
    <Card className={`w-full border border-gray-200/50 shadow-sm bg-white/60 backdrop-blur-sm hover:shadow-md transition-all duration-200`}>
      <CardContent className="p-6">
        <div className="flex items-start gap-4">
          <div className={`flex-shrink-0 w-10 h-10 bg-gradient-to-br ${styles.gradient} rounded-lg flex items-center justify-center shadow-sm`}>
            <span className="text-white text-lg">{icon}</span>
          </div>
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-3">
              <h4 className={`text-lg font-bold ${styles.text}`}>{title}</h4>
            </div>
            <div className={`${styles.text} leading-[1.7] text-sm`}>
              {children}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

// Bill Timeline Component
function BillTimeline({ bill }: { bill: Bill }) {
  // Define typical legislative steps
  const legislativeSteps = [
    { key: 'introduced', label: 'Introduced', description: 'Bill introduced in chamber' },
    { key: 'committee', label: 'Committee Review', description: 'Under committee consideration' },
    { key: 'floor_vote', label: 'Floor Vote', description: 'Voted on by chamber' },
    { key: 'passed_chamber', label: 'Passed Chamber', description: 'Approved by originating chamber' },
    { key: 'other_chamber', label: 'Other Chamber', description: 'Sent to other chamber' },
    { key: 'passed_both', label: 'Passed Both Chambers', description: 'Approved by both chambers' },
    { key: 'signed', label: 'Signed into Law', description: 'Signed by executive' },
  ];

  // Determine current step based on bill status
  const getCurrentStep = (status: string) => {
    const statusLower = status.toLowerCase();
    if (statusLower.includes('introduced')) return 0;
    if (statusLower.includes('committee')) return 1;
    if (statusLower.includes('floor') || statusLower.includes('vote')) return 2;
    if (statusLower.includes('passed')) return 3;
    if (statusLower.includes('signed') || statusLower.includes('enacted')) return 6;
    return 0; // Default to introduced
  };

  const currentStepIndex = getCurrentStep(bill.status);

  return (
    <div className="space-y-3">
      {legislativeSteps.map((step, index) => {
        const isCompleted = index <= currentStepIndex;
        const isCurrent = index === currentStepIndex;
        const isUpcoming = index > currentStepIndex;

        return (
          <div key={step.key} className="flex items-center gap-3">
            {/* Timeline dot */}
            <div className="flex flex-col items-center">
              <div className={`w-3 h-3 rounded-full border-2 transition-all duration-200 ${
                isCompleted
                  ? 'bg-blue-500 border-blue-500'
                  : isCurrent
                  ? 'bg-white border-blue-500 ring-2 ring-blue-200'
                  : 'bg-gray-100 border-gray-300'
              }`} />
              {index < legislativeSteps.length - 1 && (
                <div className={`w-0.5 h-6 mt-1 transition-colors duration-200 ${
                  isCompleted ? 'bg-blue-300' : 'bg-gray-200'
                }`} />
              )}
            </div>
            
            {/* Step content */}
            <div className="flex-1 pb-6">
              <div className="flex items-center gap-2">
                <span className={`text-sm font-medium transition-colors duration-200 ${
                  isCompleted || isCurrent ? 'text-gray-900' : 'text-gray-500'
                }`}>
                  {step.label}
                </span>
                {isCurrent && (
                  <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-700">
                    Current
                  </span>
                )}
              </div>
              <p className={`text-xs mt-1 transition-colors duration-200 ${
                isCompleted || isCurrent ? 'text-gray-600' : 'text-gray-400'
              }`}>
                {step.description}
              </p>
            </div>
          </div>
        );
      })}
    </div>
  );
}

// Step Components
function StanceSelectionStep({ 
  watchedStance, 
  onStanceSelect 
}: { 
  watchedStance: string | undefined; 
  onStanceSelect: (stance: 'support' | 'oppose' | 'amend') => void; 
}) {
  return (
    <div className="space-y-16">
      <div className="text-center space-y-6">
        <div className="inline-flex items-center gap-2 px-4 py-2 bg-blue-50 text-blue-700 rounded-full text-sm font-medium mb-4">
          <span>Step 1 of 6</span>
          <span className="w-1 h-1 bg-blue-400 rounded-full"></span>
          <span>~2 minutes to make your voice heard</span>
        </div>
        <h1 className="text-5xl font-bold text-gray-900 tracking-tight leading-tight">
          What's your position?
        </h1>
        <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-[1.7] font-medium">
          Choose your stance on this legislation. We'll use this to craft a personalized message to your representatives.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
        {[
          { 
            key: 'support', 
            title: 'Support', 
            description: 'I want this bill to pass as written',
            icon: '✓',
            gradient: 'from-emerald-50 to-green-50',
            hoverGradient: 'from-emerald-100 to-green-100',
            border: 'border-emerald-200',
            hoverBorder: 'border-emerald-300',
            ringColor: 'ring-emerald-500/30',
            shadowColor: 'shadow-emerald-500/10',
            iconBg: 'bg-emerald-500',
            textColor: 'text-emerald-900',
            titleColor: 'text-emerald-800'
          },
          { 
            key: 'oppose', 
            title: 'Oppose', 
            description: 'I don\'t want this bill to pass',
            icon: '✕',
            gradient: 'from-rose-50 to-red-50',
            hoverGradient: 'from-rose-100 to-red-100',
            border: 'border-rose-200',
            hoverBorder: 'border-rose-300',
            ringColor: 'ring-rose-500/30',
            shadowColor: 'shadow-rose-500/10',
            iconBg: 'bg-rose-500',
            textColor: 'text-rose-900',
            titleColor: 'text-rose-800'
          },
          { 
            key: 'amend', 
            title: 'Needs Changes', 
            description: 'I support with modifications',
            icon: '~',
            gradient: 'from-amber-50 to-orange-50',
            hoverGradient: 'from-amber-100 to-orange-100',
            border: 'border-amber-200',
            hoverBorder: 'border-amber-300',
            ringColor: 'ring-amber-500/30',
            shadowColor: 'shadow-amber-500/10',
            iconBg: 'bg-amber-500',
            textColor: 'text-amber-900',
            titleColor: 'text-amber-800'
          }
        ].map((stance) => (
          <Button
            key={stance.key}
            type="button"
            variant="ghost"
            size="xl"
            onClick={() => onStanceSelect(stance.key as 'support' | 'oppose' | 'amend')}
            className={`group relative overflow-hidden rounded-2xl p-6 h-auto min-h-[220px] w-full text-left whitespace-normal transition-all duration-300 hover:scale-[1.01] hover:-translate-y-0.5 active:scale-95 border-2 ${
              watchedStance === stance.key
                ? `ring-3 ${stance.ringColor} shadow-lg ${stance.shadowColor} ${stance.hoverBorder}`
                : `${stance.border} hover:${stance.hoverBorder} hover:shadow-md hover:shadow-gray-100/50`
            }`}
          >
            <div className={`absolute inset-0 bg-gradient-to-br transition-all duration-300 ${
              watchedStance === stance.key ? stance.hoverGradient : stance.gradient
            }`}></div>
            
            <div className="relative h-full flex flex-col min-h-[180px] py-4">
              <div className="flex items-start justify-start mb-4">
                <div className={`w-5 h-5 ${stance.iconBg} rounded-full flex items-center justify-center text-white text-sm font-bold group-hover:scale-110 transition-transform duration-300`}>
                  {stance.icon}
                </div>
              </div>
              
              <div className="flex-1 flex flex-col justify-center text-left space-y-3">
                <h3 className={`text-2xl font-bold ${stance.titleColor} tracking-tight`}>{stance.title}</h3>
                <p className={`${stance.textColor} text-base leading-relaxed font-medium break-words whitespace-normal overflow-visible`}>{stance.description}</p>
              </div>
            </div>
          </Button>
        ))}
      </div>
    </div>
  );
}

function ReasonsStep({ 
  watchedStance, 
  watchedReasons, 
  watchedCustomReasons, 
  customReasonInput, 
  setCustomReasonInput, 
  availableReasons, 
  onReasonToggle, 
  onAddCustomReason, 
  register 
}: any) {
  return (
    <div className="space-y-8">
      <div className="text-center">
        <h2 className="heading-1 text-gray-900 mb-4">
          Why do you {watchedStance} this bill?
        </h2>
        <p className="body-base text-gray-600 max-w-2xl mx-auto">
          Share your reasons to make your message more impactful. This step is optional but recommended.
        </p>
      </div>

      {/* Predefined Reasons */}
      {availableReasons.length > 0 && (
        <div className="space-y-4">
          <h3 className="heading-3 text-gray-900">Select reasons that apply</h3>
          <div className="grid grid-cols-1 gap-3">
            {availableReasons.map((reason: string, index: number) => (
              <label
                key={index}
                className="flex items-start p-4 border border-gray-200 rounded-xl hover:bg-gray-50 cursor-pointer transition-colors"
              >
                <input
                  type="checkbox"
                  checked={watchedReasons.includes(reason)}
                  onChange={() => onReasonToggle(reason)}
                  className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <span className="ml-3 body-sm text-gray-700">{reason}</span>
              </label>
            ))}
          </div>
        </div>
      )}

      {/* Custom Reasons */}
      <div className="space-y-4">
        <h3 className="heading-3 text-gray-900">Add your own reasons</h3>
        <div className="flex gap-3">
          <input
            type="text"
            value={customReasonInput}
            onChange={(e) => setCustomReasonInput(e.target.value)}
            placeholder="Enter your custom reason..."
            className="form-input flex-1"
            onKeyPress={(e) => {
              if (e.key === 'Enter') {
                e.preventDefault();
                onAddCustomReason();
              }
            }}
          />
          <button
            type="button"
            onClick={onAddCustomReason}
            disabled={!customReasonInput.trim()}
            className="btn-primary px-6"
          >
            Add
          </button>
        </div>

        {watchedCustomReasons.length > 0 && (
          <div className="space-y-2">
            {watchedCustomReasons.map((reason: string, index: number) => (
              <div
                key={index}
                className="flex items-center justify-between p-3 bg-blue-50 border border-blue-200 rounded-lg"
              >
                <span className="body-sm text-blue-900">{reason}</span>
                <button
                  type="button"
                  onClick={() => {
                    // Remove custom reason logic would go here
                  }}
                  className="text-blue-600 hover:text-blue-800"
                >
                  ×
                </button>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Personal Story */}
      <div className="space-y-4">
        <h3 className="heading-3 text-gray-900">Share your personal story (optional)</h3>
        <p className="body-sm text-gray-600">
          Personal stories make your message more compelling. Share how this bill affects you, your family, or your community.
        </p>
        <textarea
          {...register('personal_stories')}
          rows={4}
          placeholder="Example: As a small business owner, this bill would help me provide better healthcare for my employees..."
          className="form-textarea"
        />
      </div>
    </div>
  );
}

function ContactStep({ user, register, errors, userProfile, isLoadingProfile, watchedEmail }: any) {
  return (
    <div className="space-y-8">
      <div className="text-center">
        <h2 className="heading-1 text-gray-900 mb-4">Your Contact Information</h2>
        <p className="body-base text-gray-600 max-w-2xl mx-auto">
          We need your contact details to send your message to the right representatives and confirm delivery.
        </p>
      </div>

      <div className="max-w-2xl mx-auto space-y-6">
        {!user && (
          <div className="bg-blue-50 border border-blue-200 rounded-xl p-6 text-center">
            <h3 className="heading-3 text-blue-900 mb-2">Sign in for a better experience</h3>
            <p className="body-sm text-blue-700 mb-4">
              Save your information and track your advocacy history
            </p>
            <LoginButton className="btn-primary bg-blue-600 hover:bg-blue-700">
              Sign In / Sign Up
            </LoginButton>
            <p className="body-sm text-blue-600 mt-3">or continue as guest below</p>
          </div>
        )}

        {/* Name Fields */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="form-label">
              First Name *
            </label>
            <input
              {...register('first_name', {
                required: 'First name is required'
              })}
              type="text"
              placeholder="John"
              defaultValue={user?.given_name || ""}
              className="form-input"
            />
            {errors.first_name && (
              <p className="form-error">{errors.first_name.message}</p>
            )}
          </div>

          <div>
            <label className="form-label">
              Last Name *
            </label>
            <input
              {...register('last_name', {
                required: 'Last name is required'
              })}
              type="text"
              placeholder="Doe"
              defaultValue={user?.family_name || ""}
              className="form-input"
            />
            {errors.last_name && (
              <p className="form-error">{errors.last_name.message}</p>
            )}
          </div>
        </div>

        {/* Email and ZIP */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="form-label">
              Email Address *
            </label>
            <input
              {...register('email', {
                required: 'Email address is required',
                pattern: {
                  value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                  message: 'Please enter a valid email address'
                }
              })}
              type="email"
              placeholder="<EMAIL>"
              value={watchedEmail || ''}
              defaultValue={user?.email || ''}
              className="form-input"
            />
            {errors.email && (
              <p className="form-error">{errors.email.message}</p>
            )}
          </div>

          <div>
            <label className="form-label">
              ZIP Code *
            </label>
            <input
              {...register('zip_code', {
                required: 'ZIP code is required',
                pattern: {
                  value: /^\d{5}(-\d{4})?$/,
                  message: 'Please enter a valid ZIP code'
                }
              })}
              type="text"
              placeholder="12345"
              className="form-input"
            />
            {errors.zip_code && (
              <p className="form-error">{errors.zip_code.message}</p>
            )}
          </div>
        </div>

        {/* Address */}
        <div>
          <label className="form-label">
            Street Address *
          </label>
          <input
            {...register('address', {
              required: 'Street address is required'
            })}
            type="text"
            placeholder="123 Main St"
            className="form-input"
          />
          {errors.address && (
            <p className="form-error">{errors.address.message}</p>
          )}
        </div>

        {/* City and State */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="form-label">
              City *
            </label>
            <input
              {...register('city', {
                required: 'City is required'
              })}
              type="text"
              placeholder="San Francisco"
              className="form-input"
            />
            {errors.city && (
              <p className="form-error">{errors.city.message}</p>
            )}
          </div>

          <div>
            <label className="form-label">
              State *
            </label>
            <input
              {...register('state', {
                required: 'State is required'
              })}
              type="text"
              placeholder="CA"
              className="form-input"
            />
            {errors.state && (
              <p className="form-error">{errors.state.message}</p>
            )}
          </div>
        </div>

        {/* Privacy Controls - Only show for signed-in users */}
        {user && (
          <div className="bg-green-50 border border-green-200 rounded-xl p-6 space-y-4">
            <div className="flex items-start space-x-3">
              <input
                {...register('save_address_for_future_actions')}
                type="checkbox"
                id="save_address"
                className="mt-1 h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
              />
              <div className="flex-1">
                <label htmlFor="save_address" className="block heading-4 text-green-900 cursor-pointer">
                  💾 Save my information for future actions
                </label>
                <p className="body-sm text-green-700 mt-1">
                  Speed up future advocacy by saving your contact details securely.
                </p>
              </div>
            </div>
            
            <div className="bg-white rounded-lg p-4 space-y-3">
              <h4 className="heading-5 text-gray-900 flex items-center">
                🔒 Your privacy is our priority
              </h4>
              <div className="space-y-2 body-sm text-gray-600">
                <p className="flex items-start">
                  <span className="text-green-600 mr-2">✓</span>
                  <span>Your address is <strong>encrypted</strong> and stored securely</span>
                </p>
                <p className="flex items-start">
                  <span className="text-green-600 mr-2">✓</span>
                  <span>We <strong>never sell</strong> your personal information</span>
                </p>
                <p className="flex items-start">
                  <span className="text-green-600 mr-2">✓</span>
                  <span>Used <strong>only</strong> to contact your representatives</span>
                </p>
                <p className="flex items-start">
                  <span className="text-green-600 mr-2">✓</span>
                  <span>You can <strong>delete</strong> your saved information anytime</span>
                </p>
              </div>
              <div className="pt-2 border-t border-gray-200">
                <p className="body-xs text-gray-500">
                  By checking this box, you agree to save your contact information according to our privacy policy. 
                  You can change this preference or delete your saved data anytime in your profile settings.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Guest User Privacy Notice */}
        {!user && (
          <div className="bg-blue-50 border border-blue-200 rounded-xl p-6">
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0">
                🔒
              </div>
              <div>
                <h4 className="heading-5 text-blue-900 mb-2">Privacy Protection</h4>
                <div className="space-y-1 body-sm text-blue-800">
                  <p>• Your information is used only to send your message</p>
                  <p>• We don't store guest user data after submission</p>
                  <p>• Sign in to save your info for future actions</p>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

function AIGenerationStep({ isLoadingPreview, aiProgress, aiProgressMessage, messagePreview }: any) {
  return (
    <div className="space-y-8">
      <div className="text-center">
        <h2 className="heading-1 text-gray-900 mb-4">Crafting Your Message</h2>
        <p className="body-base text-gray-600 max-w-2xl mx-auto">
          Our AI is creating a personalized message based on your position and story.
        </p>
      </div>

      <div className="max-w-2xl mx-auto">
        {isLoadingPreview ? (
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-8 text-center">
            <div className="flex items-center justify-center mb-6">
              <SparklesIcon className="w-12 h-12 text-blue-600 animate-spin" />
            </div>
            
            <h3 className="heading-2 text-blue-900 mb-4">AI is writing your letter...</h3>
            
            <div className="w-full bg-blue-200 rounded-full h-3 mb-4">
              <div
                className="bg-blue-600 h-3 rounded-full transition-all duration-300 ease-out"
                style={{ width: `${aiProgress}%` }}
              ></div>
            </div>
            
            <p className="body-sm text-blue-800 mb-6">{aiProgressMessage}</p>
            
            <div className="text-left space-y-2 body-sm text-blue-700">
              <p>✨ Finding your representatives</p>
              <p>🎯 Analyzing your position and story</p>
              <p>📝 Crafting personalized messages</p>
            </div>
          </div>
        ) : messagePreview ? (
          <div className="bg-green-50 border border-green-200 rounded-2xl p-8 text-center">
            <CheckIcon className="w-12 h-12 text-green-600 mx-auto mb-4" />
            <h3 className="heading-2 text-green-900 mb-2">Message Ready!</h3>
            <p className="body-base text-green-700">
              Found {targetOfficialsInfo.filteredOfficials?.length || messagePreview?.representatives?.length || 0} representative{((targetOfficialsInfo.filteredOfficials?.length || messagePreview?.representatives?.length || 0) > 1) ? 's' : ''} for your area.
              Your personalized message is ready for review.
            </p>
          </div>
        ) : (
          <div className="bg-gray-50 border border-gray-200 rounded-2xl p-8 text-center">
            <p className="body-base text-gray-600">Waiting to generate your message...</p>
          </div>
        )}
      </div>
    </div>
  );
}

function EditAndSendStep({ 
  bill,
  messagePreview, 
  targetOfficialsInfo,
  register, 
  errors, 
  user, 
  isSubmitting, 
  isProcessingActionNetwork,
  watchedStance, 
  watchedReasons, 
  watchedCustomReasons, 
  getValues, 
  setValue, 
  rewriteFeedback, 
  setRewriteFeedback, 
  isRegeneratingMessage, 
  handleRewriteMessage, 
  handleGenerateNewVersion,
  isLoadingPreview,
  aiProgress,
  aiProgressMessage
}: any) {
  
  // Helper function to get chamber-aware message count (local to this component)
  const getChamberAwareMessageCount = () => {
    const filteredCount = targetOfficialsInfo.filteredOfficials?.length;
    const totalCount = messagePreview?.personalized_messages?.length;
    
    // If we have filtered officials, use that count, otherwise fall back to total
    return filteredCount || totalCount || 0;
  };
  
  // We'll show the page immediately and have inline loading for the message

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 py-12 space-y-8">
        
        {/* Header Section */}
        <div className="text-center space-y-4">
          <div className="inline-flex items-center gap-2 px-4 py-2 bg-white/80 backdrop-blur-sm rounded-full border border-slate-200 text-slate-600 text-sm font-medium">
            <div className="w-2 h-2 bg-emerald-500 rounded-full animate-pulse"></div>
            Step 3 of 3
          </div>
          <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-slate-900 via-blue-900 to-indigo-900 bg-clip-text text-transparent leading-tight">
            Review & Send
          </h1>
          <p className="text-xl text-slate-600 max-w-2xl mx-auto leading-relaxed">
            Your personalized message is ready. Review, customize, and send it to {targetOfficialsInfo.description}.
          </p>
        </div>

        {/* Bill Stage & Target Officials Banner */}
        <div className="max-w-4xl mx-auto">
          <Card className="border-0 bg-gradient-to-r from-blue-50 via-indigo-50 to-purple-50 backdrop-blur-sm shadow-lg border border-blue-100">
            <CardContent className="p-6">
              <div className="flex items-start gap-4">
                <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center flex-shrink-0">
                  <svg className="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                  </svg>
                </div>
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-2">
                    <h3 className="text-lg font-semibold text-slate-900">
                      {bill?.bill_number} • {bill?.chamber === 'house' ? 'House' : bill?.chamber === 'senate' ? 'Senate' : 'Congressional'} {bill?.status}
                    </h3>
                    <div className={`px-3 py-1 rounded-full text-xs font-medium ${
                      targetOfficialsInfo.chamber === 'house' ? 'bg-green-100 text-green-700' :
                      targetOfficialsInfo.chamber === 'senate' ? 'bg-blue-100 text-blue-700' :
                      'bg-purple-100 text-purple-700'
                    }`}>
                      {targetOfficialsInfo.chamber === 'house' ? 'House Representatives' :
                       targetOfficialsInfo.chamber === 'senate' ? 'Senate' : 'Both Chambers'}
                    </div>
                  </div>
                  <p className="text-slate-700 leading-relaxed">
                    <strong className="text-slate-900">{targetOfficialsInfo.explanation}</strong>
                  </p>
                  <p className="text-sm text-slate-600 mt-2">
                    Your message will be delivered to {targetOfficialsInfo.description} through the appropriate legislative channels.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content Grid */}
        <div className="grid lg:grid-cols-3 gap-8">
          
          {/* Left Column - Recipients & Summary */}
          <div className="lg:col-span-1 space-y-6">
            
            {/* Recipients Card */}
            <Card className="border-0 bg-white/80 backdrop-blur-sm shadow-xl shadow-blue-100/50">
              <CardContent className="p-6">
                <div className="flex items-center gap-3 mb-4">
                  <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${
                    targetOfficialsInfo.chamber === 'house' ? 'bg-gradient-to-r from-green-500 to-emerald-600' :
                    targetOfficialsInfo.chamber === 'senate' ? 'bg-gradient-to-r from-blue-500 to-indigo-600' :
                    'bg-gradient-to-r from-purple-500 to-violet-600'
                  }`}>
                    <svg className="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.196-2.121M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.196-2.121M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="font-semibold text-slate-900">
                      {targetOfficialsInfo.chamber === 'house' ? 'Your Representative' :
                       targetOfficialsInfo.chamber === 'senate' ? 'Your Senators' : 
                       'Your Representatives'}
                    </h3>
                    <p className="text-sm text-slate-500">
                      {targetOfficialsInfo.filteredOfficials?.length || messagePreview?.representatives?.length || 0} {
                        targetOfficialsInfo.chamber === 'house' ? 'House contact' :
                        targetOfficialsInfo.chamber === 'senate' ? 'Senate contacts' :
                        'contacts'
                      }
                      {targetOfficialsInfo.explanation && (
                        <span className="block text-xs text-slate-400 mt-1">
                          {targetOfficialsInfo.explanation}
                        </span>
                      )}
                    </p>
                  </div>
                </div>
                
                <div className="space-y-3">
                  {(targetOfficialsInfo.filteredOfficials || messagePreview?.representatives || [])?.map((rep: any, index: number) => (
                    <div key={index} className="group p-4 rounded-lg bg-gradient-to-r from-slate-50 to-blue-50 border border-slate-100 hover:shadow-md transition-all duration-200">
                      <div className="flex items-start gap-3">
                        <div className="w-10 h-10 bg-gradient-to-r from-slate-600 to-slate-700 rounded-full flex items-center justify-center text-white font-semibold text-sm">
                          {(rep.full_name || rep.name || 'R').charAt(0)}
                        </div>
                        <div className="flex-1 min-w-0">
                          <h4 className="font-medium text-slate-900 truncate">
                            {rep.full_name || rep.name || 'Representative'}
                          </h4>
                          <p className="text-sm text-slate-600 truncate">
                            {rep.title || rep.chamber} • {rep.state || 'Unknown State'}
                          </p>
                          <div className="flex items-center gap-2 mt-1">
                            <div className={`w-2 h-2 rounded-full ${
                              rep.party === 'Democratic' ? 'bg-blue-500' :
                              rep.party === 'Republican' ? 'bg-red-500' : 
                              'bg-purple-500'
                            }`}></div>
                            <span className="text-xs text-slate-500">{rep.party || 'Independent'}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Action Summary */}
            <Card className="border-0 bg-white/80 backdrop-blur-sm shadow-xl shadow-emerald-100/50">
              <CardContent className="p-6">
                <div className="flex items-center gap-3 mb-4">
                  <div className="w-10 h-10 bg-gradient-to-r from-emerald-500 to-green-600 rounded-lg flex items-center justify-center">
                    <svg className="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <h3 className="font-semibold text-slate-900">Action Summary</h3>
                </div>
                
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-slate-600">Position</span>
                    <div className={`px-3 py-1 rounded-full text-xs font-medium ${
                      watchedStance === 'support' ? 'bg-emerald-100 text-emerald-700' :
                      watchedStance === 'oppose' ? 'bg-red-100 text-red-700' :
                      'bg-amber-100 text-amber-700'
                    }`}>
                      {watchedStance?.charAt(0).toUpperCase() + watchedStance?.slice(1)}
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-slate-600">Recipients</span>
                    <span className="text-sm font-medium text-slate-900">
                      {messagePreview ? 
                        `${targetOfficialsInfo.filteredOfficials?.length || messagePreview.representatives?.length || 0} representative${((targetOfficialsInfo.filteredOfficials?.length || messagePreview.representatives?.length || 0) !== 1) ? 's' : ''}` :
                        targetOfficialsInfo.description
                      }
                    </span>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-slate-600">Your reasons</span>
                    <span className="text-sm font-medium text-slate-900">
                      {[...watchedReasons, ...watchedCustomReasons].length || 'General position'}
                    </span>
                  </div>
                  
                  {getValues('personal_stories') && (
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-slate-600">Personal story</span>
                      <span className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded-full">Included</span>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Right Column - Message Editor */}
          <div className="lg:col-span-2 space-y-6">
            
            {/* Message Editor Card */}
            <Card className="border-0 bg-white/80 backdrop-blur-sm shadow-xl shadow-slate-100/50">
              <CardContent className="p-8">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-gradient-to-r from-violet-500 to-purple-600 rounded-lg flex items-center justify-center">
                      <svg className="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-slate-900">Your Message</h3>
                      <p className="text-sm text-slate-500">Personalized for your representatives</p>
                    </div>
                  </div>
                  
                  {getChamberAwareMessageCount() > 1 && (
                    <div className="bg-gradient-to-r from-blue-100 to-purple-100 px-3 py-1 rounded-full">
                      <span className="text-xs font-medium text-blue-700">
                        {getChamberAwareMessageCount()} {getChamberAwareMessageCount() === 1 ? 'version' : 'versions'} generated
                      </span>
                    </div>
                  )}
                </div>

                {/* Multiple Messages Notice */}
                {getChamberAwareMessageCount() > 1 && (
                  <div className="mb-6 p-4 bg-gradient-to-r from-amber-50 to-orange-50 border border-amber-200 rounded-xl">
                    <div className="flex items-start gap-3">
                      <div className="w-8 h-8 bg-amber-500 rounded-lg flex items-center justify-center flex-shrink-0 mt-0.5">
                        <svg className="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </div>
                      <div>
                        <h4 className="font-medium text-amber-900 mb-1">Multiple Personalized Messages</h4>
                        <p className="text-sm text-amber-800">
                          We generated {getChamberAwareMessageCount()} personalized {getChamberAwareMessageCount() === 1 ? 'version' : 'versions'} for your {
                            targetOfficialsInfo.chamber === 'house' ? 'House Representative' :
                            targetOfficialsInfo.chamber === 'senate' ? 'Senators' :
                            'representatives'
                          }. 
                          Edit the message below to customize it for all recipients.
                        </p>
                      </div>
                    </div>
                  </div>
                )}

                {/* Message Content - Loading or Ready */}
                {isLoadingPreview || !messagePreview ? (
                  /* Modern AI Writing Loading State */
                  <div className="space-y-6">
                    <div className="relative bg-gradient-to-br from-blue-50 to-indigo-100 rounded-xl p-8 overflow-hidden">
                      <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-blue-200/30 to-transparent rounded-full -mr-16 -mt-16"></div>
                      <div className="relative">
                        <div className="flex items-center gap-3 mb-6">
                          <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center">
                            <svg className="w-5 h-5 text-white animate-pulse" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536M9 13h6m-3-3v6m5.036-9.036l-1.414 1.414M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                          </div>
                          <div>
                            <h3 className="font-semibold text-slate-900">AI is writing your personalized letter</h3>
                            <p className="text-sm text-slate-600">{aiProgressMessage || 'Preparing your message...'}</p>
                          </div>
                        </div>
                        
                        {/* Progress Bar */}
                        <div className="w-full bg-white/50 rounded-full h-2 mb-4 overflow-hidden">
                          <div 
                            className="h-2 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full transition-all duration-500 ease-out"
                            style={{ width: `${aiProgress}%` }}
                          ></div>
                        </div>
                        
                        {/* Animated Writing Preview */}
                        <div className="bg-white/70 backdrop-blur-sm rounded-lg p-6 min-h-[200px] relative">
                          <div className="flex items-start gap-3">
                            <div className="flex-1">
                              <div className="space-y-3">
                                <div className="h-4 bg-slate-200 rounded animate-pulse" style={{width: '60%'}}></div>
                                <div className="h-4 bg-slate-200 rounded animate-pulse" style={{width: '100%'}}></div>
                                <div className="h-4 bg-slate-200 rounded animate-pulse" style={{width: '90%'}}></div>
                                <div className="h-4 bg-slate-200 rounded animate-pulse" style={{width: '85%'}}></div>
                                <div className="h-4 bg-slate-200 rounded animate-pulse" style={{width: '95%'}}></div>
                                <div className="h-4 bg-slate-200 rounded animate-pulse" style={{width: '70%'}}></div>
                              </div>
                              
                              {/* Animated typing cursor */}
                              <div className="flex items-center mt-4">
                                <div className="w-0.5 h-5 bg-blue-500 animate-pulse"></div>
                                <span className="text-xs text-slate-500 ml-2">Writing personalized content...</span>
                              </div>
                            </div>
                          </div>
                        </div>
                        
                        <p className="text-sm text-slate-500 mt-4 flex items-center gap-2">
                          <svg className="w-4 h-4 animate-spin" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          Personalizing your message based on your position and reasons...
                        </p>
                      </div>
                    </div>
                  </div>
                ) : (
                  /* Message Ready - Show Textarea */
                  <>
                    <div className="space-y-4">
                      <textarea
                        {...register('custom_message')}
                        rows={12}
                        className="w-full px-4 py-4 border border-slate-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white/50 backdrop-blur-sm text-slate-900 placeholder-slate-400 resize-none transition-all duration-200"
                        placeholder="Your personalized message will appear here..."
                      />
                      
                      <p className="text-sm text-slate-500 flex items-center gap-2">
                        <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        Edit your message content below. Action Network will automatically add "Dear [Representative Name]," and your signature.
                      </p>
                    </div>

                    {/* Individual Messages Preview */}
                    {messagePreview?.personalized_messages?.length > 0 && (
                  <details className="mt-6 group">
                    <summary className="cursor-pointer p-4 bg-slate-50 hover:bg-slate-100 rounded-xl transition-colors duration-200">
                      <div className="flex items-center justify-between">
                        <span className="font-medium text-slate-700">Preview Messages to Each Representative</span>
                        <div className="flex items-center gap-2 text-slate-500">
                          <span className="text-sm">{getChamberAwareMessageCount()} {getChamberAwareMessageCount() === 1 ? 'message' : 'messages'}</span>
                          <svg className="w-4 h-4 group-open:rotate-180 transition-transform duration-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                          </svg>
                        </div>
                      </div>
                    </summary>
                    
                    <div className="mt-4 mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                      <p className="text-sm text-blue-800">
                        <span className="font-medium">Complete Message Preview:</span> Shows how your message will appear to each representative, including automatic greeting and signature added by Action Network.
                      </p>
                    </div>
                    
                    <div className="mt-4 space-y-4">
                      {messagePreview?.personalized_messages?.map((msg: any, index: number) => {
                        const correspondingOfficial = messagePreview?.representatives?.[index];
                        const isFiltered = targetOfficialsInfo.filteredOfficials?.some(filtered => 
                          filtered.id === correspondingOfficial?.id || 
                          filtered.full_name === correspondingOfficial?.full_name
                        );
                        const willBeSent = !targetOfficialsInfo.filteredOfficials || isFiltered;
                        
                        // Only show messages that will be sent
                        if (!willBeSent) return null;
                        
                        return (
                        <div key={index} className="p-4 bg-gradient-to-r from-green-50 to-blue-50 border border-green-200 rounded-lg">
                          <div className="flex items-start gap-3">
                            <div className="w-6 h-6 bg-green-600 rounded-full flex items-center justify-center text-white text-xs font-semibold">
                              ✓
                            </div>
                            <div className="flex-1">
                              <h5 className="font-medium text-slate-900 mb-3 flex items-center gap-2">
                                To: {correspondingOfficial?.full_name || correspondingOfficial?.name || `Representative ${index + 1}`}
                                <span className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded-full">
                                  Will be sent
                                </span>
                              </h5>
                              
                              {/* Complete message preview with automatic additions */}
                              <div className="bg-white border border-slate-200 rounded-lg p-4">
                                {/* Automatic greeting - non-editable */}
                                <div className="mb-3 pb-3 border-b border-slate-100">
                                  <p className="text-sm font-medium text-slate-600 bg-slate-50 px-2 py-1 rounded inline-block">
                                    Dear {correspondingOfficial?.title || 'Representative'} {correspondingOfficial?.last_name || correspondingOfficial?.name},
                                  </p>
                                  <p className="text-xs text-slate-400 mt-1">
                                    ↑ Added automatically by Action Network
                                  </p>
                                </div>
                                
                                {/* Message content */}
                                <div className="mb-3">
                                  <p className="text-sm text-slate-700 whitespace-pre-wrap leading-relaxed">
                                    {msg.body}
                                  </p>
                                </div>
                                
                                {/* Automatic signature - non-editable */}
                                <div className="pt-3 border-t border-slate-100">
                                  <p className="text-xs text-slate-400 mb-1">
                                    ↓ Added automatically by Action Network
                                  </p>
                                  <p className="text-sm font-medium text-slate-600 bg-slate-50 px-2 py-1 rounded inline-block">
                                    Sincerely,<br />
                                    [Your Name and Address]
                                  </p>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                        );
                      })}
                    </div>
                  </details>
                )}
                  </>
                )}
              </CardContent>
            </Card>

            {/* AI Enhancement Section */}
            <Card className="border-0 bg-gradient-to-br from-violet-500/5 to-purple-500/5 backdrop-blur-sm shadow-xl shadow-purple-100/50">
              <CardContent className="p-8">
                <div className="flex items-center gap-3 mb-6">
                  <div className="w-12 h-12 bg-gradient-to-r from-violet-500 to-purple-600 rounded-xl flex items-center justify-center">
                    <svg className="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold bg-gradient-to-r from-violet-700 to-purple-700 bg-clip-text text-transparent">
                      Enhance Your Message
                    </h3>
                    <p className="text-sm text-slate-600">
                      Let AI refine your message based on your specific feedback
                    </p>
                  </div>
                </div>

                <div className="space-y-6">
                  <textarea
                    value={rewriteFeedback}
                    onChange={(e) => setRewriteFeedback(e.target.value)}
                    placeholder="Tell us how to improve your message: 'Make it more formal', 'Add economic impact details', 'Mention my local community', 'Make it shorter and more direct'..."
                    rows={3}
                    className="w-full px-4 py-4 border border-violet-200 rounded-xl focus:ring-2 focus:ring-violet-500 focus:border-violet-500 bg-white/70 backdrop-blur-sm placeholder-violet-300"
                  />
                  
                  <div className="flex gap-3">
                    <Button
                      type="button"
                      onClick={handleRewriteMessage}
                      disabled={isRegeneratingMessage || !rewriteFeedback.trim()}
                      className={`flex-1 h-12 rounded-xl font-semibold transition-all duration-200 ${
                        isRegeneratingMessage || !rewriteFeedback.trim()
                          ? 'bg-slate-200 text-slate-400 cursor-not-allowed'
                          : 'bg-gradient-to-r from-violet-600 to-purple-600 hover:from-violet-700 hover:to-purple-700 text-white shadow-lg hover:shadow-xl hover:scale-105'
                      }`}
                    >
                      {isRegeneratingMessage ? (
                        <>
                          <svg className="animate-spin h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          Enhancing...
                        </>
                      ) : (
                        <>
                          <svg className="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                          </svg>
                          Rewrite Message
                        </>
                      )}
                    </Button>
                    
                    <Button
                      type="button"
                      onClick={handleGenerateNewVersion}
                      disabled={isRegeneratingMessage}
                      variant="ghost"
                      className="h-12 rounded-xl font-semibold bg-slate-100 hover:bg-slate-200 text-slate-700 px-6"
                >
                  {isRegeneratingMessage ? (
                    <>
                          <svg className="animate-spin h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Generating...
                    </>
                  ) : (
                    <>
                          <svg className="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                          </svg>
                          Generate New Version
                    </>
                  )}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Send Message Section - Only show when message is ready */}
        {messagePreview && !isLoadingPreview && (
          <div className="mt-12 flex justify-center">
            <div className="w-full max-w-2xl">
              <Card className="border-0 bg-gradient-to-r from-emerald-500 to-green-600 shadow-2xl shadow-emerald-500/25">
                <CardContent className="p-8 text-center">
                  <h3 className="text-xl font-bold text-white mb-2">Ready to Send Your Message?</h3>
                  <p className="text-emerald-100 mb-6">
                    Your personalized message will be sent to {targetOfficialsInfo.description}
                </p>
                <Button
                  type="button"
                  onClick={async () => {
                    if (!bill || !messagePreview) {
                      toast.error('Missing bill or message data');
                      return;
                    }
                    
                    setIsSubmitting(true);
                    
                    try {
                      const formData = getValues();
                      const data = {
                        bill_id: bill.id,
                        stance: formData.stance,
                        selected_reasons: formData.selected_reasons || [],
                        custom_reasons: formData.custom_reasons || [],
                        personal_stories: formData.personal_stories || '',
                        custom_message: formData.message || '',
                        first_name: formData.first_name,
                        last_name: formData.last_name,
                        zip_code: formData.zip_code,
                        email: formData.email,
                        address: formData.address,
                        city: formData.city,
                        state: formData.state,
                        target_chamber: targetOfficialsInfo.actionNetworkChamber
                      };
                      
                      const result = await billActionApi.submitBillAction(data);
                      
                      if (result.success) {
                        // Handle Action Network integration completely behind the scenes
                        if (result.action_network_embed?.iframe_url && result.delivery_summary?.requires_user_completion) {
                          console.log('🔧 DEBUG - Action Network integration required, submitting behind the scenes');
                          setIsProcessingActionNetwork(true);
                          
                          try {
                            // Submit to Action Network programmatically using a hidden iframe
                            await submitToActionNetworkHidden(result.action_network_embed, data);
                            
                            // Show success message only after Action Network submission is complete
                            const totalMessages = result.officials_contacted || result.delivery_summary?.total_targets || 0;
                            toast.success(`Successfully sent your message to ${totalMessages} representative${totalMessages > 1 ? 's' : ''}!`);
                            setCurrentStep('amplify');
                          } catch (error) {
                            console.error('Action Network submission failed:', error);
                            toast.error('Message delivery failed. Please try again.');
                          } finally {
                            setIsProcessingActionNetwork(false);
                          }
                        } else {
                          // Direct success path
                          toast.success('Your message has been sent successfully!');
                          setCurrentStep('amplify');
                        }
                      } else {
                        toast.error(result.message || 'Failed to send message');
                      }
                    } catch (error: any) {
                      console.error('Error submitting action:', error);
                      toast.error(error.message || 'Failed to send message');
                    } finally {
                      setIsSubmitting(false);
                    }
                  }}
                  disabled={isSubmitting || isProcessingActionNetwork}
                  className="h-14 px-12 bg-white hover:bg-gray-50 text-emerald-600 font-bold text-lg rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
                >
                  {isSubmitting || isProcessingActionNetwork ? (
                    <>
                      <svg className="animate-spin h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      {isProcessingActionNetwork ? 'Sending Messages...' : 'Preparing...'}
                    </>
                  ) : (
                    <>
                      <svg className="w-5 h-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                      </svg>
                      Send My Message
                    </>
                  )}
                </Button>
                
                {/* Target chamber info */}
                <div className="mt-4 text-emerald-100 text-sm">
                  {targetOfficialsInfo.explanation && (
                    <p className="italic">{targetOfficialsInfo.explanation}</p>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
        )}
      </div>
    </div>
  );
}