'use client';

import React, { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import apiClient from '../../services/apiClient';
import { AIUsageDashboard } from '../../components/admin/AIUsageDashboard';
import { ContactMessagesSection } from '../../components/admin/ContactMessagesSection';

interface ActionStats {
  total_actions: number;
  by_stance: { [key: string]: number };
  by_status: { [key: string]: number };
  recent_actions: Array<{
    id: string;
    bill_title: string;
    user_email: string;
    stance: string;
    created_at: string;
    selected_reasons: string[];
    custom_reason?: string;
  }>;
}

interface DetailedAction {
  id: string;
  bill_id: string;
  bill_title: string;
  user_id: string;
  user_email: string;
  user_name: string;
  stance: string;
  status: string;
  subject: string;
  message: string;
  created_at: string;
  sent_at?: string;
  delivered_at?: string;
  delivery_method?: string;
  error_message?: string;
  selected_reasons: Array<{
    id: string;
    reason_text: string;
  }>;
  custom_reason?: string;
  user_location: {
    zip_code?: string;
    city?: string;
    state?: string;
    address?: string;
  };
  representatives_contacted: Array<{
    name: string;
    title: string;
    party: string;
  }>;
  action_metadata?: any;
}

interface BillAnalytics {
  bill_id: string;
  bill_title: string;
  stats: {
    support: number;
    oppose: number;
    amend: number;
    total: number;
  };
  top_reasons: Array<{
    reason_text: string;
    count: number;
    stance: string;
  }>;
  custom_reasons: string[];
}

export default function AdminPageBackup() {
  const [isProcessing, setIsProcessing] = useState(false);
  const [billId, setBillId] = useState('hr9-118');
  const [results, setResults] = useState<any>(null);
  const [actionStats, setActionStats] = useState<ActionStats | null>(null);
  const [billAnalytics, setBillAnalytics] = useState<BillAnalytics[]>([]);
  const [selectedBillId, setSelectedBillId] = useState('bd9c4dfb-a7b7-406d-ac41-263f36548c50');
  const [loadingAnalytics, setLoadingAnalytics] = useState(false);
  const [detailedActions, setDetailedActions] = useState<DetailedAction[]>([]);
  const [showDetailedView, setShowDetailedView] = useState(false);
  const [loadingDetails, setLoadingDetails] = useState(false);
  const [selectedActionId, setSelectedActionId] = useState<string | null>(null);

  // Unified processing state
  const [unifiedProcessing, setUnifiedProcessing] = useState(false);
  const [unifiedResults, setUnifiedResults] = useState<any>(null);
  const [processingConfig, setProcessingConfig] = useState({
    maxBills: 5,
    environment: 'development',
    specificBills: ''
  });

  // Processing status state
  const [processingStatus, setProcessingStatus] = useState<any>(null);
  const [loadingStatus, setLoadingStatus] = useState(false);

  // Bills management state
  const [bills, setBills] = useState<any[]>([]);
  const [loadingBills, setLoadingBills] = useState(false);
  const [editingBill, setEditingBill] = useState<any>(null);
  const [editFormData, setEditFormData] = useState<any>({});
  const [workflowSummary, setWorkflowSummary] = useState<any>(null);
  const [selectedBillsForProcessing, setSelectedBillsForProcessing] = useState<string[]>([]);

  // Load analytics data on component mount
  useEffect(() => {
    loadActionAnalytics();
  }, []);

  const loadActionAnalytics = async () => {
    setLoadingAnalytics(true);
    try {
      // For now, skip action analytics since these endpoints don't exist yet
      // Set some placeholder data so the UI doesn't break
      setActionStats({
        total_actions: 0,
        by_stance: { support: 0, oppose: 0, amend: 0 },
        by_status: { sent: 0, pending: 0, failed: 0 },
        recent_actions: []
      });

      setBillAnalytics([{
        bill_id: selectedBillId,
        bill_title: 'Sample Bill Analytics', 
        stats: { support: 0, oppose: 0, amend: 0, total: 0 },
        top_reasons: [],
        custom_reasons: []
      }]);
      
    } catch (error) {
      console.error('Error loading analytics:', error);
      // Don't show error toast for analytics - it's not critical for bill processing
    } finally {
      setLoadingAnalytics(false);
    }
  };

  const loadDetailedActions = async (billId?: string) => {
    setLoadingDetails(true);
    try {
      // For now, skip detailed actions since these endpoints don't exist yet
      // Set empty data so the UI doesn't break
      setDetailedActions([]);
      
    } catch (error) {
      console.error('Error loading detailed actions:', error);
      toast.error('Failed to load detailed action data');
    } finally {
      setLoadingDetails(false);
    }
  };

  const handlePullBill = async () => {
    setIsProcessing(true);
    setResults(null);

    try {
      // Parse bill ID (e.g., "hr9-118" -> congress=118, bill_type=hr, bill_number=9)
      const match = billId.match(/^([a-z]+)(\d+)-(\d+)$/i);
      if (!match) {
        throw new Error('Invalid bill ID format. Use format like: hr9-118, s1234-118');
      }

      const [, billType, billNumber, congress] = match;

      const response = await apiClient.post(`/admin/process-and-save-bill?congress=${congress}&bill_type=${billType}&bill_number=${billNumber}`, {}, {
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.status !== 200) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = response.data;
      setResults(result);
      toast.success('Bill processed successfully!');
    } catch (error) {
      console.error('Error processing bill:', error);
      toast.error('Failed to process bill');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleTestPreview = async () => {
    setIsProcessing(true);
    
    try {
      const response = await apiClient.post('/actions/preview-message', {
        bill_id: results?.bill_id || '25177ae6-9916-456b-aec2-7d3b84f87647', // Use processed bill ID or fallback
        stance: 'support',
        selected_reasons: ['This bill improves safety standards'],
        custom_reasons: ['My custom reason for supporting this'],
        zip_code: '60302'
      }, {
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.status !== 200) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = response.data;
      setResults(result);
      toast.success('Message preview generated successfully!');
    } catch (error) {
      console.error('Error generating preview:', error);
      toast.error('Failed to generate message preview');
    } finally {
      setIsProcessing(false);
    }
  };

  // Unified processing function
  const handleUnifiedProcess = async () => {
    setUnifiedProcessing(true);
    setUnifiedResults(null);
    
    try {
      let response;
      
      if (processingConfig.specificBills && processingConfig.specificBills.trim()) {
        // Process specific bills - send as JSON array
        const billNumbers = processingConfig.specificBills.split(',').map(s => s.trim()).filter(s => s);
        response = await apiClient.post('/admin/process-bills', billNumbers, {
          headers: {
            'Content-Type': 'application/json',
          },
        });
      } else {
        // Process recent bills - use query parameters
        const params = new URLSearchParams({
          limit: processingConfig.maxBills.toString(),
          environment: processingConfig.environment
        });

        response = await apiClient.post(`/admin/process-bills?${params}`, null, {
          headers: {
            'Content-Type': 'application/json',
          },
        });
      }

      if (response.status !== 200) {
        const errorText = response.data || response.statusText;
        console.error('API Error Response:', errorText);
        throw new Error(`HTTP error! status: ${response.status} - ${errorText}`);
      }

      const data = response.data;
      setUnifiedResults(data);
      
      if (data.success) {
        const processedCount = data.processed_bills?.length || 0;
        const skippedCount = data.skipped_bills?.length || 0;
        const failedCount = data.failed_bills?.length || 0;
        
        if (processedCount > 0) {
          toast.success(`Successfully processed ${processedCount} bills with unified pipeline!`);
        } else if (skippedCount > 0) {
          toast.success(`Found ${skippedCount} bills, but all were already processed. No new bills to process.`);
        } else if (failedCount > 0) {
          toast.error(`Processing failed for ${failedCount} bills. Check results for details.`);
        } else {
          toast.success('Processing completed - no bills found to process.');
        }
      } else {
        toast.error('Unified processing completed with errors');
      }
    } catch (error) {
      console.error('Unified processing error:', error);
      setUnifiedResults({ error: error instanceof Error ? error.message : 'Unknown error' });
      toast.error('Failed to process bills with unified pipeline');
    } finally {
      setUnifiedProcessing(false);
    }
  };

  // Processing status function
  const fetchProcessingStatus = async () => {
    setLoadingStatus(true);
    
    try {
      const response = await fetch('http://localhost:8000/api/v1/admin/processing-status');
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      setProcessingStatus(data);
    } catch (error) {
      console.error('Status fetch error:', error);
      setProcessingStatus({ error: error instanceof Error ? error.message : 'Unknown error' });
    } finally {
      setLoadingStatus(false);
    }
  };

  // Trigger values analysis function
  const handleTriggerValuesAnalysis = async () => {
    try {
      const response = await apiClient.post('/admin/trigger-values-analysis', {
        environment: processingConfig.environment
      }, {
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.status !== 200) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = response.data;
      toast.success(`Values analysis triggered: ${data.message}`);
      fetchProcessingStatus(); // Refresh status
    } catch (error) {
      console.error('Values analysis trigger error:', error);
      toast.error(`Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  // Bills management functions
  const loadBills = async () => {
    setLoadingBills(true);
    try {
      const response = await apiClient.get('/bills/simple?limit=100');
      if (response.status === 200) {
        setBills(response.data);
      } else {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
    } catch (error) {
      console.error('Error loading bills:', error);
      toast.error('Failed to load bills');
    } finally {
      setLoadingBills(false);
    }
  };

  // Load bills with workflow status and importance scoring
  const loadBillsWithWorkflowStatus = async () => {
    setLoadingBills(true);
    try {
      const response = await apiClient.get('/admin/bills-processing-status?limit=100&include_scores=true');
      if (response.status === 200) {
        const data = response.data;
        setBills(data.bills);
        setWorkflowSummary(data.summary);
        toast.success(`Loaded ${data.bills.length} bills with workflow status`);
      } else {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
    } catch (error) {
      console.error('Error loading bills with workflow status:', error);
      toast.error('Failed to load bills workflow status');
    } finally {
      setLoadingBills(false);
    }
  };

  // Batch process selected bills through process-bill-details workflow
  const batchProcessBillDetails = async () => {
    if (selectedBillsForProcessing.length === 0) {
      toast.error('Please select bills to process');
      return;
    }

    setLoadingBills(true);
    try {
      toast(`Processing ${selectedBillsForProcessing.length} bills through AI workflow...`, { icon: 'ℹ️' });
      
      const response = await apiClient.post('/admin/batch-process-bill-details', selectedBillsForProcessing, {
        params: {
          environment: 'development'
        }
      });

      if (response.status === 200) {
        const data = response.data;
        if (data.success) {
          const processedCount = data.processed_bills?.length || 0;
          const skippedCount = data.skipped_bills?.length || 0;
          const failedCount = data.failed_bills?.length || 0;

          let message = `Processed ${processedCount} bills`;
          if (skippedCount > 0) message += `, skipped ${skippedCount}`;
          if (failedCount > 0) message += `, failed ${failedCount}`;
          
          toast.success(message);
          setSelectedBillsForProcessing([]);
          loadBillsWithWorkflowStatus(); // Refresh with updated status
        } else {
          toast.error(`Batch processing failed: ${data.message || 'Unknown error'}`);
        }
      }
    } catch (error) {
      console.error('Error in batch processing:', error);
      toast.error('Failed to process bills');
    } finally {
      setLoadingBills(false);
    }
  };

  // Toggle bill selection for batch processing
  const toggleBillSelection = (billId: string) => {
    setSelectedBillsForProcessing(prev => 
      prev.includes(billId) 
        ? prev.filter(id => id !== billId)
        : [...prev, billId]
    );
  };

  // Fetch all current bills from Congress.gov
  const fetchAllCurrentBills = async () => {
    setLoadingBills(true);
    try {
      toast('Fetching current bills from Congress.gov...', { icon: 'ℹ️' });
      const response = await apiClient.post('/admin/process-bills', null, {
        params: {
          limit: 50,
          environment: 'development'
        }
      });

      if (response.status === 200) {
        const data = response.data;
        if (data.success) {
          toast.success(`Fetched ${data.processed_bills?.length || 0} current bills!`);
          loadBills(); // Refresh the list
        } else {
          toast.error('Failed to fetch bills');
        }
      }
    } catch (error) {
      console.error('Error fetching current bills:', error);
      toast.error('Failed to fetch current bills');
    } finally {
      setLoadingBills(false);
    }
  };

  // Run AI analysis on all existing bills
  const runAIAnalysisOnAllBills = async () => {
    setLoadingBills(true);
    try {
      toast('Running span-grounded AI analysis on all bills...', { icon: '🤖' });
      const response = await apiClient.post('/admin/process-existing-bills-with-ai', null, {
        params: {
          limit: 20
        }
      });

      if (response.status === 200) {
        const data = response.data;
        if (data.success) {
          toast.success(`AI analysis completed on ${data.processed_bills?.length || 0} bills!`);
          loadBills(); // Refresh the list
        } else {
          toast.error(`AI analysis failed: ${data.error || 'Unknown error'}`);
        }
      }
    } catch (error) {
      console.error('Error running AI analysis:', error);
      toast.error('Failed to run AI analysis');
    } finally {
      setLoadingBills(false);
    }
  };

  // Run AI analysis on a specific bill
  const runAIAnalysisOnBill = async (billId: string, billNumber: string) => {
    try {
      toast(`Running AI analysis on ${billNumber}...`, { icon: '🤖' });
      const response = await apiClient.post('/admin/process-bill-details', {
        bill_number: billNumber,
        session: '118',
        environment: 'development',
        use_enhanced_analysis: false // Use cost-optimized span-grounded analysis
      });

      if (response.status === 200) {
        const data = response.data;
        if (data.success) {
          toast.success(`AI analysis completed for ${billNumber}!`);
          loadBills(); // Refresh the list
        } else {
          toast.error(`AI analysis failed for ${billNumber}: ${data.detail || 'Unknown error'}`);
        }
      }
    } catch (error) {
      console.error(`Error running AI analysis on ${billNumber}:`, error);
      toast.error(`Failed to run AI analysis on ${billNumber}`);
    }
  };

  const handleEditBill = (bill: any) => {
    setEditingBill(bill);
    setEditFormData({
      title: bill.title || '',
      bill_number: bill.bill_number || '',
      ai_summary: bill.ai_summary || '',
      status: bill.status || 'introduced',
      is_featured: bill.is_featured || false
    });
  };

  const handleSaveBill = async () => {
    if (!editingBill) return;

    try {
      const response = await apiClient.put(`/bills/${editingBill.id}`, editFormData);
      
      if (response.status === 200) {
        toast.success('Bill updated successfully');
        setEditingBill(null);
        setEditFormData({});
        loadBills(); // Refresh the list
      } else {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
    } catch (error) {
      console.error('Error updating bill:', error);
      toast.error('Failed to update bill');
    }
  };

  const handleDeleteBill = async (billId: string, billNumber: string) => {
    if (!confirm(`Are you sure you want to delete bill ${billNumber}? This action cannot be undone.`)) {
      return;
    }

    try {
      const response = await apiClient.delete(`/bills/${billId}`);
      
      if (response.status === 200) {
        toast.success('Bill deleted successfully');
        loadBills(); // Refresh the list
      } else {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
    } catch (error) {
      console.error('Error deleting bill:', error);
      toast.error('Failed to delete bill');
    }
  };

  const cancelEdit = () => {
    setEditingBill(null);
    setEditFormData({});
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h1 className="text-2xl font-bold text-gray-900">Admin Dashboard (BACKUP)</h1>
            <p className="mt-1 text-sm text-gray-600">
              This is a backup of the original admin page - do not modify
            </p>
          </div>
          {/* Rest of the original component content... */}
        </div>
      </div>
    </div>
  );
}