'use client';

import { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from '../../components/ui/button';
import { Input } from '../../components/ui/input';
import { Textarea } from '../../components/ui/textarea';
import { Label } from '../../components/ui/label';
import toast from 'react-hot-toast';
import { contactMessagesApi } from '../../services/apiClient';
import { ContactMessageCreate } from '../../types';

interface ContactForm {
  firstName: string;
  lastName: string;
  email: string;
  category: string;
  message: string;
}

export default function ContactPageClient() {
  const [form, setForm] = useState<ContactForm>({
    firstName: '',
    lastName: '',
    email: '',
    category: 'general',
    message: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [agreedToPrivacy, setAgreedToPrivacy] = useState(false);
  const [currentTestimonial, setCurrentTestimonial] = useState(0);

  const testimonials = [
    {
      quote: "ModernAction helped me contact my representatives about climate policy. The process was so simple!",
      author: "<PERSON>",
      role: "Environmental Advocate",
      location: "Portland, OR"
    },
    {
      quote: "I've taken action on 15+ bills through this platform. It's making democracy more accessible.",
      author: "Marcus Johnson",
      role: "Community Organizer",
      location: "Detroit, MI"
    },
    {
      quote: "Finally, a platform that makes civic engagement feel approachable and impactful.",
      author: "<PERSON>",
      role: "Teacher",
      location: "Austin, TX"
    },
    {
      quote: "The AI summaries help me understand complex legislation in minutes, not hours.",
      author: "David Park",
      role: "Small Business Owner",
      location: "Seattle, WA"
    }
  ];

  const stats = [
    { number: "50,000+", label: "Active Citizens" },
    { number: "2,500+", label: "Bills Tracked" },
    { number: "125,000+", label: "Actions Taken" },
    { number: "500+", label: "Representatives Contacted" }
  ];

  // Rotate testimonials every 4 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTestimonial((prev) => (prev + 1) % testimonials.length);
    }, 4000);
    return () => clearInterval(interval);
  }, [testimonials.length]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const contactData: ContactMessageCreate = {
        name: `${form.firstName} ${form.lastName}`.trim(),
        email: form.email,
        subject: `${form.category} inquiry from ${form.firstName}`,
        category: form.category,
        message: form.message,
        preferred_contact_method: 'email'
      };

      await contactMessagesApi.create(contactData);
      
      toast.success('Thank you for your message! We\'ll get back to you within 24 hours.');
      setForm({
        firstName: '',
        lastName: '',
        email: '',
        category: 'general',
        message: ''
      });
      setAgreedToPrivacy(false);
    } catch (error) {
      console.error('Error submitting contact form:', error);
      toast.error('Something went wrong. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleChange = (field: keyof ContactForm, value: string) => {
    setForm(prev => ({ ...prev, [field]: value }));
  };

  return (
    <div className="min-h-screen bg-white lg:grid lg:grid-cols-2">
      {/* Left side - Sleek Modern */}
      <div className="hidden lg:flex bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/50 relative overflow-hidden">
        <div className="flex flex-col justify-center h-full w-full p-16 relative">
          
          {/* Elegant background pattern */}
          <div className="absolute inset-0 opacity-[0.02]">
            <div className="absolute top-20 left-20 w-96 h-96 bg-blue-500 rounded-full blur-3xl"></div>
            <div className="absolute bottom-20 right-20 w-96 h-96 bg-indigo-500 rounded-full blur-3xl"></div>
          </div>

          {/* Main Content */}
          <div className="relative z-10 space-y-12">
            
            {/* Hero Section */}
            <div className="space-y-6">
              <div className="space-y-4">
                <h2 className="text-5xl font-light text-slate-900 leading-tight">
                  Shape the future of
                  <span className="block font-semibold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                    democracy
                  </span>
                </h2>
                <p className="text-xl text-slate-600 leading-relaxed max-w-md">
                  Join thousands of engaged citizens making their voices heard in the halls of power.
                </p>
              </div>
            </div>

            {/* Elegant Stats */}
            <div className="space-y-8">
              <div className="grid grid-cols-2 gap-8">
                {stats.map((stat, index) => (
                  <div 
                    key={index}
                    className="group"
                    style={{
                      animationDelay: `${index * 0.1}s`,
                      animation: 'fadeInUp 0.8s ease-out forwards'
                    }}
                  >
                    <div className="text-4xl font-light text-slate-900 mb-1 group-hover:text-blue-600 transition-colors duration-300">
                      {stat.number}
                    </div>
                    <div className="text-sm text-slate-600 font-medium uppercase tracking-wider">
                      {stat.label}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Testimonial - Single, Clean */}
            <div className="space-y-6">
              <div 
                key={currentTestimonial}
                className="transition-all duration-700 ease-in-out opacity-100"
              >
                <blockquote className="text-lg text-slate-700 leading-relaxed mb-6 font-light">
                  "{testimonials[currentTestimonial].quote}"
                </blockquote>
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center">
                    <span className="text-white font-semibold text-sm">
                      {testimonials[currentTestimonial].author.split(' ').map(n => n[0]).join('')}
                    </span>
                  </div>
                  <div>
                    <cite className="text-slate-900 font-medium not-italic">
                      {testimonials[currentTestimonial].author}
                    </cite>
                    <div className="text-sm text-slate-600">
                      {testimonials[currentTestimonial].role}
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Subtle indicator dots */}
              <div className="flex space-x-2">
                {testimonials.map((_, index) => (
                  <div
                    key={index}
                    className={`w-2 h-2 rounded-full transition-all duration-300 ${
                      index === currentTestimonial 
                        ? 'bg-blue-500 w-8' 
                        : 'bg-slate-300 hover:bg-slate-400'
                    }`}
                  />
                ))}
              </div>
            </div>

            {/* Clean Social Links */}
            <div className="space-y-4">
              <div className="text-sm text-slate-600 font-medium">Follow our impact</div>
              <div className="flex space-x-4">
                <a 
                  href="https://twitter.com/modernaction" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="w-10 h-10 bg-white/80 backdrop-blur-sm rounded-xl flex items-center justify-center text-slate-600 hover:text-blue-500 hover:bg-white transition-all duration-200 shadow-sm hover:shadow-md"
                >
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                  </svg>
                </a>

                <a 
                  href="https://linkedin.com/company/modernaction" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="w-10 h-10 bg-white/80 backdrop-blur-sm rounded-xl flex items-center justify-center text-slate-600 hover:text-blue-600 hover:bg-white transition-all duration-200 shadow-sm hover:shadow-md"
                >
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                  </svg>
                </a>

                <a 
                  href="https://github.com/modernaction" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="w-10 h-10 bg-white/80 backdrop-blur-sm rounded-xl flex items-center justify-center text-slate-600 hover:text-slate-800 hover:bg-white transition-all duration-200 shadow-sm hover:shadow-md"
                >
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0022 12.017C22 6.484 17.522 2 12 2z"/>
                  </svg>
                </a>
              </div>
            </div>

          </div>
        </div>
      </div>

      {/* Right side - ModernAction Themed Form */}
      <div className="bg-gradient-to-br from-blue-600 via-blue-700 to-blue-800 flex items-center justify-center p-8 lg:p-16">
        <div className="w-full max-w-lg">
          <div className="text-center mb-12">
            <h1 className="text-5xl font-light text-white mb-6 tracking-tight">
              Get in touch
            </h1>
            <p className="text-blue-100 text-lg leading-relaxed">
              We'd love to hear from you. Send us a message and we'll respond within 24 hours.
            </p>
          </div>

          <form onSubmit={handleSubmit} className="space-y-8">
            {/* First and Last Name */}
            <div className="grid grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="firstName" className="text-blue-100 text-sm font-medium">
                  First name
                </Label>
                <Input
                  id="firstName"
                  value={form.firstName}
                  onChange={(e) => handleChange('firstName', e.target.value)}
                  placeholder="John"
                  required
                  className="bg-white/90 border-0 rounded-xl px-4 py-4 text-gray-900 placeholder-gray-500 focus:bg-white focus:ring-2 focus:ring-white/50 backdrop-blur-sm transition-all"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="lastName" className="text-blue-100 text-sm font-medium">
                  Last name
                </Label>
                <Input
                  id="lastName"
                  value={form.lastName}
                  onChange={(e) => handleChange('lastName', e.target.value)}
                  placeholder="Doe"
                  required
                  className="bg-white/90 border-0 rounded-xl px-4 py-4 text-gray-900 placeholder-gray-500 focus:bg-white focus:ring-2 focus:ring-white/50 backdrop-blur-sm transition-all"
                />
              </div>
            </div>

            {/* Email */}
            <div className="space-y-2">
              <Label htmlFor="email" className="text-blue-100 text-sm font-medium">
                Email address
              </Label>
              <Input
                id="email"
                type="email"
                value={form.email}
                onChange={(e) => handleChange('email', e.target.value)}
                placeholder="<EMAIL>"
                required
                className="bg-white/90 border-0 rounded-xl px-4 py-4 text-gray-900 placeholder-gray-500 focus:bg-white focus:ring-2 focus:ring-white/50 backdrop-blur-sm transition-all w-full"
              />
            </div>

            {/* Category */}
            <div className="space-y-2">
              <Label htmlFor="category" className="text-blue-100 text-sm font-medium">
                How can we help?
              </Label>
              <select
                id="category"
                value={form.category}
                onChange={(e) => handleChange('category', e.target.value)}
                className="bg-white/90 border-0 rounded-xl px-4 py-4 text-gray-900 focus:bg-white focus:ring-2 focus:ring-white/50 backdrop-blur-sm transition-all w-full appearance-none cursor-pointer"
              >
                <option value="general" className="bg-white text-gray-900">General inquiry</option>
                <option value="support" className="bg-white text-gray-900">Technical support</option>
                <option value="partnerships" className="bg-white text-gray-900">Partnerships</option>
                <option value="press" className="bg-white text-gray-900">Press & media</option>
                <option value="feedback" className="bg-white text-gray-900">Feedback</option>
                <option value="other" className="bg-white text-gray-900">Other</option>
              </select>
            </div>

            {/* Message */}
            <div className="space-y-2">
              <Label htmlFor="message" className="text-blue-100 text-sm font-medium">
                Your message
              </Label>
              <Textarea
                id="message"
                value={form.message}
                onChange={(e) => handleChange('message', e.target.value)}
                placeholder="Tell us about your project, question, or how we can help..."
                required
                rows={5}
                className="bg-white/90 border-0 rounded-xl px-4 py-4 text-gray-900 placeholder-gray-500 focus:bg-white focus:ring-2 focus:ring-white/50 backdrop-blur-sm transition-all w-full resize-none"
              />
            </div>

            {/* Privacy Agreement */}
            <div className="flex items-start space-x-3">
              <input
                type="checkbox"
                id="privacy"
                checked={agreedToPrivacy}
                onChange={(e) => setAgreedToPrivacy(e.target.checked)}
                required
                className="mt-1 w-4 h-4 rounded border-0 bg-white/90 text-blue-600 focus:ring-white/50 focus:ring-offset-0"
              />
              <Label htmlFor="privacy" className="text-blue-100 text-sm leading-relaxed">
                I agree to ModernAction's{' '}
                <a href="/privacy" className="text-white hover:text-blue-100 transition-colors underline underline-offset-2">
                  privacy policy
                </a>
                {' '}and terms of service.
              </Label>
            </div>

            {/* Submit Button */}
            <Button 
              type="submit" 
              disabled={isSubmitting || !agreedToPrivacy}
              className="w-full bg-white hover:bg-gray-100 text-blue-700 font-semibold py-4 px-6 rounded-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed text-lg hover:shadow-xl transform hover:scale-[1.02]"
            >
              {isSubmitting ? (
                <span className="flex items-center justify-center space-x-2">
                  <svg className="animate-spin w-5 h-5" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  <span>Sending...</span>
                </span>
              ) : (
                'Send message'
              )}
            </Button>
          </form>

          {/* Trust Indicator */}
          <div className="mt-12 text-center">
            <div className="inline-flex items-center space-x-2 text-blue-200 text-sm">
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
              </svg>
              <span>Your information is secure and protected</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}