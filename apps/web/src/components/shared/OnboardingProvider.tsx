'use client';

import React, { useEffect, useState } from 'react';
import { useOnboardingStore } from '../../stores/onboardingStore';
import OnboardingModal from './OnboardingModal';

interface OnboardingProviderProps {
  children: React.ReactNode;
}

const OnboardingProvider: React.FC<OnboardingProviderProps> = ({ children }) => {
  // const { hasCompletedOnboarding } = useOnboardingStore(); // Unused after disabling auto-popup
  const [showOnboardingModal, setShowOnboardingModal] = useState(false);
  const [isClient, setIsClient] = useState(false);

  // Ensure we're on the client side to avoid hydration issues
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Check if onboarding should be shown
  // DISABLED: Auto-popup removed per user request
  // Users can access onboarding through settings instead
  // useEffect(() => {
  //   if (isClient && !hasCompletedOnboarding) {
  //     // Small delay to ensure the page has loaded
  //     const timer = setTimeout(() => {
  //       setShowOnboardingModal(true);
  //     }, 500);

  //     return () => clearTimeout(timer);
  //   }
  // }, [isClient, hasCompletedOnboarding]);

  const handleCloseOnboarding = () => {
    setShowOnboardingModal(false);
  };

  return (
    <>
      {children}
      {isClient && (
        <OnboardingModal
          isOpen={showOnboardingModal}
          onClose={handleCloseOnboarding}
        />
      )}
    </>
  );
};

export default OnboardingProvider;
