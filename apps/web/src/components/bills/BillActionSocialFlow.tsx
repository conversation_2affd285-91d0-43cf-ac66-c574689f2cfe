'use client';

import React, { useState, useEffect } from 'react';
import { Bill, Official } from '@/types';
import { SocialMediaContactWidget } from '../officials/SocialMediaContactWidget';
import { trackSocialEngagement } from '@/services/analytics';

interface BillActionSocialFlowProps {
  bill: Bill;
  stance: 'support' | 'oppose' | 'amend';
  userLocation?: {
    zip_code?: string;
    city?: string;
    state?: string;
  };
  representatives?: Official[];
  customReasons?: string[];
  personalStory?: string;
  onEngagementTracked?: (data: any) => void;
  onComplete?: () => void;
  className?: string;
}

interface EngagementStep {
  id: string;
  title: string;
  description: string;
  icon: string;
  completed: boolean;
  optional: boolean;
}

export const BillActionSocialFlow: React.FC<BillActionSocialFlowProps> = ({
  bill,
  stance,
  userLocation,
  representatives = [],
  customReasons = [],
  personalStory = '',
  onEngagementTracked,
  onComplete,
  className = ''
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [completedEngagements, setCompletedEngagements] = useState<string[]>([]);
  const [showAdvancedOptions, setShowAdvancedOptions] = useState(false);
  
  // Auto-start on first step for immediate engagement
  useEffect(() => {
    // Pre-load social media widget data when component mounts
    if (representatives.length > 0) {
      console.log('📱 Social media flow ready with', representatives.length, 'representatives');
    }
  }, [representatives]);

  const stanceText = stance === 'support' ? 'support' : 
                   stance === 'oppose' ? 'oppose' : 
                   'want changes to';

  const stanceColor = stance === 'support' ? 'green' : 
                     stance === 'oppose' ? 'red' : 
                     'yellow';

  // Create comprehensive message based on user input
  const createPersonalizedMessage = (): string => {
    let message = '';
    
    if (personalStory) {
      message += personalStory + '\n\n';
    }
    
    if (customReasons.length > 0) {
      message += 'Here are my key concerns:\n';
      customReasons.forEach((reason, index) => {
        message += `${index + 1}. ${reason}\n`;
      });
      message += '\n';
    }
    
    message += `As your constituent${userLocation?.zip_code ? ` from ${userLocation.zip_code}` : ''}${userLocation?.city ? ` in ${userLocation.city}` : ''}, I hope you will consider my position when this bill comes to a vote.`;
    
    return message;
  };

  // Engagement flow steps
  const engagementSteps: EngagementStep[] = [
    {
      id: 'immediate',
      title: 'Quick Social Media Outreach',
      description: 'Send immediate messages to your representatives',
      icon: '🚀',
      completed: false,
      optional: false
    },
    {
      id: 'amplify',
      title: 'Amplify Your Voice',
      description: 'Share your position publicly to encourage others',
      icon: '📢',
      completed: false,
      optional: true
    },
    {
      id: 'follow-up',
      title: 'Schedule Follow-up',
      description: 'Set reminders for continued engagement',
      icon: '📅',
      completed: false,
      optional: true
    }
  ];

  const handleEngagementComplete = (type: string, platform: string, official?: Official) => {
    const engagementId = `${type}-${platform}${official ? `-${official.id}` : ''}`;
    
    if (!completedEngagements.includes(engagementId)) {
      setCompletedEngagements(prev => [...prev, engagementId]);
      
      // Track engagement for analytics
      const engagementData = {
        type,
        platform,
        bill: bill.bill_number,
        stance,
        official: official?.name,
        timestamp: new Date().toISOString(),
        userLocation: userLocation?.zip_code
      };
      
      if (onEngagementTracked) {
        onEngagementTracked(engagementData);
      }
      
      // Auto-advance steps
      if (type === 'direct-message') {
        markStepCompleted('immediate');
      } else if (type === 'public-share') {
        markStepCompleted('amplify');
      }
    }
  };

  const markStepCompleted = (stepId: string) => {
    // Implementation would mark step as completed
    console.log(`Step completed: ${stepId}`);
  };

  // Generate social media hashtags
  const generateHashtags = (): string[] => {
    const hashtags = [
      '#' + bill.bill_number.replace(/\s+/g, ''),
      stance === 'support' ? '#SupportThis' : stance === 'oppose' ? '#OposeThis' : '#AmendThis',
    ];
    
    if (bill.title.toLowerCase().includes('climate')) hashtags.push('#ClimateAction');
    if (bill.title.toLowerCase().includes('health')) hashtags.push('#Healthcare');
    if (bill.title.toLowerCase().includes('education')) hashtags.push('#Education');
    
    return hashtags.slice(0, 3); // Keep it manageable
  };

  // Create public sharing message
  const createPublicMessage = (): string => {
    const hashtags = generateHashtags();
    
    let message = `I just contacted my representatives about ${bill.bill_number}: ${bill.title.length > 80 ? bill.title.substring(0, 77) + '...' : bill.title}

I ${stanceText} this legislation because it affects our community directly.`;

    if (userLocation?.city && userLocation?.state) {
      message += ` Speaking up for ${userLocation.city}, ${userLocation.state}!`;
    }
    
    message += `\n\n${hashtags.join(' ')} #YourVoteMatters #CivicEngagement`;
    
    return message;
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Progress Header */}
      <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-bold text-gray-900">
            🎯 Amplify Your Voice
          </h2>
          <div className={`px-3 py-1 rounded-full text-sm font-medium ${
            stanceColor === 'green' ? 'bg-green-100 text-green-800' :
            stanceColor === 'red' ? 'bg-red-100 text-red-800' :
            'bg-yellow-100 text-yellow-800'
          }`}>
            You {stanceText} {bill.bill_number}
          </div>
        </div>
        
        <div className="bg-blue-100 border border-blue-200 rounded-lg p-4 mb-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-blue-800">
                📢 Your message has been successfully delivered!
              </p>
              <p className="text-sm text-blue-700 mt-1">
                Now let's amplify your voice through social media to maximize impact and inspire others to take action.
              </p>
            </div>
          </div>
        </div>
        
        {/* Engagement Progress */}
        <div className="grid grid-cols-3 gap-4">
          {engagementSteps.map((step, index) => (
            <div key={step.id} className={`text-center p-3 rounded-lg transition-colors ${
              currentStep >= index ? 'bg-white shadow-sm' : 'bg-gray-50'
            }`}>
              <div className={`text-2xl mb-1 ${step.completed ? 'grayscale' : ''}`}>
                {step.completed ? '✅' : step.icon}
              </div>
              <h3 className="text-sm font-semibold text-gray-900">{step.title}</h3>
              <p className="text-xs text-gray-600 mt-1">{step.description}</p>
              {step.optional && (
                <span className="inline-block mt-1 px-2 py-0.5 text-xs bg-gray-200 text-gray-600 rounded">
                  Optional
                </span>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Step 1: Direct Contact with Representatives */}
      {currentStep === 0 && (
        <div className="bg-white border border-gray-200 rounded-lg shadow-sm">
          <div className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
              <span className="mr-2">🎯</span>
              Contact Your Representatives Directly
            </h3>
            <p className="text-gray-600 mb-6">
              Send personalized messages to your elected officials about your position on {bill.bill_number}.
            </p>

            <SocialMediaContactWidget
              officials={representatives}
              bill={bill}
              stance={stance}
              userZip={userLocation?.zip_code}
              customMessage={createPersonalizedMessage()}
              context="bill-action"
              variant="full"
            />

            {/* Impact Message */}
            <div className="mt-6 p-4 bg-blue-50 rounded-lg">
              <h4 className="font-semibold text-blue-900 mb-2 flex items-center">
                <span className="mr-2">💡</span>
                Maximize Your Impact
              </h4>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• Be specific about how this bill affects your community</li>
                <li>• Share your personal story to make it memorable</li>
                <li>• Use @mentions to increase visibility</li>
                <li>• Follow up with phone calls or official contact forms</li>
              </ul>
            </div>

            <div className="flex justify-between mt-6">
              <button
                onClick={() => setCurrentStep(1)}
                className="text-sm text-gray-600 hover:text-gray-800"
              >
                Skip to public sharing →
              </button>
              <button
                onClick={() => setCurrentStep(1)}
                className="px-6 py-2 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors"
              >
                Next: Amplify Your Message
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Step 2: Public Amplification */}
      {currentStep === 1 && (
        <div className="bg-white border border-gray-200 rounded-lg shadow-sm">
          <div className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
              <span className="mr-2">📢</span>
              Share Your Position Publicly
            </h3>
            <p className="text-gray-600 mb-6">
              Inspire others in your community to take action by sharing your position publicly.
            </p>

            {/* Public Message Preview */}
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 mb-6">
              <h4 className="font-medium text-gray-900 mb-2">Preview your public message:</h4>
              <div className="text-sm text-gray-700 whitespace-pre-line">
                {createPublicMessage()}
              </div>
            </div>

            {/* Platform Selection */}
            <div className="grid grid-cols-2 sm:grid-cols-4 gap-3 mb-6">
              {[
                { platform: 'twitter', name: 'Twitter/X', icon: '🐦', color: 'bg-blue-500' },
                { platform: 'facebook', name: 'Facebook', icon: '📘', color: 'bg-blue-600' },
                { platform: 'instagram', name: 'Instagram', icon: '📷', color: 'bg-pink-500' },
                { platform: 'linkedin', name: 'LinkedIn', icon: '💼', color: 'bg-blue-700' }
              ].map(({ platform, name, icon, color }) => (
                <button
                  key={platform}
                  onClick={() => {
                    const message = createPublicMessage();
                    const url = encodeURIComponent(window.location.origin + `/bills/${bill.id}`);
                    
                    let shareUrl = '';
                    if (platform === 'twitter') {
                      shareUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(message)}&url=${url}`;
                    } else if (platform === 'facebook') {
                      shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${url}&quote=${encodeURIComponent(message)}`;
                    } else if (platform === 'linkedin') {
                      shareUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${url}&title=${encodeURIComponent(bill.title)}`;
                    }
                    
                    if (shareUrl) {
                      window.open(shareUrl, '_blank', 'width=600,height=400');
                      handleEngagementComplete('public-share', platform);
                      
                      // Track public sharing analytics
                      trackSocialEngagement({
                        actionType: 'public-share',
                        platform: platform as any,
                        official: 'public',
                        bill: bill.bill_number,
                        stance,
                        userLocation: userLocation?.zip_code || 'unknown',
                        messageLength: message.length,
                        customization: false,
                        context: 'bill-action'
                      });
                    }
                  }}
                  className={`flex items-center justify-center space-x-2 p-3 text-white rounded-lg font-medium hover:opacity-90 transition-opacity ${color}`}
                >
                  <span>{icon}</span>
                  <span className="text-sm">{name}</span>
                </button>
              ))}
            </div>

            {/* Alternative Actions */}
            <div className="border-t border-gray-200 pt-4">
              <h4 className="font-medium text-gray-900 mb-3">Other ways to amplify:</h4>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                <button className="flex items-center space-x-3 p-3 text-left bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                  <span className="text-xl">📧</span>
                  <div>
                    <div className="font-medium text-gray-900">Email friends & family</div>
                    <div className="text-sm text-gray-600">Share with your personal network</div>
                  </div>
                </button>
                <button className="flex items-center space-x-3 p-3 text-left bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                  <span className="text-xl">💬</span>
                  <div>
                    <div className="font-medium text-gray-900">Text message</div>
                    <div className="text-sm text-gray-600">Quick share via SMS</div>
                  </div>
                </button>
              </div>
            </div>

            <div className="flex justify-between mt-6">
              <button
                onClick={() => setCurrentStep(0)}
                className="text-sm text-gray-600 hover:text-gray-800"
              >
                ← Back to direct contact
              </button>
              <div className="space-x-3">
                <button
                  onClick={() => setCurrentStep(2)}
                  className="px-4 py-2 text-sm text-gray-600 hover:text-gray-800"
                >
                  Skip
                </button>
                <button
                  onClick={() => setCurrentStep(2)}
                  className="px-6 py-2 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors"
                >
                  Next: Follow Up
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Step 3: Follow-up Planning */}
      {currentStep === 2 && (
        <div className="bg-white border border-gray-200 rounded-lg shadow-sm">
          <div className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
              <span className="mr-2">📅</span>
              Stay Engaged
            </h3>
            <p className="text-gray-600 mb-6">
              Sustained engagement is key to making an impact. Here's how to stay involved:
            </p>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Follow-up Actions */}
              <div>
                <h4 className="font-medium text-gray-900 mb-3">Follow-up Actions</h4>
                <div className="space-y-3">
                  <label className="flex items-center space-x-3">
                    <input type="checkbox" className="rounded border-gray-300 text-blue-600" />
                    <span className="text-sm">Remind me in 1 week to check bill status</span>
                  </label>
                  <label className="flex items-center space-x-3">
                    <input type="checkbox" className="rounded border-gray-300 text-blue-600" />
                    <span className="text-sm">Email me when the bill moves to committee</span>
                  </label>
                  <label className="flex items-center space-x-3">
                    <input type="checkbox" className="rounded border-gray-300 text-blue-600" />
                    <span className="text-sm">Notify me before the floor vote</span>
                  </label>
                  <label className="flex items-center space-x-3">
                    <input type="checkbox" className="rounded border-gray-300 text-blue-600" />
                    <span className="text-sm">Alert me to related bills</span>
                  </label>
                </div>
              </div>

              {/* Additional Engagement */}
              <div>
                <h4 className="font-medium text-gray-900 mb-3">Keep the Momentum Going</h4>
                <div className="space-y-3 text-sm">
                  <div className="flex items-start space-x-2">
                    <span>🗳️</span>
                    <span>Join local advocacy groups working on this issue</span>
                  </div>
                  <div className="flex items-start space-x-2">
                    <span>📞</span>
                    <span>Schedule calls with your representatives' offices</span>
                  </div>
                  <div className="flex items-start space-x-2">
                    <span>📰</span>
                    <span>Write a letter to your local newspaper</span>
                  </div>
                  <div className="flex items-start space-x-2">
                    <span>👥</span>
                    <span>Organize friends and neighbors to take action</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Completion */}
            <div className="mt-6 p-4 bg-green-50 rounded-lg border border-green-200">
              <h4 className="font-semibold text-green-900 mb-2 flex items-center">
                <span className="mr-2">🎉</span>
                Great Job Taking Action!
              </h4>
              <p className="text-sm text-green-800">
                You've made your voice heard on {bill.bill_number}. Every action counts in our democracy. 
                Your representatives are more likely to listen when constituents like you speak up.
              </p>
            </div>

            <div className="flex justify-between mt-6">
              <button
                onClick={() => setCurrentStep(1)}
                className="text-sm text-gray-600 hover:text-gray-800"
              >
                ← Back to amplification
              </button>
              <button
                onClick={() => {
                  // Complete the flow - call onComplete callback
                  console.log('Engagement flow completed');
                  if (onComplete) {
                    onComplete();
                  }
                }}
                className="px-6 py-2 bg-green-600 text-white rounded-lg font-medium hover:bg-green-700 transition-colors"
              >
                Complete ✅
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Engagement Summary */}
      {completedEngagements.length > 0 && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <h4 className="font-semibold text-green-900 mb-2 flex items-center">
            <span className="mr-2">📊</span>
            Your Engagement Impact
          </h4>
          <p className="text-sm text-green-800">
            You've completed {completedEngagements.length} engagement action{completedEngagements.length > 1 ? 's' : ''} on {bill.bill_number}. 
            Thank you for making your voice heard!
          </p>
          
          {/* Share Impact */}
          <button
            onClick={() => {
              const impactMessage = `I just took ${completedEngagements.length} action${completedEngagements.length > 1 ? 's' : ''} on ${bill.bill_number}: ${bill.title}! Every voice matters in democracy. 🗳️ #YourVoteMatters #CivicEngagement`;
              const shareUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(impactMessage)}`;
              window.open(shareUrl, '_blank');
            }}
            className="mt-3 inline-flex items-center space-x-2 text-sm text-green-700 hover:text-green-900"
          >
            <span>🐦</span>
            <span>Share your impact</span>
          </button>
        </div>
      )}
    </div>
  );
};

export default BillActionSocialFlow;