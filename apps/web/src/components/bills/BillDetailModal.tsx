import React from 'react';
import { Bill } from '../../types';
import { StatusBadge } from './StatusBadge';
import { GroupPill } from './GroupPill';
import { ValuesAnalysisTags } from './ValuesAnalysisTags';

interface BillDetailModalProps {
  isOpen: boolean;
  onClose: () => void;
  bill: Bill;
  onTakeAction: (bill: Bill) => void;
}

const CloseIcon = () => (
  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
  </svg>
);

const MegaphoneIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z" />
  </svg>
);

export const BillDetailModal: React.FC<BillDetailModalProps> = ({ isOpen, onClose, bill, onTakeAction }) => {
  if (!isOpen) return null;

  const urgency = bill.status === 'floor' ? 'high' : 'medium';

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        {/* Background overlay */}
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={onClose}></div>

        {/* Modal panel - Larger and more spacious */}
        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-6xl sm:w-full max-h-[90vh] flex flex-col">
          {/* Header - Fixed height */}
          <div className="bg-gradient-to-r from-blue-600 to-purple-700 px-8 py-6 flex-shrink-0">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <span className="bg-white bg-opacity-20 text-white px-4 py-2 rounded-full text-sm font-semibold">
                  {bill.bill_number}
                </span>
                <StatusBadge status={bill.status} urgency={urgency} />
                <div className="text-white text-sm">
                  <span className="opacity-80">Session:</span> {bill.session_year} | <span className="opacity-80">Chamber:</span> {bill.chamber}
                </div>
              </div>
              <button
                onClick={onClose}
                className="text-white hover:text-gray-200 transition-colors p-1"
              >
                <CloseIcon />
              </button>
            </div>
            <h2 className="text-3xl font-bold text-white mt-4 leading-tight">{bill.title}</h2>
            {bill.sponsor_name && (
              <p className="text-blue-100 mt-2">Sponsored by {bill.sponsor_name}</p>
            )}
          </div>

          {/* Content - Scrollable with generous spacing */}
          <div className="px-8 py-8 overflow-y-auto flex-1">
            <div className="max-w-4xl mx-auto space-y-8">

              {/* Fallback for bills without structured summaries */}
              {!bill.summary_what_does && !bill.summary_who_affects && !bill.summary_why_matters && bill.ai_summary && (
                <div className="bg-blue-50 rounded-xl p-6 border-l-4 border-blue-500">
                  <h3 className="text-2xl font-bold text-blue-700 mb-4 flex items-center gap-2">
                    📋 Bill Summary
                  </h3>
                  <div className="text-gray-800 leading-relaxed text-lg whitespace-pre-line">
                    {bill.ai_summary}
                  </div>
                </div>
              )}

              {/* What This Bill Does */}
              {bill.summary_what_does && (
                <div className="bg-blue-50 rounded-xl p-6 border-l-4 border-blue-500">
                  <h3 className="text-2xl font-bold text-blue-700 mb-4 flex items-center gap-2">
                    📋 {bill.summary_what_does.title}
                  </h3>
                  <p className="text-gray-800 mb-4 leading-relaxed text-lg">
                    {bill.summary_what_does.content}
                  </p>
                  {bill.summary_what_does.key_points && bill.summary_what_does.key_points.length > 0 && (
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-3 text-lg">🎯 Key Points:</h4>
                      <ul className="space-y-2">
                        {bill.summary_what_does.key_points.map((point, index) => (
                          <li key={index} className="flex items-start gap-3">
                            <span className="bg-blue-200 text-blue-800 rounded-full w-6 h-6 flex items-center justify-center text-sm font-semibold flex-shrink-0 mt-0.5">
                              {index + 1}
                            </span>
                            <span className="text-gray-700 leading-relaxed">{point}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              )}

              {/* Who This Affects */}
              {bill.summary_who_affects && (
                <div className="bg-green-50 rounded-xl p-6 border-l-4 border-green-500">
                  <h3 className="text-2xl font-bold text-green-700 mb-4 flex items-center gap-2">
                    👥 {bill.summary_who_affects.title}
                  </h3>
                  <p className="text-gray-800 mb-4 leading-relaxed text-lg">
                    {bill.summary_who_affects.content}
                  </p>
                  {bill.summary_who_affects.affected_groups && bill.summary_who_affects.affected_groups.length > 0 && (
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-3 text-lg">🎯 Affected Groups:</h4>
                      <div className="flex flex-wrap gap-3">
                        {bill.summary_who_affects.affected_groups.map((group, index) => (
                          <span key={index} className="bg-green-200 text-green-800 px-4 py-2 rounded-full text-sm font-medium">
                            {group}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* Why It Matters */}
              {bill.summary_why_matters && (
                <div className="bg-purple-50 rounded-xl p-6 border-l-4 border-purple-500">
                  <h3 className="text-2xl font-bold text-purple-700 mb-4 flex items-center gap-2">
                    💡 {bill.summary_why_matters.title}
                  </h3>
                  <p className="text-gray-800 mb-6 leading-relaxed text-lg">
                    {bill.summary_why_matters.content}
                  </p>

                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {bill.summary_why_matters.benefits && bill.summary_why_matters.benefits.length > 0 && (
                      <div className="bg-white rounded-lg p-5 border border-green-200">
                        <h4 className="font-bold text-green-800 mb-4 text-lg flex items-center gap-2">
                          ✅ Potential Benefits
                        </h4>
                        <ul className="space-y-3">
                          {bill.summary_why_matters.benefits.map((benefit, index) => (
                            <li key={index} className="flex items-start gap-3">
                              <span className="bg-green-100 text-green-700 rounded-full w-6 h-6 flex items-center justify-center text-sm font-semibold flex-shrink-0 mt-0.5">
                                +
                              </span>
                              <span className="text-gray-700 leading-relaxed">{benefit}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}

                    {bill.summary_why_matters.concerns && bill.summary_why_matters.concerns.length > 0 && (
                      <div className="bg-white rounded-lg p-5 border border-red-200">
                        <h4 className="font-bold text-red-800 mb-4 text-lg flex items-center gap-2">
                          ⚠️ Potential Concerns
                        </h4>
                        <ul className="space-y-3">
                          {bill.summary_why_matters.concerns.map((concern, index) => (
                            <li key={index} className="flex items-start gap-3">
                              <span className="bg-red-100 text-red-700 rounded-full w-6 h-6 flex items-center justify-center text-sm font-semibold flex-shrink-0 mt-0.5">
                                !
                              </span>
                              <span className="text-gray-700 leading-relaxed">{concern}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Key Provisions */}
              {bill.summary_key_provisions && (
                <div className="bg-indigo-50 rounded-xl p-6 border-l-4 border-indigo-500">
                  <h3 className="text-2xl font-bold text-indigo-700 mb-4 flex items-center gap-2">
                    📜 {bill.summary_key_provisions.title}
                  </h3>
                  <p className="text-gray-800 mb-4 leading-relaxed text-lg">
                    {bill.summary_key_provisions.content}
                  </p>
                  {bill.summary_key_provisions.provisions && bill.summary_key_provisions.provisions.length > 0 && (
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-4 text-lg">🔍 Key Provisions:</h4>
                      <div className="grid gap-3">
                        {bill.summary_key_provisions.provisions.map((provision, index) => (
                          <div key={index} className="bg-white rounded-lg p-4 border border-indigo-200">
                            <div className="flex items-start gap-3">
                              <span className="bg-indigo-200 text-indigo-800 rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold flex-shrink-0">
                                {index + 1}
                              </span>
                              <span className="text-gray-700 leading-relaxed">{provision}</span>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* Additional Sections that were missing */}
              {/* Key Provisions */}
              {(bill as any).overview?.provisions?.content && (
                <div className="bg-amber-50 rounded-xl p-6 border-l-4 border-amber-500">
                  <h3 className="text-2xl font-bold text-amber-700 mb-4 flex items-center gap-2">
                    ⚙️ Key Provisions
                  </h3>
                  <div className="text-gray-800 leading-relaxed text-lg whitespace-pre-line">
                    {(bill as any).overview.provisions.content}
                  </div>
                </div>
              )}

              {/* Implementation Mechanisms */}
              {(bill as any).overview?.mechanisms?.content && (
                <div className="bg-teal-50 rounded-xl p-6 border-l-4 border-teal-500">
                  <h3 className="text-2xl font-bold text-teal-700 mb-4 flex items-center gap-2">
                    🔧 Implementation Mechanisms
                  </h3>
                  <div className="text-gray-800 leading-relaxed text-lg whitespace-pre-line">
                    {(bill as any).overview.mechanisms.content}
                  </div>
                </div>
              )}

              {/* Budget Impact */}
              {(bill as any).overview?.budget_impact?.content && (
                <div className="bg-rose-50 rounded-xl p-6 border-l-4 border-rose-500">
                  <h3 className="text-2xl font-bold text-rose-700 mb-4 flex items-center gap-2">
                    💵 Budget Impact
                  </h3>
                  <div className="text-gray-800 leading-relaxed text-lg whitespace-pre-line">
                    {(bill as any).overview.budget_impact.content}
                  </div>
                </div>
              )}

              {/* Enforcement Details */}
              {(bill as any).overview?.enforcement_details?.content && (
                <div className="bg-slate-50 rounded-xl p-6 border-l-4 border-slate-500">
                  <h3 className="text-2xl font-bold text-slate-700 mb-4 flex items-center gap-2">
                    🛡️ Enforcement Details
                  </h3>
                  <div className="text-gray-800 leading-relaxed text-lg whitespace-pre-line">
                    {(bill as any).overview.enforcement_details.content}
                  </div>
                </div>
              )}

              {/* Other Provisions */}
              {(bill as any).overview?.other_provisions?.content && (
                <div className="bg-cyan-50 rounded-xl p-6 border-l-4 border-cyan-500">
                  <h3 className="text-2xl font-bold text-cyan-700 mb-4 flex items-center gap-2">
                    📋 Other Provisions
                  </h3>
                  <div className="text-gray-800 leading-relaxed text-lg whitespace-pre-line">
                    {(bill as any).overview.other_provisions.content}
                  </div>
                </div>
              )}

              {/* Timeline & Cost Impact */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {bill.summary_timeline && (
                  <div className="bg-orange-50 rounded-xl p-6 border-l-4 border-orange-500">
                    <h3 className="text-xl font-bold text-orange-700 mb-4 flex items-center gap-2">
                      ⏰ {bill.summary_timeline.title}
                    </h3>
                    <p className="text-gray-800 mb-4 leading-relaxed">
                      {bill.summary_timeline.content}
                    </p>
                    {bill.summary_timeline.milestones && bill.summary_timeline.milestones.length > 0 && (
                      <div>
                        <h4 className="font-semibold text-orange-800 mb-3">📅 Key Milestones:</h4>
                        <div className="space-y-2">
                          {bill.summary_timeline.milestones.map((milestone, index) => (
                            <div key={index} className="bg-white rounded-lg p-3 border border-orange-200">
                              <div className="flex items-start gap-3">
                                <span className="bg-orange-200 text-orange-800 rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold flex-shrink-0 mt-0.5">
                                  {index + 1}
                                </span>
                                <span className="text-gray-700 text-sm leading-relaxed">{milestone}</span>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                )}

                {bill.summary_cost_impact && (
                  <div className="bg-red-50 rounded-xl p-6 border-l-4 border-red-500">
                    <h3 className="text-xl font-bold text-red-700 mb-4 flex items-center gap-2">
                      💰 {bill.summary_cost_impact.title}
                    </h3>
                    <p className="text-gray-800 mb-4 leading-relaxed">
                      {bill.summary_cost_impact.content}
                    </p>
                    {bill.summary_cost_impact.estimates && bill.summary_cost_impact.estimates.length > 0 && (
                      <div>
                        <h4 className="font-semibold text-red-800 mb-3">💵 Cost Estimates:</h4>
                        <div className="space-y-2">
                          {bill.summary_cost_impact.estimates.map((estimate, index) => (
                            <div key={index} className="bg-white rounded-lg p-3 border border-red-200">
                              <div className="flex items-start gap-3">
                                <span className="bg-red-200 text-red-800 rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold flex-shrink-0 mt-0.5">
                                  $
                                </span>
                                <span className="text-gray-700 text-sm leading-relaxed">{estimate}</span>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>

              {/* Bill Metadata */}
              <div className="bg-gray-50 rounded-xl p-6 border border-gray-200">
                <h3 className="text-xl font-bold text-gray-800 mb-4 flex items-center gap-2">
                  📋 Bill Information
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {bill.created_at && (
                    <div className="bg-white rounded-lg p-4 border border-gray-200">
                      <div className="text-sm text-gray-500 mb-1">Date Introduced</div>
                      <div className="font-semibold text-gray-800">
                        {new Date(bill.created_at).toLocaleDateString('en-US', {
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric'
                        })}
                      </div>
                    </div>
                  )}
                  <div className="bg-white rounded-lg p-4 border border-gray-200">
                    <div className="text-sm text-gray-500 mb-1">Bill Number</div>
                    <div className="font-semibold text-gray-800">{bill.bill_number}</div>
                  </div>
                  <div className="bg-white rounded-lg p-4 border border-gray-200">
                    <div className="text-sm text-gray-500 mb-1">Chamber</div>
                    <div className="font-semibold text-gray-800">{bill.chamber}</div>
                  </div>
                  <div className="bg-white rounded-lg p-4 border border-gray-200">
                    <div className="text-sm text-gray-500 mb-1">Status</div>
                    <div className="font-semibold text-gray-800 capitalize">{bill.status}</div>
                  </div>
                  <div className="bg-white rounded-lg p-4 border border-gray-200">
                    <div className="text-sm text-gray-500 mb-1">Session Year</div>
                    <div className="font-semibold text-gray-800">{bill.session_year}</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Values Analysis Section */}
            {bill.values_tags && bill.values_tags.length > 0 && (
              <div className="bg-purple-50 rounded-xl p-6 border border-purple-200">
                <h3 className="text-xl font-bold text-purple-900 mb-4 flex items-center gap-2">
                  🎯 Impact Assessment
                </h3>
                <p className="text-sm text-purple-700 mb-4">
                  This bill has been analyzed for its potential impact on democratic processes,
                  civil rights, and environmental considerations.
                </p>
                <ValuesAnalysisTags
                  tags={bill.values_tags}
                  variant="full"
                />
                <p className="text-xs text-purple-600 italic mt-4">
                  Analysis uses neutral, factual criteria. All assessments are reviewed by our policy team.
                </p>
              </div>
            )}

          </div>

          {/* Footer - Enhanced */}
          <div className="bg-gradient-to-r from-blue-600 to-purple-700 px-8 py-6 flex-shrink-0">
            <div className="flex items-center justify-between">
              <div className="text-white">
                <p className="text-sm opacity-90">Ready to make your voice heard?</p>
                <p className="text-xs opacity-75">Contact your representatives about {bill.bill_number}</p>
              </div>
              <div className="flex items-center gap-4">
                <button
                  onClick={onClose}
                  className="bg-white bg-opacity-20 hover:bg-opacity-30 text-white font-medium py-3 px-6 rounded-lg transition-colors"
                >
                  Close
                </button>
                <button
                  onClick={() => {
                    onTakeAction(bill);
                    onClose();
                  }}
                  className="bg-white text-blue-600 hover:bg-gray-100 font-bold py-3 px-8 rounded-lg transition-colors flex items-center gap-2 shadow-lg"
                >
                  <MegaphoneIcon />
                  Take Action Now
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
