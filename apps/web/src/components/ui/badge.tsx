import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "./utils"

const badgeVariants = cva(
  "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      variant: {
        default:
          "border-transparent bg-primary text-primary-foreground hover:bg-primary/80",
        secondary:
          "border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",
        destructive:
          "border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",
        outline: "text-foreground",
        // Custom variants for our civic platform - using RGB values directly
        civic: "border-transparent bg-[#3B82F6] text-white",
        status: "border-transparent bg-[#DBEAFE] text-[#1E40AF]",
        committee: "border-transparent bg-[#FEF3C7] text-[#92400E]", 
        floor: "border-transparent bg-[#FEE2E2] text-[#991B1B]",
        passed: "border-transparent bg-[#D1FAE5] text-[#065F46]",
        support: "border-transparent bg-[#D1FAE5] text-[#065F46]",
        oppose: "border-transparent bg-[#FEE2E2] text-[#991B1B]",
        amend: "border-transparent bg-[#EDE9FE] text-[#5B21B6]",
        urgent: "border-transparent bg-[#FED7AA] text-[#9A3412] animate-pulse"
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
)

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {}

function Badge({ className, variant, ...props }: BadgeProps) {
  return (
    <div className={cn(badgeVariants({ variant }), className)} {...props} />
  )
}

export { Badge, badgeVariants }