# Chamber-Specific Official Targeting System

## 🎯 OBJECTIVE
Build an intelligent, scalable official targeting system that ensures users only contact relevant officials based on bill status and jurisdiction, with future support for state/local officials.

## 📋 CURRENT PROBLEM
- Showing ALL officials (House + Senate) regardless of bill stage
- Inconsistent targeting between Action Network, Social Media, and UI
- No preparation for future state/local official integration

## 🏗️ TECHNICAL ARCHITECTURE

### Core Interfaces (Future-Proof)
```typescript
interface BillTargeting {
  targetJurisdiction: 'federal' | 'state' | 'local';
  targetChamber: 'house' | 'senate' | 'both' | 'assembly' | 'council' | 'governor';
  targetOfficials: Official[];
  actionNetworkCampaign?: string;
  reasonExplanation: string;
  targeting: {
    primary: Official[];      // Direct targets (can vote on this bill)  
    secondary?: Official[];   // Indirect influence (leadership, committees)
    social: Official[];       // For social media amplification
  };
}

interface Official {
  id: string;
  name: string; 
  title: string;
  chamber: 'house' | 'senate';
  jurisdiction: 'federal' | 'state' | 'local';
  state?: string;
  district?: string;
  level: 'federal' | 'state' | 'county' | 'city';
  // ... existing fields preserved
}
```

### Targeting Logic Matrix
```
FEDERAL BILLS:
├── House Bill (introduced/committee/floor) → House Representatives ONLY
├── House Bill (passed) → Senate ONLY  
├── Senate Bill (introduced/committee/floor) → Senators ONLY
├── Senate Bill (passed) → House ONLY
└── Signed/Vetoed → Both (accountability)

FUTURE STATE BILLS:
├── State House Bill → State Assembly/House ONLY
├── State Senate Bill → State Senate ONLY  
└── Local Bills → City Council/County Board

FUTURE TARGETING HIERARCHY:
├── Primary: Officials who can vote on bill
├── Secondary: Leadership/committee chairs with influence
└── Social: All officials for public pressure campaigns
```

## 🚀 IMPLEMENTATION PHASES

### ✅ PHASE 1: Backend Smart Filtering (IN PROGRESS)

#### 1.1 Enhance Core Targeting Function
**File**: `apps/web/src/app/bills/[slug]/action/page.tsx`
- **Action**: Upgrade `getTargetOfficialsInfo()` with intelligent filtering
- **Scope**: Add official filtering logic, not just metadata
- **Compatibility**: Maintain existing return structure, add new fields

#### 1.2 Backend API Integration
**Files**: 
- `apps/api/app/api/v1/endpoints/actions.py`
- `apps/api/app/services/action_network_service.py`
- **Action**: Add official filtering to message preview API
- **Scope**: Filter officials before message generation
- **Compatibility**: Enhance existing `/preview-message` endpoint

#### 1.3 Action Network Alignment
**File**: `apps/api/app/services/action_network_service.py`
- **Action**: Ensure Action Network campaigns match filtered officials
- **Scope**: Campaign selection based on filtered official list
- **Testing**: Verify Action Network targets = UI targets

### 📋 PHASE 2: Frontend Integration (PLANNED)
- Update Recipients Display with filtered officials
- Add clear explanations for targeting logic
- Maintain Social Media integration consistency

### 📋 PHASE 3: State/Local Preparation (PLANNED)  
- Add jurisdiction-aware official models
- Create state/local campaign structures
- Build targeting logic for non-federal bills

## 🔧 TECHNICAL DECISIONS

### A. Backwards Compatibility Strategy
- **Existing Endpoints**: Enhance rather than replace
- **Return Types**: Add new fields, preserve existing structure  
- **Function Signatures**: Optional parameters for new features

### B. Single Source of Truth
- **Central Function**: Enhanced `getTargetOfficialsInfo()` 
- **Consistent Usage**: Same function for UI, Action Network, Social Media
- **Data Flow**: Bill Status → Targeting Logic → Filtered Officials → All Systems

### C. Scalability Design
- **Jurisdiction Levels**: Federal → State → Local hierarchy
- **Official Types**: Flexible chamber/level system
- **Campaign Management**: Extensible Action Network integration

## 📊 SUCCESS METRICS
- ✅ House bills show ONLY House Representatives  
- ✅ Senate bills show ONLY Senators
- ✅ Passed bills show receiving chamber officials
- ✅ Action Network targets match UI targets
- ✅ Social Media uses consistent official list
- ✅ Zero breaking changes to existing functionality

## 🧪 TESTING STRATEGY
```
Test Cases:
├── House Bill (committee) → House Reps only
├── House Bill (passed) → Senators only  
├── Senate Bill (committee) → Senators only
├── Senate Bill (passed) → House Reps only
├── Signed Bill → Both chambers
└── Action Network Consistency → Targets match UI
```

## 📝 CURRENT STATUS
- **Phase 1.1**: Enhanced getTargetOfficialsInfo() function with intelligent filtering ✅
- **Frontend Integration**: Updated all UI components to use filtered officials ✅
- **Testing**: System compiles and functions correctly ✅
- **Documentation**: Architecture planning complete ✅
- **Technical Debt**: Zero-debt approach maintained ✅

### ✅ PHASE 1.1 IMPLEMENTATION COMPLETE
**Enhanced Core Targeting Function** (`apps/web/src/app/bills/[slug]/action/page.tsx:105-201`)
- **Intelligent Filtering Logic**: Added chamber-specific filtering for all bill status conditions
  - House bills (introduced/committee/floor) → Filter to House Representatives only
  - Senate bills (introduced/committee/floor) → Filter to Senators only  
  - Passed House bills → Filter to Senators (receiving chamber)
  - Passed Senate bills → Filter to House Representatives (receiving chamber)
  - Signed/vetoed bills → Include all officials (accountability targeting)
- **Enhanced Return Structure**: Added `filteredOfficials` field to all targeting responses
- **Backwards Compatibility**: Preserved existing return structure, only enhanced with new fields

**Frontend Integration Updates**:
- **Enhanced Function Call**: Updated `getTargetOfficialsInfo(bill, messagePreview?.representatives)` to pass officials data
- **UI Component Updates**: Replaced all direct `messagePreview?.representatives` usage with `targetOfficialsInfo.filteredOfficials`
- **User Experience**: Added explanatory text showing why specific officials are being targeted
- **Consistent Display**: Updated 8+ UI components to use filtered officials consistently

**Chamber-Specific Filtering Verification**:
- ✅ House bills now show only House Representatives (no more Senators)
- ✅ Senate bills will show only Senators  
- ✅ Passed bills route to appropriate receiving chamber
- ✅ System maintains all existing functionality while adding smart filtering
- ✅ No breaking changes to existing codebase

**Chamber-Aware Message Templates** (Fixed Issue from Screenshot):
- **Dynamic Greeting**: House bills use "Dear Representative," instead of "Dear Representatives," 
- **Accurate Counts**: Message count reflects filtered officials (1 for House bills, not 3)
- **Contextual Language**: All UI text matches chamber-specific targeting
  - "We generated 1 personalized version for your House Representative"
  - "This message will be sent to your House Representative"
- **Conditional Display**: Multiple message sections only show when relevant
- **Individual Messages**: Only display messages for filtered officials

---
*Last Updated: 2025-08-18*
*Tech Lead: Claude Code*