# ModernAction Bill Discovery & Analysis Implementation Plan

## Overview

Transform ModernAction from manual bill curation to intelligent automated discovery with on-demand AI analysis. This plan implements cost-effective bill ingestion that scales to handle thousands of bills while providing users with smart filtering and analysis capabilities.

## Updated Architecture Approach

### Core Strategy: Discovery First, Analysis On-Demand
1. **Fast Discovery**: Automatically ingest House floor bills with basic metadata
2. **Smart Filtering**: Provide urgency scores and activity metrics without AI
3. **User-Driven Analysis**: Full AI processing only when users request it
4. **Progressive Enhancement**: Build engagement before expensive processing

## Database Schema Updates

```sql
-- Phase 1 Migration: Enhanced bill filtering columns
ALTER TABLE bills ADD COLUMN urgency_score INTEGER DEFAULT 0;
ALTER TABLE bills ADD COLUMN filter_metadata JSONB DEFAULT '{}'::jsonb;
ALTER TABLE bills ADD COLUMN activity_score INTEGER DEFAULT 0;
ALTER TABLE bills ADD COLUMN analysis_requested BOOLEAN DEFAULT FALSE;
ALTER TABLE bills ADD COLUMN analysis_requested_at TIMESTAMP;

-- Index for performance
CREATE INDEX idx_bills_urgency_score ON bills(urgency_score DESC);
CREATE INDEX idx_bills_activity_score ON bills(activity_score DESC);
CREATE INDEX idx_bills_analysis_requested ON bills(analysis_requested);
```

### Filter Metadata Structure
```json
{
  "urgency_factors": {
    "days_until_vote": 7,
    "committee_activity": "high", 
    "cosponsors_growth": 15,
    "media_mentions": 12
  },
  "activity_trends": {
    "momentum_score": 8.5,
    "recent_actions": 5,
    "committee_meetings": 3,
    "amendment_activity": 2
  },
  "basic_categories": ["healthcare", "budget"],
  "last_calculated": "2024-01-15T10:30:00Z",
  "discovery_source": "house_floor_tracker"
}
```

## Implementation Timeline

### Phase 1: Smart Discovery System (Week 1)
**Priority: Core automated ingestion without AI costs**

#### 1.1 Database Migration
- [ ] Add new columns: `urgency_score`, `filter_metadata`, `activity_score`, `analysis_requested`
- [ ] Create performance indexes
- [ ] Update Bill model in `apps/api/app/models/bill.py`

#### 1.2 Bill Discovery Lambda Enhancement
```python
# apps/lambda/bill_discovery/handler.py
class EnhancedBillDiscoveryService:
    async def discover_house_floor_bills(self):
        """Fast discovery with basic scoring"""
        # Query Congress.gov for floor-status bills
        # Calculate urgency_score (0-10) based on:
        #   - Days until potential vote
        #   - Committee activity level  
        #   - Cosponsors count
        #   - Recent action frequency
        
    async def calculate_basic_metadata(self, bill_data):
        """Non-AI metadata calculation"""
        urgency_score = self._calculate_urgency_score(bill_data)
        activity_score = self._calculate_activity_score(bill_data)
        filter_metadata = self._build_filter_metadata(bill_data)
        
        return {
            "urgency_score": urgency_score,
            "activity_score": activity_score, 
            "filter_metadata": filter_metadata,
            "analysis_requested": False
        }
```

**Schedule**: Daily at 6 AM EST via EventBridge

#### 1.3 Enhanced Status Updates
- [ ] Modify existing `bill_status_update` Lambda
- [ ] Update urgency/activity scores during status changes
- [ ] Maintain filter_metadata freshness

#### 1.4 Request Analysis API Endpoint
```python
@router.post("/bills/{bill_id}/request-analysis")
async def request_bill_analysis(bill_id: str, background_tasks: BackgroundTasks):
    """User-triggered AI analysis"""
    bill = get_bill(bill_id)
    
    if bill.analysis_requested:
        return {"status": "already_requested", "estimated_completion": "2-3 minutes"}
    
    # Mark as requested
    bill.analysis_requested = True
    bill.analysis_requested_at = datetime.utcnow()
    db.commit()
    
    # Queue existing admin/process-bill-details flow
    background_tasks.add_task(process_bill_details_full, bill_id)
    
    return {"status": "analysis_queued", "estimated_completion": "2-3 minutes"}
```

### Phase 2: Enhanced Frontend & Filtering (Week 2)
**Priority: User experience improvements**

#### 2.1 Bill Card Enhancement
```typescript
// Two-state bill card system
interface BillCardProps {
  bill: Bill;
  onRequestAnalysis: (billId: string) => void;
  onTakeAction: (bill: Bill) => void;
}

// Basic card (no analysis)
const BasicBillCard = ({ bill, onRequestAnalysis }) => (
  <div className="bill-card">
    <div className="bill-header">
      <h3>{bill.title}</h3>
      <div className="bill-meta">
        <span>{bill.bill_number}</span>
        <BillStatusBadge status={bill.status} />
        {bill.urgency_score > 7 && (
          <span className="urgent-badge">🔥 Urgent</span>
        )}
      </div>
    </div>
    
    {/* Key Feature: Request Analysis Button */}
    <button 
      onClick={() => onRequestAnalysis(bill.id)}
      className="request-analysis-btn"
    >
      📊 Request AI Analysis
    </button>
  </div>
);

// Enhanced card (post-analysis) 
const EnhancedBillCard = ({ bill, onTakeAction }) => (
  <div className="bill-card enhanced">
    {/* Full AI summary, positions, take action */}
    <BillSummary summary={bill.ai_summary} />
    <BillPositions 
      support={bill.support_reasons}
      oppose={bill.oppose_reasons}
    />
    <button onClick={() => onTakeAction(bill)}>
      Take Action
    </button>
  </div>
);
```

#### 2.2 Advanced Filter Bar
```typescript
interface EnhancedFilterProps {
  // Existing
  statusFilter: BillStatus | 'all';
  urgencyFilter: 'all' | 'critical' | 'high' | 'medium' | 'low';
  
  // New smart filters
  activityFilter: 'trending' | 'active' | 'stale' | 'all';
  timeframeFilter: 'this_week' | 'this_month' | 'this_session' | 'all';
  analysisFilter: 'analyzed' | 'pending' | 'not_requested' | 'all';
  
  // Enhanced metadata
  onActivityFilterChange: (activity: string) => void;
  onAnalysisFilterChange: (analysis: string) => void;
}
```

#### 2.3 Smart Filter API Support
```python
@router.get("/bills/search")
def search_bills_enhanced(
    # Existing parameters...
    urgency_min: Optional[int] = Query(None, ge=0, le=10),
    activity_min: Optional[int] = Query(None, ge=0, le=10), 
    analysis_status: Optional[str] = Query(None, regex="^(analyzed|pending|not_requested|all)$"),
    trending_only: Optional[bool] = Query(False),
    db: Session = Depends(get_db)
):
    """Enhanced search with smart filtering"""
    query = db.query(Bill)
    
    if urgency_min:
        query = query.filter(Bill.urgency_score >= urgency_min)
    
    if activity_min:
        query = query.filter(Bill.activity_score >= activity_min)
        
    if analysis_status == "analyzed":
        query = query.filter(Bill.ai_processed_at.isnot(None))
    elif analysis_status == "pending":
        query = query.filter(
            Bill.analysis_requested == True,
            Bill.ai_processed_at.is_(None)
        )
    elif analysis_status == "not_requested":
        query = query.filter(Bill.analysis_requested == False)
    
    if trending_only:
        query = query.filter(Bill.activity_score >= 7)
    
    return query.all()
```

### Phase 3: Intelligence Layer Integration (Week 3)
**Priority: Smart scoring within existing AI flow**

#### 3.1 Enhanced admin/process-bill-details Flow
```python
# apps/api/app/api/v1/endpoints/bills.py - Enhanced existing endpoint
async def process_bill_details_full(bill_id: str):
    """Full AI analysis + smart scoring (existing + new)"""
    
    # Existing AI processing...
    ai_analysis = await ai_service.analyze_bill_comprehensive(bill_text)
    bill_details = create_bill_details_record(ai_analysis)
    
    # NEW: Add smart scoring to existing flow
    smart_scores = calculate_comprehensive_scores(bill_data, ai_analysis)
    trend_analysis = detect_momentum_trends(bill_data, historical_data)
    
    # Update bill with enhanced metadata
    bill.urgency_score = smart_scores.urgency_score
    bill.activity_score = smart_scores.activity_score
    bill.filter_metadata = {
        **bill.filter_metadata,  # Preserve discovery metadata
        "ai_categories": ai_analysis.categories,
        "complexity_score": smart_scores.complexity,
        "impact_score": smart_scores.impact,
        "trend_indicators": trend_analysis,
        "last_analyzed": datetime.utcnow()
    }
    
    # Mark analysis complete
    bill.ai_processed_at = datetime.utcnow()
    db.commit()
```

#### 3.2 Smart Scoring Algorithms
```python
class SmartScoringService:
    def calculate_urgency_score(self, bill_data, ai_analysis=None) -> int:
        """0-10 urgency score"""
        score = 0
        
        # Time pressure (0-4 points)
        if bill_data.status == "floor":
            score += 4
        elif bill_data.status == "committee":
            score += 2
            
        # Activity surge (0-3 points)  
        recent_actions = len(bill_data.recent_actions or [])
        if recent_actions >= 5:
            score += 3
        elif recent_actions >= 3:
            score += 2
        elif recent_actions >= 1:
            score += 1
            
        # AI-enhanced factors (0-3 points) - only if analyzed
        if ai_analysis:
            if "urgent" in ai_analysis.tags:
                score += 2
            if ai_analysis.impact_score > 8:
                score += 1
                
        return min(score, 10)
        
    def detect_momentum_trends(self, bill_data, historical_data) -> dict:
        """Identify bills gaining traction"""
        return {
            "cosponsors_growth": calculate_cosponsor_velocity(historical_data),
            "media_attention": calculate_media_mentions_trend(bill_data),
            "committee_activity": analyze_committee_momentum(historical_data),
            "momentum_score": calculate_overall_momentum(bill_data)
        }
```

## User Experience Flow

### Discovery → Interest → Analysis → Action

1. **User browses bills page**: Sees bills with urgency indicators, no AI analysis yet
2. **Smart filtering**: Users can filter by urgency, activity, status without AI costs
3. **User requests analysis**: Clicks "Request AI Analysis" on interesting bills
4. **Background processing**: Existing `admin/process-bill-details` runs full AI pipeline
5. **Enhanced experience**: User gets full AI summary, positions, action templates

## Technical Benefits

### For Users:
- **Faster Discovery**: Find relevant bills instantly with smart scoring
- **Cost-Effective Engagement**: AI analysis only for bills they care about  
- **Progressive Enhancement**: Basic → Smart → AI-Enhanced experience
- **Urgency Awareness**: Immediate visibility into time-sensitive bills

### For System:
- **Scalable**: Handle thousands of bills without exploding AI costs
- **Efficient**: Smart metadata calculated once, cached in database
- **User-Driven**: AI investment follows user interest patterns
- **Performance**: Pre-computed scores enable fast filtering

### For Civic Engagement:
- **Timely Discovery**: Catch important bills before critical votes
- **Focused Attention**: Smart scoring helps prioritize limited citizen time  
- **Lower Barriers**: Progressive enhancement reduces cognitive load
- **Democratic Scale**: Handle full legislative volume, not just curated subset

## Success Metrics

### Week 1: Discovery Foundation
- [ ] Bills discovered automatically: 50+ House floor bills
- [ ] Basic urgency scoring: All bills have 0-10 urgency scores
- [ ] Performance: Discovery completes in <10 minutes daily

### Week 2: User Experience  
- [ ] Filter performance: <200ms for all filter combinations
- [ ] User engagement: 25%+ of users try "Request Analysis" 
- [ ] Smart filtering adoption: 40%+ use urgency/activity filters

### Week 3: Intelligence Integration
- [ ] Analysis request fulfillment: <3 minutes average
- [ ] Smart scoring accuracy: User feedback on urgency alignment
- [ ] Cost efficiency: <10% of discovered bills get full AI analysis

## Implementation Checklist

### Phase 1 Tasks
- [ ] Create database migration for new columns
- [ ] Update Bill model with new fields
- [ ] Create or enhance bill discovery Lambda
- [ ] Implement basic urgency/activity scoring algorithms
- [ ] Add request analysis API endpoint
- [ ] Test discovery automation with House floor bills

### Phase 2 Tasks  
- [ ] Create two-state bill card components (basic vs enhanced)
- [ ] Update FilterBar component with new filter options
- [ ] Enhance bills search API with smart filtering
- [ ] Add loading states for analysis requests
- [ ] Implement real-time updates for analysis completion

### Phase 3 Tasks
- [ ] Integrate smart scoring into existing admin/process-bill-details
- [ ] Create comprehensive scoring service
- [ ] Implement trend detection algorithms
- [ ] Add momentum tracking to filter metadata
- [ ] Test full user flow: discovery → request → analysis → action

This approach transforms ModernAction into an intelligent civic engagement platform that scales to handle the full complexity of federal legislation while maintaining cost efficiency and user-centric design.