# 🔄 REVISED CITATION STRATEGY

## User Clarification: Keep Citations in Complete Analysis

**User Request**: *"make sure we KEEP or even IMPROVE the citations with the complete analysis (just the complete analysis)"*

## Revised Approach

### ✅ What to Keep/Improve:
- **Complete Analysis sections**: Keep detailed citations with proper evidence spans
- **Rich detailed sections**: Maintain full citation functionality where it works
- **Evidence integration**: Improve citation quality in complete_analysis

### ❌ What to Remove/Simplify:
- **Main extraction sections**: Remove broken citations from what_does, why_matters, etc.
- **Hero citations**: Remove broken citation arrays from hero summary
- **Fragmented quotes**: Eliminate meaningless citation fragments

## Implementation Strategy

### Phase 1: Fix Content Truncation (COMPLETED ✅)
- Smart sentence-aware truncation implemented
- All extraction methods using proper boundaries
- No more mid-sentence cutoffs

### Phase 2: Selective Citation Removal (ADJUST)
- Remove citations from main sections only
- **KEEP citations in complete_analysis** 
- Improve citation quality where they're kept

### Phase 3: Improve Complete Analysis Citations
- Fix citation data structure mismatch
- Ensure evidence IDs match stored data
- Improve quote selection for meaningful excerpts

## New Target Structure

```json
{
  // Main sections - clean strings (no broken citations)
  "what_does": "Clean content with proper sentence boundaries...",
  "who_affects": "Clean content...",
  "why_matters": "Clean content...",
  
  // Complete analysis - KEEP and IMPROVE citations
  "complete_analysis": [
    {
      "title": "Section Analysis",
      "detailed_summary": "Rich content...",
      "why_it_matters": "Impact analysis...",
      "citations": [
        {
          "quote": "Meaningful quote from actual bill text",
          "heading": "SEC. 2. FINDINGS",
          "anchor_id": "sec-2",
          "start_offset": 1234,
          "end_offset": 1456
        }
      ],
      "ev_ids": ["span_1", "span_2"]
    }
  ]
}
```

## Action Plan

1. ✅ Keep content truncation fixes
2. 🔄 Restore citation functionality in complete_analysis 
3. ❌ Remove citations from main extraction sections only
4. 🔧 Fix evidence ID mapping for complete_analysis citations
5. 📈 Improve quote selection to avoid fragments

This gives users clean, readable main sections while preserving detailed citation functionality in the complete analysis where it belongs.