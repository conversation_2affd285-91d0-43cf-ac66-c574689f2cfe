#!/bin/bash

# Google Cloud CLI Helper Script for OAuth and API Management
# Usage: ./google-cloud-cli-helper.sh [command]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Ensure gcloud is in PATH
export PATH="/Users/<USER>/google-cloud-sdk/bin:$PATH"

print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}  Google Cloud CLI Helper${NC}"
    echo -e "${BLUE}================================${NC}"
}

show_current_config() {
    echo -e "${GREEN}Current Configuration:${NC}"
    gcloud config list
    echo ""
}

list_enabled_apis() {
    echo -e "${GREEN}Enabled APIs:${NC}"
    gcloud services list --enabled
    echo ""
}

enable_common_apis() {
    echo -e "${YELLOW}Enabling common APIs for OAuth and authentication...${NC}"
    
    apis=(
        "people.googleapis.com"
        "plus.googleapis.com"
        "gmail.googleapis.com"
        "calendar-json.googleapis.com"
        "drive.googleapis.com"
    )
    
    for api in "${apis[@]}"; do
        echo "Enabling $api..."
        gcloud services enable "$api" || echo "Failed to enable $api (might already be enabled or not available)"
    done
    echo ""
}

show_oauth_setup_instructions() {
    echo -e "${GREEN}OAuth 2.0 Setup Instructions:${NC}"
    echo ""
    echo "1. Go to Google Cloud Console:"
    echo "   https://console.cloud.google.com/apis/credentials?project=modernaction"
    echo ""
    echo "2. Click 'Create Credentials' > 'OAuth 2.0 Client IDs'"
    echo ""
    echo "3. Configure your OAuth consent screen first if prompted"
    echo ""
    echo "4. Choose application type:"
    echo "   - Web application (for web apps)"
    echo "   - Desktop application (for CLI tools)"
    echo "   - Mobile application (for mobile apps)"
    echo ""
    echo "5. Add authorized redirect URIs (for web applications):"
    echo "   - http://localhost:3000/api/auth/callback/google (for Next.js)"
    echo "   - https://yourdomain.com/api/auth/callback/google (for production)"
    echo ""
    echo "6. Download the client configuration JSON file"
    echo ""
    echo -e "${YELLOW}Note: Keep your client secret secure and never commit it to version control!${NC}"
    echo ""
}

show_service_account_instructions() {
    echo -e "${GREEN}Service Account Setup Instructions:${NC}"
    echo ""
    echo "1. Create a service account:"
    echo "   gcloud iam service-accounts create my-service-account --display-name='My Service Account'"
    echo ""
    echo "2. Grant necessary roles:"
    echo "   gcloud projects add-iam-policy-binding modernaction --member='serviceAccount:<EMAIL>' --role='roles/viewer'"
    echo ""
    echo "3. Create and download key:"
    echo "   gcloud iam service-accounts keys create key.json --iam-account=<EMAIL>"
    echo ""
}

open_console() {
    echo -e "${GREEN}Opening Google Cloud Console...${NC}"
    open "https://console.cloud.google.com/apis/credentials?project=modernaction"
}

show_help() {
    echo -e "${GREEN}Available commands:${NC}"
    echo "  config          - Show current gcloud configuration"
    echo "  apis            - List enabled APIs"
    echo "  enable-apis     - Enable common APIs for OAuth"
    echo "  oauth-setup     - Show OAuth 2.0 setup instructions"
    echo "  service-account - Show service account setup instructions"
    echo "  console         - Open Google Cloud Console"
    echo "  help            - Show this help message"
    echo ""
}

# Main script logic
case "${1:-help}" in
    "config")
        print_header
        show_current_config
        ;;
    "apis")
        print_header
        list_enabled_apis
        ;;
    "enable-apis")
        print_header
        enable_common_apis
        ;;
    "oauth-setup")
        print_header
        show_oauth_setup_instructions
        ;;
    "service-account")
        print_header
        show_service_account_instructions
        ;;
    "console")
        print_header
        open_console
        ;;
    "help"|*)
        print_header
        show_help
        ;;
esac
