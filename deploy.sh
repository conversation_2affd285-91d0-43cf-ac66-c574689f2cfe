#!/bin/bash

# ModernAction Deployment Script
# Usage: ./deploy.sh [staging|prod] [options]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
ENVIRONMENT=""
BUILD_IMAGES=true
DEPLOY_INFRASTRUCTURE=true
UPDATE_SERVICES=true
WAIT_FOR_STABILITY=true
HEALTH_CHECK=true

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    cat << EOF
ModernAction Deployment Script

Usage: $0 [staging|prod] [options]

Environments:
  staging    Deploy to staging environment (staging.modernaction.io)
  prod       Deploy to production environment (modernaction.io)

Options:
  --skip-build          Skip Docker image building
  --skip-infrastructure Skip CDK infrastructure deployment
  --skip-services       Skip ECS service updates
  --skip-wait          Skip waiting for service stability
  --skip-health        Skip health checks
  --help               Show this help message

Examples:
  $0 staging                    # Full staging deployment
  $0 prod --skip-build          # Production deploy without rebuilding images
  $0 staging --skip-infrastructure  # Update services only

Environment Variables:
  AWS_REGION           AWS region (default: us-east-1)
  AWS_ACCOUNT_ID       AWS account ID (default: ************)
  ECR_REGISTRY         ECR registry URL
EOF
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        staging|prod)
            ENVIRONMENT="$1"
            shift
            ;;
        --skip-build)
            BUILD_IMAGES=false
            shift
            ;;
        --skip-infrastructure)
            DEPLOY_INFRASTRUCTURE=false
            shift
            ;;
        --skip-services)
            UPDATE_SERVICES=false
            shift
            ;;
        --skip-wait)
            WAIT_FOR_STABILITY=false
            shift
            ;;
        --skip-health)
            HEALTH_CHECK=false
            shift
            ;;
        --help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Validate environment
if [[ -z "$ENVIRONMENT" ]]; then
    print_error "Environment must be specified (staging or prod)"
    show_usage
    exit 1
fi

# Set environment variables
export AWS_REGION=${AWS_REGION:-us-east-1}
export AWS_ACCOUNT_ID=${AWS_ACCOUNT_ID:-************}
export ECR_REGISTRY=${ECR_REGISTRY:-$AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com}

# Set environment-specific variables
if [[ "$ENVIRONMENT" == "staging" ]]; then
    AUTH0_DOMAIN="dev-vvwd64m28nwqm871.us.auth0.com"
    CLIENT_ID="Jtv4r9uS9BSeahx5jp93v0Ur1K7b5ihC"
    API_URL="https://staging.modernaction.io/api/v1"
    DOMAIN="staging.modernaction.io"
    ECS_CLUSTER="modernaction-staging"
elif [[ "$ENVIRONMENT" == "prod" ]]; then
    AUTH0_DOMAIN="dev-cp0hhr8rujq87exh.us.auth0.com"
    CLIENT_ID="JZYS6kWu4jKPHmajvukycBDgbhCrPNIx"
    API_URL="https://modernaction.io/api/v1"
    DOMAIN="modernaction.io"
    ECS_CLUSTER="modernaction-prod"
    
    # Production safety check
    print_warning "Deploying to PRODUCTION environment!"
    read -p "Are you sure you want to continue? (yes/no): " confirm
    if [[ $confirm != "yes" ]]; then
        print_error "Production deployment cancelled"
        exit 1
    fi
fi

print_status "Starting deployment to $ENVIRONMENT environment"
print_status "Domain: $DOMAIN"
print_status "Auth0 Tenant: $AUTH0_DOMAIN"

# Function to check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check required tools
    command -v aws >/dev/null 2>&1 || { print_error "AWS CLI is required but not installed."; exit 1; }
    command -v docker >/dev/null 2>&1 || { print_error "Docker is required but not installed."; exit 1; }
    command -v cdk >/dev/null 2>&1 || { print_error "AWS CDK is required but not installed."; exit 1; }
    
    # Check AWS credentials
    aws sts get-caller-identity >/dev/null 2>&1 || { print_error "AWS credentials not configured."; exit 1; }
    
    # Check ECR login
    aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin $ECR_REGISTRY >/dev/null 2>&1 || {
        print_error "Failed to login to ECR"
        exit 1
    }
    
    print_success "Prerequisites check passed"
}

# Function to build and push Docker images
build_images() {
    if [[ "$BUILD_IMAGES" == "false" ]]; then
        print_warning "Skipping image building"
        return
    fi
    
    print_status "Building and pushing Docker images..."
    
    # Build API image
    print_status "Building API image..."
    cd apps/api
    docker build -t modernaction-api-$ENVIRONMENT:latest . --platform linux/amd64
    docker tag modernaction-api-$ENVIRONMENT:latest $ECR_REGISTRY/modernaction-api-$ENVIRONMENT:latest
    docker push $ECR_REGISTRY/modernaction-api-$ENVIRONMENT:latest
    cd ../..
    
    # Build Web image
    print_status "Building Web image..."
    cd apps/web
    docker build -t modernaction-web-$ENVIRONMENT:latest \
        --platform linux/amd64 \
        --build-arg NEXT_PUBLIC_API_URL=$API_URL \
        --build-arg NEXT_PUBLIC_AUTH0_DOMAIN=$AUTH0_DOMAIN \
        --build-arg NEXT_PUBLIC_AUTH0_CLIENT_ID=$CLIENT_ID .
    docker tag modernaction-web-$ENVIRONMENT:latest $ECR_REGISTRY/modernaction-web-$ENVIRONMENT:latest
    docker push $ECR_REGISTRY/modernaction-web-$ENVIRONMENT:latest
    cd ../..
    
    print_success "Images built and pushed successfully"
}

# Function to deploy infrastructure
deploy_infrastructure() {
    if [[ "$DEPLOY_INFRASTRUCTURE" == "false" ]]; then
        print_warning "Skipping infrastructure deployment"
        return
    fi
    
    print_status "Deploying infrastructure with CDK..."
    cd infrastructure
    
    # Install dependencies if needed
    if [[ ! -d ".venv" ]]; then
        print_status "Installing CDK dependencies..."
        pip install -r requirements.txt
    fi
    
    # Deploy with CDK
    if [[ "$ENVIRONMENT" == "prod" ]]; then
        ENVIRONMENT=$ENVIRONMENT cdk deploy --require-approval broadening
    else
        ENVIRONMENT=$ENVIRONMENT cdk deploy --require-approval never
    fi
    
    cd ..
    print_success "Infrastructure deployed successfully"
}

# Function to update ECS services
update_services() {
    if [[ "$UPDATE_SERVICES" == "false" ]]; then
        print_warning "Skipping service updates"
        return
    fi
    
    print_status "Updating ECS services..."
    
    # Update API service
    aws ecs update-service \
        --cluster $ECS_CLUSTER \
        --service modernaction-api-$ENVIRONMENT \
        --force-new-deployment >/dev/null
    
    # Update Web service
    aws ecs update-service \
        --cluster $ECS_CLUSTER \
        --service modernaction-web-$ENVIRONMENT \
        --force-new-deployment >/dev/null
    
    print_success "Services updated successfully"
}

# Function to wait for service stability
wait_for_stability() {
    if [[ "$WAIT_FOR_STABILITY" == "false" ]]; then
        print_warning "Skipping stability wait"
        return
    fi
    
    print_status "Waiting for services to stabilize..."
    
    aws ecs wait services-stable \
        --cluster $ECS_CLUSTER \
        --services modernaction-api-$ENVIRONMENT modernaction-web-$ENVIRONMENT
    
    print_success "Services are stable"
}

# Function to run health checks
run_health_checks() {
    if [[ "$HEALTH_CHECK" == "false" ]]; then
        print_warning "Skipping health checks"
        return
    fi
    
    print_status "Running health checks..."
    
    # API Health Check
    print_status "Checking API health..."
    API_STATUS=$(curl -s -o /dev/null -w "%{http_code}" https://$DOMAIN/api/v1/health)
    if [[ "$API_STATUS" == "200" ]]; then
        print_success "API Health: PASS"
    else
        print_error "API Health: FAIL (HTTP $API_STATUS)"
        exit 1
    fi
    
    # Web Application Check
    print_status "Checking web application..."
    WEB_STATUS=$(curl -s -o /dev/null -w "%{http_code}" https://$DOMAIN/campaigns)
    if [[ "$WEB_STATUS" == "200" ]]; then
        print_success "Web Application: PASS"
    else
        print_error "Web Application: FAIL (HTTP $WEB_STATUS)"
        exit 1
    fi
    
    # Auth0 Configuration Check
    print_status "Checking Auth0 configuration..."
    if [[ "$ENVIRONMENT" == "staging" ]]; then
        EXPECTED_TENANT="dev-vvwd64m28nwqm871"
    else
        EXPECTED_TENANT="dev-cp0hhr8rujq87exh"
    fi
    
    AUTH_CHECK=$(curl -s https://$DOMAIN | grep -o "$EXPECTED_TENANT" | head -1 || echo "")
    if [[ "$AUTH_CHECK" == "$EXPECTED_TENANT" ]]; then
        print_success "Auth0 Configuration: PASS ($EXPECTED_TENANT)"
    else
        print_warning "Auth0 Configuration: Could not verify tenant (may be normal)"
    fi
    
    print_success "Health checks completed"
}

# Function to display deployment summary
show_summary() {
    print_status "Deployment Summary"
    echo "===================="
    echo "Environment: $ENVIRONMENT"
    echo "Domain: https://$DOMAIN"
    echo "API URL: $API_URL"
    echo "Auth0 Tenant: $AUTH0_DOMAIN"
    echo "ECS Cluster: $ECS_CLUSTER"
    echo ""
    echo "Next steps:"
    echo "1. Visit https://$DOMAIN to test the application"
    echo "2. Test authentication by clicking 'Sign In'"
    echo "3. Monitor services: aws ecs describe-services --cluster $ECS_CLUSTER"
    echo "4. View logs: aws logs tail /aws/ecs/modernaction-web-$ENVIRONMENT --follow"
}

# Main deployment flow
main() {
    print_status "ModernAction Deployment Script v2.0"
    
    check_prerequisites
    build_images
    deploy_infrastructure
    update_services
    wait_for_stability
    run_health_checks
    show_summary
    
    print_success "🚀 Deployment to $ENVIRONMENT completed successfully!"
}

# Run main function
main "$@"