#!/bin/bash

# ModernAction Health Check Script
# Usage: ./health-check.sh [staging|prod]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[✅ PASS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[⚠️ WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[❌ FAIL]${NC} $1"
}

# Get environment
ENVIRONMENT=${1:-staging}

# Set environment-specific variables
if [[ "$ENVIRONMENT" == "staging" ]]; then
    BASE_URL="https://staging.modernaction.io"
    EXPECTED_TENANT="dev-vvwd64m28nwqm871"
    ECS_CLUSTER="modernaction-staging"
elif [[ "$ENVIRONMENT" == "prod" ]]; then
    BASE_URL="https://modernaction.io"
    EXPECTED_TENANT="dev-cp0hhr8rujq87exh"
    ECS_CLUSTER="modernaction-prod"
else
    echo "Usage: $0 [staging|prod]"
    exit 1
fi

print_status "🔍 Health Check for $ENVIRONMENT environment"
print_status "📍 Testing: $BASE_URL"
echo ""

# 1. API Health Check
print_status "1. API Health Check..."
API_STATUS=$(curl -s -o /dev/null -w "%{http_code}" $BASE_URL/api/v1/health)
if [[ "$API_STATUS" == "200" ]]; then
    print_success "API Health endpoint responding"
    
    # Get API details
    API_RESPONSE=$(curl -s $BASE_URL/api/v1/health)
    if command -v jq >/dev/null 2>&1; then
        echo "   Response: $(echo $API_RESPONSE | jq -c .)"
    else
        echo "   Response: $API_RESPONSE"
    fi
else
    print_error "API Health check failed (HTTP $API_STATUS)"
fi

# 2. Web Application Check
print_status "2. Web Application Check..."
WEB_STATUS=$(curl -s -o /dev/null -w "%{http_code}" $BASE_URL)
if [[ "$WEB_STATUS" == "200" ]]; then
    print_success "Web application loading"
else
    print_error "Web application failed (HTTP $WEB_STATUS)"
fi

# 3. Database Connectivity Check
print_status "3. Database Connectivity Check..."
DB_STATUS=$(curl -s -o /dev/null -w "%{http_code}" $BASE_URL/api/v1/campaigns)
if [[ "$DB_STATUS" == "200" ]]; then
    print_success "Database connectivity working"
else
    print_error "Database connectivity failed (HTTP $DB_STATUS)"
fi

# 4. Auth0 Configuration Check
print_status "4. Auth0 Configuration Check..."
AUTH_CHECK=$(curl -s $BASE_URL | grep -o "$EXPECTED_TENANT" | head -1 || echo "")
if [[ "$AUTH_CHECK" == "$EXPECTED_TENANT" ]]; then
    print_success "Auth0 tenant configuration correct ($EXPECTED_TENANT)"
else
    print_warning "Auth0 tenant verification inconclusive"
fi

# 5. SSL Certificate Check
print_status "5. SSL Certificate Check..."
SSL_INFO=$(curl -s -I $BASE_URL | grep -i "strict-transport-security" || echo "")
if [[ -n "$SSL_INFO" ]]; then
    print_success "SSL/HTTPS working with security headers"
else
    print_warning "SSL working but security headers may be missing"
fi

# 6. ECS Service Status
print_status "6. ECS Service Status..."
if command -v aws >/dev/null 2>&1; then
    ECS_STATUS=$(aws ecs describe-services --cluster $ECS_CLUSTER \
        --services modernaction-api-$ENVIRONMENT modernaction-web-$ENVIRONMENT \
        --query "services[].{Name:serviceName,Status:status,Running:runningCount,Desired:desiredCount}" \
        --output table 2>/dev/null || echo "AWS CLI not configured")
    
    if [[ "$ECS_STATUS" != "AWS CLI not configured" ]]; then
        print_success "ECS Service Status:"
        echo "$ECS_STATUS"
    else
        print_warning "ECS status check skipped (AWS CLI not configured)"
    fi
else
    print_warning "ECS status check skipped (AWS CLI not installed)"
fi

# 7. Response Time Check
print_status "7. Response Time Check..."
RESPONSE_TIME=$(curl -s -o /dev/null -w "%{time_total}" $BASE_URL/api/v1/health)
RESPONSE_MS=$(echo "$RESPONSE_TIME * 1000" | bc -l 2>/dev/null || echo "${RESPONSE_TIME}000")
if (( $(echo "$RESPONSE_TIME < 2.0" | bc -l 2>/dev/null || echo "1") )); then
    print_success "Response time: ${RESPONSE_MS%.*}ms (Good)"
else
    print_warning "Response time: ${RESPONSE_MS%.*}ms (Slow)"
fi

echo ""
print_status "📊 Health Check Summary"
echo "========================"
echo "Environment: $ENVIRONMENT"
echo "Base URL: $BASE_URL"
echo "Expected Auth0 Tenant: $EXPECTED_TENANT"
echo "ECS Cluster: $ECS_CLUSTER"
echo ""

# Overall status
if [[ "$API_STATUS" == "200" && "$WEB_STATUS" == "200" && "$DB_STATUS" == "200" ]]; then
    print_success "🎉 Overall Status: HEALTHY"
    echo ""
    echo "🔗 Quick Links:"
    echo "   • Application: $BASE_URL"
    echo "   • API Health: $BASE_URL/api/v1/health"
    echo "   • Campaigns: $BASE_URL/campaigns"
    echo "   • Sign In Test: $BASE_URL (click Sign In button)"
    
    if [[ "$ENVIRONMENT" == "staging" ]]; then
        echo ""
        echo "🧪 Testing Steps:"
        echo "   1. Visit $BASE_URL"
        echo "   2. Click 'Sign In' button"
        echo "   3. Verify redirect to dev-vvwd64m28nwqm871.us.auth0.com"
        echo "   4. Test Google OAuth login"
        echo "   5. Verify successful return to application"
    fi
else
    print_error "🚨 Overall Status: UNHEALTHY"
    echo ""
    echo "🛠️ Troubleshooting:"
    echo "   • Check ECS service status: aws ecs describe-services --cluster $ECS_CLUSTER"
    echo "   • View logs: aws logs tail /aws/ecs/modernaction-web-$ENVIRONMENT --follow"
    echo "   • Check deployments: gh run list --limit 3"
fi

echo ""