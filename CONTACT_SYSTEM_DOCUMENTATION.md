# Contact Message System Documentation

## Overview

The ModernAction contact system provides a comprehensive solution for users to reach out to the platform administrators. It includes a user-facing contact form, backend API for message processing, and an admin interface for message management.

## Architecture

### Frontend Components

#### Contact Page (`/contact`)
- **Location**: `apps/web/src/app/contact/page.tsx` (Server Component)
- **Client Component**: `apps/web/src/app/contact/ContactPageClient.tsx`
- **Features**:
  - Hero section with call-to-action
  - Contact method cards (General, Press, Partnerships, Technical)
  - Contact form with validation
  - FAQ section
  - Additional contact information

#### Admin Interface
- **Location**: `apps/web/src/components/admin/ContactMessagesSection.tsx`
- **Features**:
  - Statistics dashboard (total, unread, urgent, today, this week)
  - Message filtering (status, category, priority)
  - Message table with actions
  - Detailed message view modal
  - Bulk operations support

### Backend Components

#### Database Model
- **Location**: `apps/api/app/models/contact_message.py`
- **Table**: `contact_messages`
- **Key Fields**:
  - Basic info: name, email, subject, category, message
  - Status tracking: status, priority, assigned_to, admin_notes
  - Response tracking: responded_at, response_method
  - Metadata: ip_address, user_agent, referrer
  - Spam detection: is_spam, spam_score

#### API Endpoints
- **Location**: `apps/api/app/api/v1/endpoints/contact_messages.py`
- **Base URL**: `/api/v1/contact-messages/`

### API Reference

#### Public Endpoints

##### Submit Contact Message
```http
POST /api/v1/contact-messages/
Content-Type: application/json

{
  "name": "John Doe",
  "email": "<EMAIL>",
  "subject": "Question about platform",
  "category": "general",
  "message": "I have a question about...",
  "phone_number": "******-123-4567",
  "preferred_contact_method": "email"
}
```

**Response**: ContactMessage object with computed fields

#### Admin Endpoints (Authentication Required)

##### Get All Messages
```http
GET /api/v1/contact-messages/
Authorization: Bearer <token>

Query Parameters:
- skip: number (pagination offset)
- limit: number (max 50)
- status_filter: string (unread, read, responded, resolved, archived)
- category_filter: string (general, support, technical, partnerships, press, feedback)
- priority_filter: string (low, normal, high, urgent)
- search: string (searches name, email, subject, message)
```

##### Get Message Statistics
```http
GET /api/v1/contact-messages/stats
Authorization: Bearer <token>
```

**Response**:
```json
{
  "total_messages": 150,
  "unread_count": 12,
  "urgent_count": 3,
  "today_count": 5,
  "this_week_count": 28,
  "by_category": {
    "general": 45,
    "technical": 30,
    "support": 25
  },
  "by_status": {
    "unread": 12,
    "read": 8,
    "responded": 130
  }
}
```

##### Update Message
```http
PATCH /api/v1/contact-messages/{id}
Authorization: Bearer <token>
Content-Type: application/json

{
  "status": "responded",
  "priority": "high",
  "admin_notes": "Resolved via email",
  "assigned_to": "<EMAIL>"
}
```

##### Mark as Responded
```http
POST /api/v1/contact-messages/{id}/mark-as-responded
Authorization: Bearer <token>
Content-Type: application/json

{
  "response_method": "email"
}
```

##### Bulk Update
```http
POST /api/v1/contact-messages/bulk-update
Authorization: Bearer <token>
Content-Type: application/json

{
  "message_ids": ["id1", "id2", "id3"],
  "status": "read",
  "priority": "normal"
}
```

## Data Models

### ContactMessage
```typescript
interface ContactMessage {
  id: string;
  name: string;
  email: string;
  subject: string;
  category: string;
  message: string;
  status: 'unread' | 'read' | 'responded' | 'resolved' | 'archived';
  priority: 'low' | 'normal' | 'high' | 'urgent';
  assigned_to?: string;
  admin_notes?: string;
  phone_number?: string;
  preferred_contact_method: 'email' | 'phone';
  responded_at?: string;
  response_method?: string;
  ip_address?: string;
  user_agent?: string;
  referrer?: string;
  is_spam: boolean;
  spam_score?: string;
  user_id?: string;
  created_at: string;
  updated_at: string;
  // Computed properties
  is_urgent: boolean;
  short_message: string;
}
```

### ContactMessageCreate
```typescript
interface ContactMessageCreate {
  name: string;
  email: string;
  subject: string;
  category: string;
  message: string;
  phone_number?: string;
  preferred_contact_method?: 'email' | 'phone';
}
```

## Features

### Automatic Priority Assignment
- **Technical/Support categories** → High priority
- Messages containing urgent keywords → Urgent priority
- Default → Normal priority

### Spam Detection
- Keyword-based filtering for common spam terms
- Automatic flagging with spam score
- Admin can review flagged messages

### Request Metadata Tracking
- IP address capture for analytics
- User agent and referrer tracking
- Helps identify patterns and sources

### Admin Management Features
- **Status Management**: Track message lifecycle
- **Assignment**: Assign messages to specific admins
- **Notes**: Internal admin notes
- **Response Tracking**: When and how responses were sent
- **Bulk Operations**: Update multiple messages at once
- **Search & Filter**: Find messages quickly

### Message Categories
- **General**: General inquiries and questions
- **Support**: Technical support requests
- **Technical**: Bug reports and technical issues
- **Partnerships**: Partnership and collaboration requests
- **Press**: Media and press inquiries
- **Feedback**: User feedback and suggestions

## Integration

### Frontend Integration
```typescript
import { contactMessagesApi } from '../services/apiClient';

// Submit contact message
const result = await contactMessagesApi.create({
  name: 'John Doe',
  email: '<EMAIL>',
  subject: 'Question',
  category: 'general',
  message: 'My question...',
  preferred_contact_method: 'email'
});

// Admin: Get messages with filters
const messages = await contactMessagesApi.getAll({
  status_filter: 'unread',
  limit: 20
}, accessToken);

// Admin: Get statistics
const stats = await contactMessagesApi.getStats(accessToken);
```

### Authentication
- Public endpoints: No authentication required
- Admin endpoints: Require valid JWT token in Authorization header
- Uses Auth0 for authentication in production

## Database Schema

```sql
CREATE TABLE contact_messages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    subject VARCHAR(500) NOT NULL,
    category VARCHAR(50) NOT NULL,
    message TEXT NOT NULL,
    status VARCHAR(50) DEFAULT 'unread',
    priority VARCHAR(20) DEFAULT 'normal',
    assigned_to VARCHAR(255),
    admin_notes TEXT,
    phone_number VARCHAR(20),
    preferred_contact_method VARCHAR(10) DEFAULT 'email',
    responded_at TIMESTAMP,
    response_method VARCHAR(50),
    ip_address VARCHAR(45),
    user_agent TEXT,
    referrer TEXT,
    is_spam BOOLEAN DEFAULT FALSE,
    spam_score VARCHAR(20),
    user_id UUID REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for performance
CREATE INDEX idx_contact_messages_email ON contact_messages(email);
CREATE INDEX idx_contact_messages_status ON contact_messages(status);
CREATE INDEX idx_contact_messages_category ON contact_messages(category);
CREATE INDEX idx_contact_messages_created_at ON contact_messages(created_at);
```

## Error Handling

### Frontend Error Handling
- Form validation with required field checking
- API error responses with user-friendly messages
- Toast notifications for success/error states
- Loading states during submission

### Backend Error Handling
- Input validation using Pydantic schemas
- Proper HTTP status codes
- Detailed error messages for debugging
- Authentication/authorization error handling

## Testing

### Manual Testing Checklist
- [ ] Contact form submission works
- [ ] Required field validation
- [ ] Success message after submission
- [ ] Message appears in database
- [ ] Admin can view messages
- [ ] Admin authentication required
- [ ] Message filtering works
- [ ] Status updates work
- [ ] Bulk operations work

### API Testing Examples
```bash
# Test public endpoint
curl -X POST http://localhost:8000/api/v1/contact-messages/ \
  -H "Content-Type: application/json" \
  -d '{"name":"Test","email":"<EMAIL>","subject":"Test","category":"general","message":"Test message"}'

# Test admin endpoint (should fail without auth)
curl -X GET http://localhost:8000/api/v1/contact-messages/stats
```

## Deployment Considerations

### Environment Variables
- `DATABASE_URL`: PostgreSQL connection string
- `NEXT_PUBLIC_API_URL`: API base URL for frontend

### Security
- Admin endpoints require authentication
- Input sanitization and validation
- Rate limiting should be implemented
- CORS configuration for cross-origin requests

### Monitoring
- Track message volume and response times
- Monitor spam detection effectiveness
- Alert on urgent messages
- Response time metrics for admin team

## Future Enhancements

- **Email Notifications**: Notify admins of new urgent messages
- **Advanced Spam Detection**: Machine learning-based spam filtering
- **Response Templates**: Pre-built response templates for common questions
- **SLA Tracking**: Track response time goals
- **Integration**: Connect with help desk systems
- **Auto-categorization**: AI-powered category assignment
- **Customer Portal**: Allow users to track their message status