# Auth0 Configuration Guide - ModernAction

## Overview
This document outlines the bulletproof Auth0 setup for ModernAction using separate tenants for staging and production environments.

## Architecture Decision
✅ **Tenant Strategy**: Separate Auth0 tenants for maximum isolation
- **Staging Tenant**: `dev-vvwd64m28nwqm871.us.auth0.com`
- **Production Tenant**: `[TO BE CREATED]`

## Current Configuration Status

### Phase 1 Complete ✅
#### Staging Tenant (dev-vvwd64m28nwqm871.us.auth0.com)
- **Application**: `ModernAction.io - Staging (RWA)` ✅
  - Client ID: `Jtv4r9uS9BSeahx5jp93v0Ur1K7b5ihC`
  - Type: Regular Web Application
  - Callbacks: `http://localhost:3000/api/auth/callback`, `https://staging.modernaction.io/api/auth/callback`
  - Logout URLs: `http://localhost:3000`, `https://staging.modernaction.io`
  - Origins: `http://localhost:3000`, `https://staging.modernaction.io`

- **Cleanup Complete** ✅
  - Deleted: Default App, ModernAction (SPA), ModernAction API (Test), My App, ModernAction Web App
  - Remaining: Only the properly configured staging application

- **Social Connections** ✅
  - Google OAuth2: Configured (Connection ID: `con_w2bojAt6jjZ63uCp`)
  - Username-Password: Configured with "Good" password policy
  - Twitter: **[NEEDS SETUP - See instructions below]**

### Phase 1 Complete ✅
#### Production Tenant (dev-cp0hhr8rujq87exh.us.auth0.com)
- **Application**: `ModernAction.io - Production (RWA)` ✅
  - Client ID: `JZYS6kWu4jKPHmajvukycBDgbhCrPNIx`
  - Type: Single Page Web Application
  - Callbacks: `https://modernaction.io/api/auth/callback`
  - Logout URLs: `https://modernaction.io`
  - Origins: `https://modernaction.io`

- **Cleanup Complete** ✅
  - Deleted: Default App
  - Remaining: Only the properly configured production application

- **Social Connections** ✅
  - Google OAuth2: Configured (Connection ID: `con_WCeQj80kGAiELsRn`) - **NEEDS CREDENTIALS**
  - Username-Password: Configured with "Good" password policy

### Phase 1 Pending 🔄

#### Social Login Credentials Needed
**Google OAuth2**:
- Requires: Google Cloud Console OAuth 2.0 Client credentials
- Status: Current setup may need proper production credentials

**Twitter OAuth**:
- Requires: Twitter Developer App credentials  
- Status: Not yet configured

## Setup Instructions

### 1. Production Tenant Creation
```bash
# 1. Create new Auth0 account at https://auth0.com/signup
# 2. Note the new tenant domain (e.g., prod-xyz123.auth0.com)
# 3. Add to CLI:
auth0 tenants add [new-production-tenant-domain]
auth0 tenants use [new-production-tenant-domain]

# 4. Create production application:
auth0 apps create \
  --name "ModernAction.io - Production (RWA)" \
  --type "regular-web" \
  --callbacks "https://modernaction.io/api/auth/callback" \
  --logout-urls "https://modernaction.io" \
  --origins "https://modernaction.io" \
  --web-origins "https://modernaction.io"
```

### 2. Social Login Setup

#### Google OAuth2 Setup
1. **Google Cloud Console** (https://console.cloud.google.com):
   ```
   1. Create/select project
   2. Enable Google+ API
   3. OAuth consent screen → External → Fill required fields
   4. Credentials → Create OAuth 2.0 Client ID
   5. Application type: Web application
   6. Authorized redirect URIs:
      - Staging: https://dev-vvwd64m28nwqm871.us.auth0.com/login/callback
      - Production: https://[PROD-TENANT].auth0.com/login/callback
   ```

2. **Auth0 Configuration**:
   ```bash
   # Update Google connection with real credentials
   auth0 api patch "connections/con_w2bojAt6jjZ63uCp" --data '{
     "options": {
       "client_id": "[GOOGLE_CLIENT_ID]",
       "client_secret": "[GOOGLE_CLIENT_SECRET]",
       "email": true,
       "profile": true,
       "scope": ["email", "profile"]
     }
   }'
   ```

#### Twitter OAuth Setup
1. **Twitter Developer Portal** (https://developer.twitter.com):
   ```
   1. Apply for developer account
   2. Create new app
   3. App permissions → Read and write
   4. Callback URLs:
      - Staging: https://dev-vvwd64m28nwqm871.us.auth0.com/login/callback
      - Production: https://[PROD-TENANT].auth0.com/login/callback
   ```

2. **Auth0 Configuration**:
   ```bash
   # Create Twitter connection
   auth0 api post "connections" --data '{
     "strategy": "twitter",
     "name": "twitter",
     "options": {
       "consumer_key": "[TWITTER_API_KEY]",
       "consumer_secret": "[TWITTER_API_SECRET]"
     },
     "enabled_clients": ["[CLIENT_ID]"]
   }'
   ```

### 3. Custom Domains Setup

#### DNS Configuration Required
```
# Add CNAME records to your DNS:
auth.staging.modernaction.io → CNAME → [auth0-provided-domain]
auth.modernaction.io → CNAME → [auth0-provided-domain]
```

#### Auth0 Custom Domain Setup
```bash
# Configure custom domains (requires Auth0 Dashboard)
# 1. Go to Auth0 Dashboard → Branding → Custom Domains
# 2. Add domain: auth.staging.modernaction.io (staging tenant)
# 3. Add domain: auth.modernaction.io (production tenant)
# 4. Follow SSL certificate verification process
```

### 4. Security Configuration ✅

#### Password Policy
- **Current**: "Good" policy (balanced security/UX)
- **MFA**: Available for admin roles (not required for standard users at launch)
- **Brute Force Protection**: Enabled

#### Session Management
- **Refresh Tokens**: Non-rotating, non-expiring (can be modified post-launch)
- **Token Lifetime**: 30 days
- **Idle Token Lifetime**: 15 days

## AWS Integration

### Secrets Manager Configuration
Current secrets need updating with proper tenant separation:

#### Staging Secrets
```bash
# Update staging secrets with proper values
aws secretsmanager update-secret \
  --secret-id "Auth0SecretE68A07FE-ffbFX6p8FqXw" \
  --secret-string '{
    "AUTH0_SECRET": "[GENERATE_32_CHAR_SECRET]",
    "AUTH0_CLIENT_SECRET": "[FROM_AUTH0_STAGING_APP]"
  }'
```

#### Production Secrets (When Ready)
```bash
# Create production secrets
aws secretsmanager create-secret \
  --name "Auth0Secret-Production" \
  --secret-string '{
    "AUTH0_SECRET": "[GENERATE_32_CHAR_SECRET]",
    "AUTH0_CLIENT_SECRET": "[FROM_AUTH0_PRODUCTION_APP]"
  }'
```

### Environment Variables Update Required
Update CDK configuration to use proper Auth0 domains:

```python
# infrastructure/modernaction/modernaction_stack.py
# Staging environment:
"AUTH0_DOMAIN": "dev-vvwd64m28nwqm871.us.auth0.com",  # Current
"AUTH0_DOMAIN": "auth.staging.modernaction.io",       # Future (custom domain)

# Production environment:
"AUTH0_DOMAIN": "[PROD-TENANT].auth0.com",            # Initial
"AUTH0_DOMAIN": "auth.modernaction.io",               # Future (custom domain)
```

## Testing Checklist

### Staging Environment Testing
- [ ] Username/password registration
- [ ] Username/password login
- [ ] Google OAuth login
- [ ] Twitter OAuth login (when configured)
- [ ] Password reset flow
- [ ] Logout functionality
- [ ] Session persistence

### Production Environment Testing (When Ready)
- [ ] All staging tests repeated
- [ ] Custom domain functionality
- [ ] SSL certificate validation
- [ ] Cross-environment isolation verified

## Next Steps

1. **Immediate** (Phase 1 Completion):
   - Create production Auth0 tenant
   - Obtain Google/Twitter credentials
   - Configure custom domains

2. **Phase 2** (Integration):
   - Update AWS secrets with real credentials
   - Update CDK configuration
   - Deploy and test staging
   - Deploy production when ready

3. **Phase 3** (Post-Launch):
   - Monitor Auth0 logs and analytics
   - Consider MFA enablement for admin users
   - Review and optimize session policies
   - Set up Auth0 alerts and monitoring

## Security Notes

- ✅ Complete tenant isolation achieved
- ✅ Staging cannot access production users/data
- ✅ Clean application configuration (no leftover test apps)
- ✅ Modern password policy enabled
- ✅ Brute force protection active
- 🔄 Custom domains pending (improves user trust)
- 🔄 Social login credentials pending (production-ready setup)

---
*Last Updated: 2025-08-20*
*Configured by: Claude Code*