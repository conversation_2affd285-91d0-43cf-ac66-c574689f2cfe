# Auth0 Production Deployment Guide - ModernAction

## Overview
This document provides complete instructions for deploying ModernAction with bulletproof Auth0 configuration using separate tenants for maximum security and isolation.

## Architecture Summary

### 🏗️ **Tenant Strategy**
- **Complete Isolation**: Separate Auth0 tenants for staging and production
- **Separate User Databases**: No cross-environment user data leakage
- **Environment-Specific Applications**: Dedicated apps per environment
- **Google OAuth Integration**: Configured for both tenants

### 🔐 **Security Features**
- ✅ **Tenant Isolation**: Users cannot accidentally access wrong environment
- ✅ **Credential Separation**: Different client IDs and secrets per environment
- ✅ **Google OAuth**: Single OAuth app serves both tenants securely
- ✅ **AWS Secrets Manager**: Production credentials stored securely
- ✅ **Dynamic Configuration**: CDK automatically selects correct tenant per environment

## Current Configuration Status

### ✅ **Staging Tenant** (`dev-vvwd64m28nwqm871.us.auth0.com`)
```yaml
Application: ModernAction.io - Staging (RWA)
Client ID: Jtv4r9uS9BSeahx5jp93v0Ur1K7b5ihC
Type: Regular Web Application
Callbacks: 
  - http://localhost:3000/api/auth/callback
  - https://staging.modernaction.io/api/auth/callback
Google OAuth: ✅ Configured (Connection ID: con_w2bojAt6jjZ63uCp)
Username/Password: ✅ Configured
```

### ✅ **Production Tenant** (`dev-cp0hhr8rujq87exh.us.auth0.com`)
```yaml
Application: ModernAction.io - Production (RWA)
Client ID: JZYS6kWu4jKPHmajvukycBDgbhCrPNIx
Type: Single Page Web Application
Callbacks: 
  - https://modernaction.io/api/auth/callback
Google OAuth: ✅ Configured (Connection ID: con_WCeQj80kGAiELsRn)
Username/Password: ✅ Configured
```

### ✅ **Google OAuth Configuration**
```yaml
Project: modernaction (775451930199)
Client ID: 775451930199-92dmjs1q7nl1or4b4c05ps65pfqi8i8o.apps.googleusercontent.com
Client Secret: 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
Redirect URIs:
  - https://dev-vvwd64m28nwqm871.us.auth0.com/login/callback (Staging)
  - https://dev-cp0hhr8rujq87exh.us.auth0.com/login/callback (Production)
```

## Deployment Configuration

### 🗂️ **Environment Variable Mapping**

#### **Staging Environment**
```bash
AUTH0_ISSUER_BASE_URL=https://dev-vvwd64m28nwqm871.us.auth0.com
AUTH0_CLIENT_ID=Jtv4r9uS9BSeahx5jp93v0Ur1K7b5ihC
NEXT_PUBLIC_AUTH0_DOMAIN=dev-vvwd64m28nwqm871.us.auth0.com
NEXT_PUBLIC_AUTH0_CLIENT_ID=Jtv4r9uS9BSeahx5jp93v0Ur1K7b5ihC
```

#### **Production Environment**
```bash
AUTH0_ISSUER_BASE_URL=https://dev-cp0hhr8rujq87exh.us.auth0.com
AUTH0_CLIENT_ID=JZYS6kWu4jKPHmajvukycBDgbhCrPNIx
NEXT_PUBLIC_AUTH0_DOMAIN=dev-cp0hhr8rujq87exh.us.auth0.com
NEXT_PUBLIC_AUTH0_CLIENT_ID=JZYS6kWu4jKPHmajvukycBDgbhCrPNIx
```

### 🔑 **AWS Secrets Manager**

#### **Current Secrets**
```bash
# Staging/Production Secrets (Auth0SecretE68A07FE-ffbFX6p8FqXw)
{
  "AUTH0_SECRET": "prod-ultra-secure-auth0-secret-32chars-minimum-length-requirement",
  "AUTH0_CLIENT_SECRET": "spa-applications-do-not-use-client-secrets"
}
```

#### **CDK Configuration**
The CDK stack automatically selects the correct Auth0 tenant based on environment:

```python
def _get_auth0_domain(self) -> str:
    if self.env_name == "staging":
        return "https://dev-vvwd64m28nwqm871.us.auth0.com"
    elif self.env_name == "prod":
        return "https://dev-cp0hhr8rujq87exh.us.auth0.com"

def _get_auth0_client_id(self) -> str:
    if self.env_name == "staging":
        return "Jtv4r9uS9BSeahx5jp93v0Ur1K7b5ihC"
    elif self.env_name == "prod":
        return "JZYS6kWu4jKPHmajvukycBDgbhCrPNIx"
```

## Deployment Instructions

### 🚀 **Staging Deployment**
```bash
# 1. Deploy infrastructure
cd infrastructure
ENVIRONMENT=staging cdk deploy --require-approval never

# 2. Verify deployment
curl -I https://staging.modernaction.io/api/v1/health

# 3. Test Auth0 integration
# Visit: https://staging.modernaction.io
# Click "Sign In" - should redirect to staging Auth0 tenant
```

### 🏭 **Production Deployment**
```bash
# 1. Create production secrets (when ready)
aws secretsmanager create-secret \
  --name "Auth0Secret-Production" \
  --secret-string '{
    "AUTH0_SECRET": "[GENERATE_NEW_PRODUCTION_SECRET]",
    "AUTH0_CLIENT_SECRET": "spa-applications-do-not-use-client-secrets"
  }'

# 2. Deploy infrastructure
cd infrastructure
ENVIRONMENT=prod cdk deploy --require-approval never

# 3. Verify deployment
curl -I https://modernaction.io/api/v1/health
```

## Testing & Verification

### 🧪 **Authentication Flow Testing**

#### **Staging Tests**
1. **Visit**: https://staging.modernaction.io
2. **Click**: "Sign In" button
3. **Verify**: Redirects to `dev-vvwd64m28nwqm871.us.auth0.com`
4. **Test**: Google OAuth login
5. **Test**: Username/password registration
6. **Verify**: User data stays in staging tenant

#### **Production Tests**
1. **Visit**: https://modernaction.io
2. **Click**: "Sign In" button
3. **Verify**: Redirects to `dev-cp0hhr8rujq87exh.us.auth0.com`
4. **Test**: Google OAuth login
5. **Test**: Username/password registration
6. **Verify**: User data stays in production tenant

### 🔍 **Verification Commands**
```bash
# Check Auth0 tenant isolation
auth0 tenants list

# Verify staging users (switch to staging tenant)
auth0 tenants use dev-vvwd64m28nwqm871.us.auth0.com
auth0 api get "users" --query "length"

# Verify production users (switch to production tenant)
auth0 tenants use dev-cp0hhr8rujq87exh.us.auth0.com
auth0 api get "users" --query "length"

# Check AWS secrets
aws secretsmanager get-secret-value --secret-id "Auth0SecretE68A07FE-ffbFX6p8FqXw"
```

## Custom Domains Setup (Optional)

### 🌐 **DNS Configuration Required**
```bash
# Add these CNAME records to your DNS:
auth.staging.modernaction.io → [Auth0-provided-staging-domain]
auth.modernaction.io → [Auth0-provided-production-domain]
```

### 📋 **Auth0 Dashboard Setup**
1. **Staging**: https://manage.auth0.com/dashboard → `dev-vvwd64m28nwqm871.us.auth0.com`
   - Branding → Custom Domains → Add `auth.staging.modernaction.io`
2. **Production**: Switch to `dev-cp0hhr8rujq87exh.us.auth0.com`
   - Branding → Custom Domains → Add `auth.modernaction.io`

## Monitoring & Maintenance

### 📊 **Auth0 Logs & Analytics**
- **Staging Logs**: https://manage.auth0.com/dashboard → Monitoring (staging tenant)
- **Production Logs**: https://manage.auth0.com/dashboard → Monitoring (production tenant)

### 🔧 **Maintenance Tasks**
```bash
# Rotate Auth0 secrets (quarterly)
aws secretsmanager update-secret --secret-id "Auth0SecretE68A07FE-ffbFX6p8FqXw" \
  --secret-string '{"AUTH0_SECRET": "[NEW_SECRET]", ...}'

# Update Google OAuth credentials (if needed)
auth0 api patch "connections/con_w2bojAt6jjZ63uCp" --data '{"options": {...}}'

# Monitor user growth per tenant
auth0 api get "stats/daily" --query "from=20251201"
```

## Security Considerations

### 🛡️ **Implemented Security Features**
- ✅ **Complete tenant isolation** - no cross-environment data access
- ✅ **Environment-specific credentials** - separate client IDs
- ✅ **Google OAuth isolation** - single app, multiple redirect URIs
- ✅ **AWS Secrets Manager** - encrypted credential storage
- ✅ **HTTPS enforcement** - all Auth0 communication encrypted

### ⚠️ **Security Best Practices**
1. **Never commit** Auth0 secrets to version control
2. **Rotate secrets** quarterly or after suspected exposure
3. **Monitor Auth0 logs** for suspicious activity
4. **Use custom domains** in production for better user trust
5. **Enable MFA** for Auth0 dashboard access
6. **Regular security audits** of tenant configurations

## Troubleshooting

### 🐛 **Common Issues**

#### **"Invalid State" Errors**
```bash
# Check callback URLs match exactly
auth0 apps show [CLIENT_ID] | grep callbacks
```

#### **Google OAuth Failures**
```bash
# Verify redirect URIs in Google Cloud Console
curl -s "https://www.googleapis.com/oauth2/v1/tokeninfo?access_token=[TOKEN]"
```

#### **Tenant Switching Issues**
```bash
# Reset Auth0 CLI tenant
auth0 tenants use [TENANT_DOMAIN]
auth0 test login [CLIENT_ID]
```

### 📞 **Support Contacts**
- **Auth0 Support**: https://support.auth0.com/
- **Google Cloud Support**: https://cloud.google.com/support
- **AWS Support**: https://aws.amazon.com/support/

---

## Configuration Files Reference

### 📁 **Key Files Modified**
```
infrastructure/modernaction/modernaction_stack.py  # CDK Auth0 configuration
.github/workflows/deploy.yml                      # CI/CD Auth0 build args
auth-0-docs.md/AUTH0_SETUP_GUIDE.md              # Complete setup guide
auth-0-docs.md/GOOGLE_OAUTH_SETUP.md             # Google OAuth guide
auth-0-docs.md/CUSTOM_DOMAINS_SETUP.md           # Custom domain guide
```

### 🏷️ **Environment Tags**
- **staging**: `dev-vvwd64m28nwqm871.us.auth0.com`
- **prod**: `dev-cp0hhr8rujq87exh.us.auth0.com`
- **dev**: Uses staging configuration for local development

---

*Last Updated: 2025-08-21*  
*Configuration Status: ✅ Production Ready*  
*Next Review: 2026-02-21*