# Custom Domain Setup Instructions - Immediate Action Required

## 🚨 **Action Required: Set Up Custom Domains Now**

Your Auth0 tenants are ready for custom domain configuration. Follow these steps **immediately** for a professional authentication experience.

## 📋 **Step-by-Step Setup**

### **1. Staging Custom Domain Setup**

#### **Auth0 Dashboard - Staging Tenant**
1. **Go to**: https://manage.auth0.com/dashboard
2. **Switch to Staging Tenant**: `dev-vvwd64m28nwqm871.us.auth0.com`
3. **Navigate**: **Branding** → **Custom Domains**
4. **Click**: **"Set Up Custom Domain"**

#### **Configuration**
```
Domain Name: auth.staging.modernaction.io
Provider: Auth0 Managed Certificates
```

#### **DNS Configuration**
After setup, Auth0 will provide a CNAME record like:
```
auth.staging.modernaction.io → abc123-staging.edge.tenants.auth0.com
```

**Add this CNAME record to your DNS (Route53)**:
```bash
# Example - replace with actual values from Auth0
aws route53 change-resource-record-sets --hosted-zone-id Z123456789 --change-batch '{
  "Changes": [{
    "Action": "CREATE",
    "ResourceRecordSet": {
      "Name": "auth.staging.modernaction.io",
      "Type": "CNAME",
      "TTL": 300,
      "ResourceRecords": [{"Value": "[AUTH0_PROVIDED_DOMAIN]"}]
    }
  }]
}'
```

### **2. Production Custom Domain Setup**

#### **Auth0 Dashboard - Production Tenant**
1. **Switch to Production Tenant**: `dev-cp0hhr8rujq87exh.us.auth0.com`
2. **Navigate**: **Branding** → **Custom Domains**
3. **Click**: **"Set Up Custom Domain"**

#### **Configuration**
```
Domain Name: auth.modernaction.io
Provider: Auth0 Managed Certificates
```

#### **DNS Configuration**
Add the provided CNAME record:
```bash
# Example - replace with actual values from Auth0
aws route53 change-resource-record-sets --hosted-zone-id Z123456789 --change-batch '{
  "Changes": [{
    "Action": "CREATE",
    "ResourceRecordSet": {
      "Name": "auth.modernaction.io",
      "Type": "CNAME", 
      "TTL": 300,
      "ResourceRecords": [{"Value": "[AUTH0_PROVIDED_DOMAIN]"}]
    }
  }]
}'
```

## 🔧 **After Custom Domains Are Active**

Once both custom domains show **"Ready"** status in Auth0 Dashboard:

### **Update Application URLs**

#### **Staging Application**
```bash
auth0 tenants use dev-vvwd64m28nwqm871.us.auth0.com
auth0 apps update Jtv4r9uS9BSeahx5jp93v0Ur1K7b5ihC \
  --callbacks "http://localhost:3000/api/auth/callback,https://staging.modernaction.io/api/auth/callback" \
  --logout-urls "http://localhost:3000,https://staging.modernaction.io" \
  --origins "http://localhost:3000,https://staging.modernaction.io"
```

#### **Production Application**  
```bash
auth0 tenants use dev-cp0hhr8rujq87exh.us.auth0.com
auth0 apps update JZYS6kWu4jKPHmajvukycBDgbhCrPNIx \
  --callbacks "https://modernaction.io/api/auth/callback" \
  --logout-urls "https://modernaction.io" \
  --origins "https://modernaction.io"
```

### **Update CDK Configuration**

Update the Auth0 domain helper methods in `infrastructure/modernaction/modernaction_stack.py`:

```python
def _get_auth0_domain(self) -> str:
    """Get Auth0 domain based on environment"""
    if self.env_name == "staging":
        return "https://auth.staging.modernaction.io"  # Updated to custom domain
    elif self.env_name == "prod":
        return "https://auth.modernaction.io"  # Updated to custom domain
    else:  # dev
        return "https://dev-vvwd64m28nwqm871.us.auth0.com"

def _get_auth0_domain_public(self) -> str:
    """Get Auth0 domain for public/client-side use"""
    if self.env_name == "staging":
        return "auth.staging.modernaction.io"  # Updated to custom domain
    elif self.env_name == "prod":
        return "auth.modernaction.io"  # Updated to custom domain
    else:  # dev
        return "dev-vvwd64m28nwqm871.us.auth0.com"
```

### **Redeploy with Custom Domains**
```bash
# Redeploy staging with custom domain
cd infrastructure
ENVIRONMENT=staging cdk deploy --require-approval never

# Later, redeploy production with custom domain
ENVIRONMENT=prod cdk deploy --require-approval never
```

## ✅ **Verification Steps**

### **Test Custom Domain SSL**
```bash
# Should return 200 OK with valid SSL certificate
curl -I https://auth.staging.modernaction.io/.well-known/openid_configuration
curl -I https://auth.modernaction.io/.well-known/openid_configuration
```

### **Test Authentication Flow**
1. **Visit**: https://staging.modernaction.io
2. **Click**: "Sign In"
3. **Verify**: URL shows `auth.staging.modernaction.io` (not `dev-vvwd64m28nwqm871.us.auth0.com`)
4. **Complete**: Google OAuth or username/password login
5. **Verify**: Successful redirect back to application

## 🎯 **Benefits of Custom Domains**

- ✅ **Professional appearance**: Users see your domain during authentication
- ✅ **Increased trust**: No third-party domains in auth flow
- ✅ **Better branding**: Consistent domain experience
- ✅ **SSL certificates**: Automatically managed by Auth0
- ✅ **Security**: Reduces phishing risks from domain confusion

## 📞 **Support**

If you encounter issues:
1. **Auth0 Dashboard**: Check custom domain status and error messages
2. **DNS Verification**: Use `dig auth.staging.modernaction.io CNAME` to verify DNS propagation
3. **SSL Certificate**: May take 5-10 minutes to provision after DNS verification

---

**⚡ Start with staging custom domain setup now - this will significantly improve the user authentication experience!**