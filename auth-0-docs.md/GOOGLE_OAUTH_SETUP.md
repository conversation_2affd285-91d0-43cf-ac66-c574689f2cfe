# Google OAuth 2.0 Setup Guide

## Overview
This guide walks through creating Google OAuth 2.0 credentials for ModernAction's Auth0 integration.

## Step 1: Google Cloud Console Setup

### 1.1 Access Google Cloud Console
1. Go to: https://console.cloud.google.com/
2. Sign in with your primary company Google account

### 1.2 Create or Select Project
**Option A - Create New Project (Recommended)**:
1. Click the project dropdown at the top
2. Click "New Project"
3. **Project Name**: `ModernAction Auth`
4. **Organization**: Select your organization (if applicable)
5. Click "Create"

**Option B - Use Existing Project**:
1. Select your existing project from the dropdown

### 1.3 Enable Required APIs
1. Go to: **APIs & Services** → **Library**
2. Search for and enable:
   - **Google+ API** (for profile information)
   - **Google Identity Toolkit API** (for authentication)

## Step 2: OAuth Consent Screen

### 2.1 Configure Consent Screen
1. Go to: **APIs & Services** → **OAuth consent screen**
2. **User Type**: Select **"External"** (unless you have Google Workspace)
3. Click **"Create"**

### 2.2 App Information
Fill out the required fields:

```
App name: ModernAction
User support email: [your-company-email]
Developer contact information: [your-company-email]

App domain (optional but recommended):
- Application home page: https://modernaction.io
- Application privacy policy: https://modernaction.io/privacy
- Application terms of service: https://modernaction.io/terms
```

### 2.3 Scopes
1. Click **"Add or Remove Scopes"**
2. Select these scopes:
   - `../auth/userinfo.email`
   - `../auth/userinfo.profile`
   - `openid`
3. Click **"Update"**

### 2.4 Test Users (Optional)
Add your email and any developer emails for testing.

## Step 3: Create OAuth 2.0 Credentials

### 3.1 Create Credentials
1. Go to: **APIs & Services** → **Credentials**
2. Click **"+ Create Credentials"** → **"OAuth 2.0 Client IDs"**
3. **Application type**: **"Web application"**
4. **Name**: `ModernAction Auth0 Integration`

### 3.2 Configure Redirect URIs
**CRITICAL**: Add these exact redirect URIs:

**For Staging**:
```
https://dev-vvwd64m28nwqm871.us.auth0.com/login/callback
```

**For Production**:
```
https://dev-cp0hhr8rujq87exh.us.auth0.com/login/callback
```

**For Custom Domains** (when configured):
```
https://auth.staging.modernaction.io/login/callback
https://auth.modernaction.io/login/callback
```

### 3.3 Complete Creation
1. Click **"Create"**
2. **IMPORTANT**: Copy and securely store:
   - **Client ID**: `[GOOGLE_CLIENT_ID]`
   - **Client Secret**: `[GOOGLE_CLIENT_SECRET]`

## Step 4: Security Notes

### Best Practices Applied
- ✅ External user type (supports any Google account)
- ✅ Minimal required scopes (email, profile, openid only)
- ✅ Proper redirect URI configuration
- ✅ Company-owned Google account setup

### Post-Setup Security
- Regularly review OAuth consent screen settings
- Monitor usage in Google Cloud Console
- Update redirect URIs when custom domains are active
- Consider verified domain ownership for production

## Step 5: Auth0 Integration

### When Ready to Configure
Once you have the credentials, I'll configure them in Auth0 using:

```bash
# Update existing Google connection in staging
auth0 api patch "connections/con_w2bojAt6jjZ63uCp" --data '{
  "options": {
    "client_id": "[YOUR_GOOGLE_CLIENT_ID]",
    "client_secret": "[YOUR_GOOGLE_CLIENT_SECRET]",
    "email": true,
    "profile": true,
    "scope": ["email", "profile", "openid"]
  }
}'
```

## Next Steps

1. **Complete this setup** following the steps above
2. **Securely provide** the Client ID and Client Secret
3. **I'll configure** the Auth0 integration
4. **Test** the Google OAuth flow in staging
5. **Replicate** for production tenant when ready

---
**Security Reminder**: Keep the Client Secret secure and never commit it to version control.