{"taskDefinitionArn": "arn:aws:ecs:us-east-1:308755113449:task-definition/ModernActionstagingWebTaskDefinition6EB55C12:37", "containerDefinitions": [{"name": "WebContainer", "image": "308755113449.dkr.ecr.us-east-1.amazonaws.com/modernaction-web-staging:final-launch-candidate-v2", "cpu": 0, "links": [], "portMappings": [{"containerPort": 3000, "hostPort": 3000, "protocol": "tcp"}], "essential": true, "entryPoint": [], "command": [], "environment": [{"name": "NEXT_PUBLIC_AUTH0_DOMAIN", "value": "dev-vvwd64m28nwqm871.us.auth0.com"}, {"name": "AUTH0_CLIENT_ID", "value": "Jtv4r9uS9BSeahx5jp93v0Ur1K7b5ihC"}, {"name": "INTERNAL_API_URL", "value": "http://api.staging.local:8000/api/v1"}, {"name": "NEXT_PUBLIC_AUTH0_CLIENT_ID", "value": "Jtv4r9uS9BSeahx5jp93v0Ur1K7b5ihC"}, {"name": "NEXT_PUBLIC_AUTH0_AUDIENCE", "value": "https://api.modernaction.io"}, {"name": "NODE_ENV", "value": "production"}, {"name": "AUTH0_ISSUER_BASE_URL", "value": "https://dev-vvwd64m28nwqm871.us.auth0.com"}, {"name": "AUTH0_BASE_URL", "value": "https://staging.modernaction.io"}, {"name": "AUTH0_AUDIENCE", "value": "https://api.modernaction.io"}, {"name": "NEXT_PUBLIC_API_URL", "value": "https://staging.modernaction.io/api/v1"}], "environmentFiles": [], "mountPoints": [], "volumesFrom": [], "secrets": [{"name": "AUTH0_SECRET", "valueFrom": "arn:aws:secretsmanager:us-east-1:308755113449:secret:Auth0SecretE68A07FE-ViiC4OCIIUaY-CU5r0I:AUTH0_SECRET::"}, {"name": "AUTH0_CLIENT_SECRET", "valueFrom": "arn:aws:secretsmanager:us-east-1:308755113449:secret:Auth0SecretE68A07FE-ViiC4OCIIUaY-CU5r0I:AUTH0_CLIENT_SECRET::"}], "dnsServers": [], "dnsSearchDomains": [], "extraHosts": [], "dockerSecurityOptions": [], "dockerLabels": {}, "ulimits": [], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "modernaction-staging-WebTaskDefinitionWebContainerLogGroup9BD817D4-hP8WFAx75ym7", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "web"}, "secretOptions": []}, "systemControls": [], "credentialSpecs": []}], "family": "ModernActionstagingWebTaskDefinition6EB55C12", "taskRoleArn": "arn:aws:iam::308755113449:role/modernaction-staging-TaskRole30FC0FBB-JAAIfoNAHD2S", "executionRoleArn": "arn:aws:iam::308755113449:role/modernaction-staging-TaskExecutionRole250D2532-8gUEXnupxMlT", "networkMode": "awsvpc", "revision": 37, "volumes": [], "status": "ACTIVE", "requiresAttributes": [{"name": "com.amazonaws.ecs.capability.logging-driver.awslogs"}, {"name": "ecs.capability.execution-role-awslogs"}, {"name": "com.amazonaws.ecs.capability.ecr-auth"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.19"}, {"name": "ecs.capability.secrets.asm.environment-variables"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.17"}, {"name": "com.amazonaws.ecs.capability.task-iam-role"}, {"name": "ecs.capability.execution-role-ecr-pull"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.18"}, {"name": "ecs.capability.task-eni"}], "placementConstraints": [], "compatibilities": ["EC2", "FARGATE"], "requiresCompatibilities": ["FARGATE"], "cpu": "1024", "memory": "2048", "registeredAt": "2025-08-21T02:14:38.867000-04:00", "registeredBy": "arn:aws:sts::308755113449:assumed-role/cdk-hnb659fds-cfn-exec-role-308755113449-us-east-1/AWSCloudFormation"}